
# """
# Keyphrase Aggregator Module
# ===========================

# This module processes Excel files from the production pipeline output and aggregates
# unique predicted keyphrases for each TAN_NAME with segment priority.

# Priority order: title > abstract > results > conclusion > other segments

# Output: Excel file with columns: TAN_NAME, segment, pdf_file, unique_predicted_keyphrases
# """

# import os
# import pandas as pd
# import ast
# from typing import List, Dict, Set, Tuple
# import glob
# from collections import defaultdict, OrderedDict


# class KeyphraseAggregator:
#     """Aggregates predicted keyphrases from production pipeline output"""
    
#     def __init__(self):
#         # Define segment priority order
#         self.segment_priority = [
#             'title',
#             'abstract', 
#             'results',
#             'conclusion'
#         ]
    
#     def parse_predicted_keyphrases(self, keyphrases_str: str) -> List[str]:
#         """Parse predicted keyphrases string to list with robust handling"""
#         if pd.isna(keyphrases_str) or not keyphrases_str:
#             return []

#         try:
#             # Handle different input types
#             if isinstance(keyphrases_str, str):
#                 # Remove extra whitespace
#                 keyphrases_str = keyphrases_str.strip()

#                 if not keyphrases_str or keyphrases_str.lower() in ['nan', 'none', 'null', '[]']:
#                     return []

#                 # Handle string representation of list
#                 if keyphrases_str.startswith('[') and keyphrases_str.endswith(']'):
#                     try:
#                         # Parse as literal list
#                         keyphrases = ast.literal_eval(keyphrases_str)
#                     except (ValueError, SyntaxError):
#                         # If literal_eval fails, try manual parsing
#                         content = keyphrases_str[1:-1]  # Remove brackets
#                         if content.strip():
#                             # Split by comma and clean quotes
#                             keyphrases = [item.strip().strip("'\"") for item in content.split(',')]
#                         else:
#                             keyphrases = []
#                 else:
#                     # Split by comma if it's a comma-separated string
#                     keyphrases = [kp.strip() for kp in keyphrases_str.split(',')]

#             elif isinstance(keyphrases_str, list):
#                 keyphrases = keyphrases_str
#             else:
#                 return []

#             # Clean and filter keyphrases
#             cleaned_keyphrases = []
#             for kp in keyphrases:
#                 if isinstance(kp, str) and kp.strip():
#                     cleaned_kp = kp.strip().lower()
#                     # Filter out very short terms and common noise
#                     if (len(cleaned_kp) > 2 and
#                         cleaned_kp not in ['nan', 'none', 'null', '', 'the', 'and', 'or']):
#                         cleaned_keyphrases.append(cleaned_kp)

#             return cleaned_keyphrases

#         except Exception as e:
#             print(f"Warning: Could not parse keyphrases '{str(keyphrases_str)[:100]}...': {e}")
#             return []
    
#     def get_segment_priority_score(self, segment: str) -> int:
#         """Get priority score for segment (lower is higher priority)"""
#         segment_lower = segment.lower()
        
#         for i, priority_segment in enumerate(self.segment_priority):
#             if priority_segment in segment_lower:
#                 return i
        
#         # Return high number for non-priority segments
#         return len(self.segment_priority) + 100
    
#     def aggregate_keyphrases_for_tan(self, tan_df: pd.DataFrame) -> Dict:
#         """Aggregate keyphrases for a single TAN with segment priority"""
        
#         # Group by segment and collect keyphrases
#         segment_keyphrases = defaultdict(set)
#         segment_info = {}
        
#         for _, row in tan_df.iterrows():
#             segment = str(row.get('segment', 'unknown')).strip()
#             pdf_file = str(row.get('pdf_file', 'unknown')).strip()
#             predicted_kp = row.get('predicted_keyphrases', '')
            
#             # Parse keyphrases
#             keyphrases = self.parse_predicted_keyphrases(predicted_kp)
            
#             # Add to segment collection
#             if keyphrases:
#                 segment_keyphrases[segment].update(keyphrases)
#                 segment_info[segment] = pdf_file
        
#         # Sort segments by priority
#         sorted_segments = sorted(
#             segment_keyphrases.keys(),
#             key=self.get_segment_priority_score
#         )
        
#         # Collect unique keyphrases in priority order
#         all_unique_keyphrases = OrderedDict()
        
#         for segment in sorted_segments:
#             keyphrases = segment_keyphrases[segment]
#             for kp in sorted(keyphrases):  # Sort alphabetically within segment
#                 if kp not in all_unique_keyphrases:
#                     all_unique_keyphrases[kp] = segment
        
#         # Get the most representative info
#         tan_name = tan_df['TAN_NAME'].iloc[0] if 'TAN_NAME' in tan_df.columns else 'unknown'
        
#         # Get primary segment and pdf_file (from highest priority segment with keyphrases)
#         primary_segment = sorted_segments[0] if sorted_segments else 'unknown'
#         primary_pdf_file = segment_info.get(primary_segment, 'unknown')
        
#         return {
#             'TAN_NAME': tan_name,
#             'segment': primary_segment,
#             'pdf_file': primary_pdf_file,
#             'unique_predicted_keyphrases': list(all_unique_keyphrases.keys()),
#             'segment_breakdown': dict(all_unique_keyphrases),
#             'total_unique_count': len(all_unique_keyphrases)
#         }
    
#     def process_excel_file(self, excel_file: str) -> Dict:
#         """Process a single Excel file and return aggregated keyphrases"""
        
#         try:
#             print(f"Processing: {os.path.basename(excel_file)}")
            
#             # Read Excel file
#             df = pd.read_excel(excel_file)
            
#             if df.empty:
#                 print(f"  Warning: Empty file {excel_file}")
#                 return None
            
#             # Check required columns
#             required_cols = ['TAN_NAME']
#             missing_cols = [col for col in required_cols if col not in df.columns]
#             if missing_cols:
#                 print(f"  Warning: Missing columns {missing_cols} in {excel_file}")
#                 return None
            
#             # Get TAN_NAME (should be same for all rows in file)
#             tan_name = df['TAN_NAME'].iloc[0]
            
#             # Aggregate keyphrases
#             result = self.aggregate_keyphrases_for_tan(df)
            
#             print(f"  TAN: {tan_name}, Unique keyphrases: {result['total_unique_count']}")
            
#             return result
            
#         except Exception as e:
#             print(f"  Error processing {excel_file}: {e}")
#             return None
    
#     def process_folder(self, folder_path: str, output_file: str = None) -> pd.DataFrame:
#         """Process all Excel files in folder and create aggregated output"""

#         print("="*80)
#         print("KEYPHRASE AGGREGATION FROM PRODUCTION OUTPUT")
#         print("="*80)
#         print(f"Input folder: {folder_path}")

#         # Find all Excel files
#         excel_pattern = os.path.join(folder_path, "*.xlsx")
#         excel_files = glob.glob(excel_pattern)

#         if not excel_files:
#             print(f"No Excel files found in {folder_path}")
#             return pd.DataFrame()

#         print(f"Found {len(excel_files)} Excel files to process")
#         print()

#         # Process each file
#         aggregated_results = []

#         for excel_file in excel_files:
#             result = self.process_excel_file(excel_file)
#             if result:
#                 aggregated_results.append(result)

#         if not aggregated_results:
#             print("No valid results found")
#             return pd.DataFrame()

#         # Create output DataFrame
#         output_data = []
#         for result in aggregated_results:
#             output_data.append({
#                 'TAN_NAME': result['TAN_NAME'],
#                 'segment': result['segment'],
#                 'pdf_file': result['pdf_file'],
#                 'unique_predicted_keyphrases': result['unique_predicted_keyphrases'],
#                 'total_unique_count': result['total_unique_count']
#             })

#         # Create DataFrame
#         output_df = pd.DataFrame(output_data)

#         # Sort by TAN_NAME
#         output_df = output_df.sort_values('TAN_NAME').reset_index(drop=True)

#         # Save to Excel if output file specified
#         if output_file:
#             # Create Excel writer for multiple sheets
#             with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
#                 # Main summary sheet
#                 output_df.to_excel(writer, sheet_name='Summary', index=False)

#                 # Detailed breakdown sheet
#                 detailed_data = []
#                 for result in aggregated_results:
#                     tan_name = result['TAN_NAME']
#                     segment_breakdown = result['segment_breakdown']

#                     for keyphrase, source_segment in segment_breakdown.items():
#                         detailed_data.append({
#                             'TAN_NAME': tan_name,
#                             'keyphrase': keyphrase,
#                             'source_segment': source_segment,
#                             'pdf_file': result['pdf_file']
#                         })

#                 detailed_df = pd.DataFrame(detailed_data)
#                 detailed_df.to_excel(writer, sheet_name='Detailed_Breakdown', index=False)

#             print(f"\nOutput saved to: {output_file}")
#             print("  - Summary sheet: Aggregated keyphrases per TAN")
#             print("  - Detailed_Breakdown sheet: Individual keyphrases with source segments")

#         # Print summary
#         print(f"\nSUMMARY:")
#         print(f"Total TANs processed: {len(output_df)}")
#         print(f"Total unique keyphrases across all TANs: {sum(output_df['total_unique_count'])}")
#         print(f"Average keyphrases per TAN: {output_df['total_unique_count'].mean():.1f}")

#         # Segment priority statistics
#         segment_stats = defaultdict(int)
#         for result in aggregated_results:
#             segment_stats[result['segment']] += 1

#         print(f"\nPrimary segments distribution:")
#         for segment in self.segment_priority:
#             if segment in segment_stats:
#                 print(f"  {segment}: {segment_stats[segment]} TANs")

#         other_segments = {k: v for k, v in segment_stats.items() if k not in self.segment_priority}
#         if other_segments:
#             print(f"  Other segments: {sum(other_segments.values())} TANs")

#         return output_df


# def create_analysis_report(result_df: pd.DataFrame, aggregated_results: List[Dict]) -> str:
#     """Create a comprehensive analysis report"""

#     report = []
#     report.append("="*80)
#     report.append("KEYPHRASE AGGREGATION ANALYSIS REPORT")
#     report.append("="*80)

#     # Basic statistics
#     total_tans = len(result_df)
#     total_keyphrases = sum(result_df['total_unique_count'])
#     avg_keyphrases = result_df['total_unique_count'].mean()

#     report.append(f"Total TANs processed: {total_tans}")
#     report.append(f"Total unique keyphrases: {total_keyphrases}")
#     report.append(f"Average keyphrases per TAN: {avg_keyphrases:.1f}")
#     report.append("")

#     # Distribution analysis
#     report.append("KEYPHRASE COUNT DISTRIBUTION:")
#     count_dist = result_df['total_unique_count'].value_counts().sort_index()
#     for count, freq in count_dist.head(10).items():
#         report.append(f"  {count} keyphrases: {freq} TANs")
#     report.append("")

#     # Top keyphrases across all TANs
#     all_keyphrases = defaultdict(int)
#     for result in aggregated_results:
#         for kp in result['unique_predicted_keyphrases']:
#             all_keyphrases[kp] += 1

#     report.append("TOP 20 MOST COMMON KEYPHRASES:")
#     top_keyphrases = sorted(all_keyphrases.items(), key=lambda x: x[1], reverse=True)
#     for i, (kp, count) in enumerate(top_keyphrases[:20], 1):
#         report.append(f"  {i:2d}. {kp} ({count} TANs)")
#     report.append("")

#     # Segment analysis
#     segment_stats = defaultdict(int)
#     for result in aggregated_results:
#         segment_stats[result['segment']] += 1

#     report.append("PRIMARY SEGMENT DISTRIBUTION:")
#     for segment, count in sorted(segment_stats.items(), key=lambda x: x[1], reverse=True):
#         report.append(f"  {segment}: {count} TANs ({count/total_tans*100:.1f}%)")

#     return "\n".join(report)


# def main():
#     """Main function to run keyphrase aggregation"""

#     # Configuration
#     input_folder = "production_section_52_shipment_982560"
#     output_file = "aggregated_keyphrases_section_52_shipment_982560.xlsx"

#     # Check if input folder exists
#     if not os.path.exists(input_folder):
#         print(f"Error: Input folder '{input_folder}' does not exist")
#         print("Please make sure you have run the production pipeline first")
#         return

#     # Create aggregator and process
#     aggregator = KeyphraseAggregator()
#     result_df = aggregator.process_folder(input_folder, output_file)

#     if not result_df.empty:
#         print("\nSample results:")
#         print(result_df[['TAN_NAME', 'segment', 'total_unique_count']].head())

#         # Show detailed breakdown for first few TANs
#         print("\nDetailed breakdown for first 3 TANs:")
#         for i, row in result_df.head(3).iterrows():
#             print(f"\nTAN: {row['TAN_NAME']}")
#             print(f"Primary segment: {row['segment']}")
#             print(f"PDF file: {row['pdf_file']}")
#             keyphrases_preview = row['unique_predicted_keyphrases'][:10]
#             remaining = len(row['unique_predicted_keyphrases']) - 10
#             print(f"Unique keyphrases ({row['total_unique_count']}): {keyphrases_preview}")
#             if remaining > 0:
#                 print(f"  ... and {remaining} more")

#         # Save analysis report
#         # Note: We need to get aggregated_results from the aggregator
#         # For now, create a simple report
#         print("\n" + "="*80)
#         print("ANALYSIS SUMMARY")
#         print("="*80)
#         print(f"Successfully processed {len(result_df)} TANs")
#         print(f"Total unique keyphrases: {sum(result_df['total_unique_count'])}")
#         print(f"Average keyphrases per TAN: {result_df['total_unique_count'].mean():.1f}")
#         print(f"Min keyphrases: {result_df['total_unique_count'].min()}")
#         print(f"Max keyphrases: {result_df['total_unique_count'].max()}")

#     else:
#         print("No results generated. Please check the input folder and files.")


# if __name__ == "__main__":
#     main()


##########################################################################################
"""
Keyphrase Aggregator Module
===========================

This module processes Excel files from the production pipeline output and aggregates
unique predicted keyphrases for each TAN_NAME with segment priority.

Priority order: title > abstract > results > conclusion > other segments

Output: Excel file with columns: TAN_NAME, segment, pdf_file, unique_predicted_keyphrases
"""

import os
import pandas as pd
import ast
import re
from typing import List, Dict, Set, Tuple
import glob
from collections import defaultdict, OrderedDict

class KeyphraseAggregator:
    """Aggregates predicted keyphrases from production pipeline output"""
    
    def __init__(self):
        # Define segment priority order
        self.segment_priority = [
            'title',
            'abstract',
            'results',
            'conclusion'
        ]
        try:
            from nltk.stem import PorterStemmer
            self.ps = PorterStemmer()
        except ImportError:
            self.ps = None
    
    def normalize_hyphens(self, text):
        """Replace different types of hyphens with standard hyphen"""
        return re.sub(r'[\u2010\u2011\u2012\u2013\u2014\u2015–—-]', '-', text)
    
    def contains_number(self, text):
        """Check if text contains any number"""
        return bool(re.search(r'\d', text))
    
    def is_singular_plural_pair(self, small, large):
        """Check if two strings form singular-plural pair"""
        if self.ps:
            stem_small = self.ps.stem(small)
            stem_large = self.ps.stem(large)
            if stem_small == stem_large:
                if small.endswith('s') and not large.endswith('s'):
                    return False  # small is plural, large is singular
                if large.endswith('s') and not small.endswith('s'):
                    return True  # large is plural, small is singular
        else:
            # Simple fallback without NLTK
            if small + 's' == large:
                return True
            if large + 's' == small:
                return False
        return False
    
    def is_word_subset(self, small, large):
        """Check if words of small string are subset of words of large string"""
        words_small = set(small.replace('-', ' ').split())
        words_large = set(large.replace('-', ' ').split())
        return words_small.issubset(words_large) and len(words_small) < len(words_large)
    
    def deduplicate_keyphrases(self, kp_list):
        """Apply deduplication rules to keyphrase list"""
        if not kp_list:
            return []
        
        # Step 1: Normalize hyphens but keep original mapping
        normalized_kps = [self.normalize_hyphens(kp.lower()) for kp in kp_list]
        
        norm_to_original = {}
        for kp in kp_list:
            norm = self.normalize_hyphens(kp.lower())
            # Prefer hyphenated versions
            if norm in norm_to_original:
                if '-' in kp and '-' not in norm_to_original[norm]:
                    norm_to_original[norm] = kp
            else:
                norm_to_original[norm] = kp
        
        # Step 2: Remove strings containing numbers
        filtered_kps = [kp for kp in normalized_kps if not self.contains_number(kp)]
        
        if not filtered_kps:
            return []
        
        # Step 3: Remove duplicates and apply rules
        # Sort by length descending to process larger phrases first
        filtered_kps = sorted(set(filtered_kps), key=lambda x: len(x), reverse=True)
        
        included = set()
        result = []
        
        for kp_current in filtered_kps:
            keep = True
            
            # Check against already included keyphrases
            for included_kp in included.copy():
                # If included keyphrase is word subset of current, remove it
                if self.is_word_subset(included_kp, kp_current):
                    included.remove(included_kp)
                    if included_kp in result:
                        result.remove(included_kp)
                
                # If current is word subset of included keyphrase, don't add current
                elif self.is_word_subset(kp_current, included_kp):
                    keep = False
                    break
                
                # Check singular/plural pairs
                elif self.is_singular_plural_pair(kp_current, included_kp):
                    # Current is singular, included is plural - don't add current
                    keep = False
                    break
                elif self.is_singular_plural_pair(included_kp, kp_current):
                    # Included is singular, current is plural - remove included, add current
                    included.remove(included_kp)
                    if included_kp in result:
                        result.remove(included_kp)
            
            if keep and kp_current not in included:
                included.add(kp_current)
                result.append(kp_current)
        
        # Step 4: Convert back to original format and sort alphabetically
        result_original = [norm_to_original[kp] for kp in result]
        return sorted(result_original, key=lambda x: x.lower())

    def parse_predicted_keyphrases(self, keyphrases_str: str) -> List[str]:
        """Parse predicted keyphrases string to list with robust handling"""
        if pd.isna(keyphrases_str) or not keyphrases_str:
            return []
        
        try:
            # Handle different input types
            if isinstance(keyphrases_str, str):
                # Remove extra whitespace
                keyphrases_str = keyphrases_str.strip()
                if not keyphrases_str or keyphrases_str.lower() in ['nan', 'none', 'null', '[]']:
                    return []
                
                # Handle string representation of list
                if keyphrases_str.startswith('[') and keyphrases_str.endswith(']'):
                    try:
                        # Parse as literal list
                        keyphrases = ast.literal_eval(keyphrases_str)
                    except (ValueError, SyntaxError):
                        # If literal_eval fails, try manual parsing
                        content = keyphrases_str[1:-1] # Remove brackets
                        if content.strip():
                            # Split by comma and clean quotes
                            keyphrases = [item.strip().strip("'\"") for item in content.split(',')]
                        else:
                            keyphrases = []
                else:
                    # Split by comma if it's a comma-separated string
                    keyphrases = [kp.strip() for kp in keyphrases_str.split(',')]
                    
            elif isinstance(keyphrases_str, list):
                keyphrases = keyphrases_str
            else:
                return []
            
            # Clean and filter keyphrases - preserve original case
            cleaned_keyphrases = []
            for kp in keyphrases:
                if isinstance(kp, str) and kp.strip():
                    cleaned_kp = kp.strip()
                    # Filter out very short terms and common noise
                    if (len(cleaned_kp) > 2 and 
                        cleaned_kp.lower() not in ['nan', 'none', 'null', '', 'the', 'and', 'or']):
                        cleaned_keyphrases.append(cleaned_kp)
            
            return cleaned_keyphrases
            
        except Exception as e:
            print(f"Warning: Could not parse keyphrases '{str(keyphrases_str)[:100]}...': {e}")
            return []

    def get_segment_priority_score(self, segment: str) -> int:
        """Get priority score for segment (lower is higher priority)"""
        segment_lower = segment.lower()
        for i, priority_segment in enumerate(self.segment_priority):
            if priority_segment in segment_lower:
                return i
        # Return high number for non-priority segments
        return len(self.segment_priority) + 100

    def aggregate_keyphrases_for_tan(self, tan_df: pd.DataFrame) -> Dict:
        """Aggregate keyphrases for a single TAN with segment priority"""
        
        # Group by segment and collect keyphrases
        segment_keyphrases = defaultdict(set)
        segment_info = {}
        
        for _, row in tan_df.iterrows():
            segment = str(row.get('segment', 'unknown')).strip()
            pdf_file = str(row.get('pdf_file', 'unknown')).strip()
            predicted_kp = row.get('predicted_keyphrases', '')
            
            # Parse keyphrases
            keyphrases = self.parse_predicted_keyphrases(predicted_kp)
            
            # Add to segment collection
            if keyphrases:
                segment_keyphrases[segment].update(keyphrases)
                segment_info[segment] = pdf_file
        
        # Sort segments by priority
        sorted_segments = sorted(
            segment_keyphrases.keys(),
            key=self.get_segment_priority_score
        )
        
        # Collect unique keyphrases in priority order
        all_unique_keyphrases = OrderedDict()
        for segment in sorted_segments:
            keyphrases = segment_keyphrases[segment]
            for kp in sorted(keyphrases): # Sort alphabetically within segment
                if kp not in all_unique_keyphrases:
                    all_unique_keyphrases[kp] = segment
        
        # Apply deduplication to the final keyphrase list
        final_keyphrases = self.deduplicate_keyphrases(list(all_unique_keyphrases.keys()))
        
        # Get the most representative info
        tan_name = tan_df['TAN_NAME'].iloc[0] if 'TAN_NAME' in tan_df.columns else 'unknown'
        
        # Get primary segment and pdf_file (from highest priority segment with keyphrases)
        primary_segment = sorted_segments[0] if sorted_segments else 'unknown'
        primary_pdf_file = segment_info.get(primary_segment, 'unknown')
        
        return {
            'TAN_NAME': tan_name,
            'segment': primary_segment,
            'pdf_file': primary_pdf_file,
            'unique_predicted_keyphrases': final_keyphrases,  # Now uses deduplication
            'segment_breakdown': dict(all_unique_keyphrases),
            'total_unique_count': len(final_keyphrases)
        }

    def process_excel_file(self, excel_file: str) -> Dict:
        """Process a single Excel file and return aggregated keyphrases"""
        try:
            print(f"Processing: {os.path.basename(excel_file)}")
            
            # Read Excel file
            df = pd.read_excel(excel_file)
            
            if df.empty:
                print(f"  Warning: Empty file {excel_file}")
                return None
            
            # Check required columns
            required_cols = ['TAN_NAME']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"  Warning: Missing columns {missing_cols} in {excel_file}")
                return None
            
            # Get TAN_NAME (should be same for all rows in file)
            tan_name = df['TAN_NAME'].iloc[0]
            
            # Aggregate keyphrases
            result = self.aggregate_keyphrases_for_tan(df)
            print(f"  TAN: {tan_name}, Unique keyphrases: {result['total_unique_count']}")
            
            return result
            
        except Exception as e:
            print(f"  Error processing {excel_file}: {e}")
            return None

    def process_folder(self, folder_path: str, output_file: str = None) -> pd.DataFrame:
        """Process all Excel files in folder and create aggregated output"""
        
        print("="*80)
        print("KEYPHRASE AGGREGATION FROM PRODUCTION OUTPUT")
        print("="*80)
        print(f"Input folder: {folder_path}")
        
        # Find all Excel files
        excel_pattern = os.path.join(folder_path, "*.xlsx")
        excel_files = glob.glob(excel_pattern)
        
        if not excel_files:
            print(f"No Excel files found in {folder_path}")
            return pd.DataFrame()
        
        print(f"Found {len(excel_files)} Excel files to process")
        print()
        
        # Process each file
        aggregated_results = []
        for excel_file in excel_files:
            result = self.process_excel_file(excel_file)
            if result:
                aggregated_results.append(result)
        
        if not aggregated_results:
            print("No valid results found")
            return pd.DataFrame()
        
        # Create output DataFrame
        output_data = []
        for result in aggregated_results:
            output_data.append({
                'TAN_NAME': result['TAN_NAME'],
                'segment': result['segment'],
                'pdf_file': result['pdf_file'],
                'unique_predicted_keyphrases': result['unique_predicted_keyphrases'],
                'total_unique_count': result['total_unique_count']
            })
        
        # Create DataFrame
        output_df = pd.DataFrame(output_data)
        
        # Sort by TAN_NAME
        output_df = output_df.sort_values('TAN_NAME').reset_index(drop=True)
        
        # Save to Excel if output file specified
        if output_file:
            # Create Excel writer for multiple sheets
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # Main summary sheet
                output_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Detailed breakdown sheet
                detailed_data = []
                for result in aggregated_results:
                    tan_name = result['TAN_NAME']
                    segment_breakdown = result['segment_breakdown']
                    for keyphrase, source_segment in segment_breakdown.items():
                        detailed_data.append({
                            'TAN_NAME': tan_name,
                            'keyphrase': keyphrase,
                            'source_segment': source_segment,
                            'pdf_file': result['pdf_file']
                        })
                
                detailed_df = pd.DataFrame(detailed_data)
                detailed_df.to_excel(writer, sheet_name='Detailed_Breakdown', index=False)
            
            print(f"\nOutput saved to: {output_file}")
            print("  - Summary sheet: Aggregated keyphrases per TAN")
            print("  - Detailed_Breakdown sheet: Individual keyphrases with source segments")
        
        # Print summary
        print(f"\nSUMMARY:")
        print(f"Total TANs processed: {len(output_df)}")
        print(f"Total unique keyphrases across all TANs: {sum(output_df['total_unique_count'])}")
        print(f"Average keyphrases per TAN: {output_df['total_unique_count'].mean():.1f}")
        
        # Segment priority statistics
        segment_stats = defaultdict(int)
        for result in aggregated_results:
            segment_stats[result['segment']] += 1
        
        print(f"\nPrimary segments distribution:")
        for segment in self.segment_priority:
            if segment in segment_stats:
                print(f"  {segment}: {segment_stats[segment]} TANs")
        
        other_segments = {k: v for k, v in segment_stats.items() if k not in self.segment_priority}
        if other_segments:
            print(f"  Other segments: {sum(other_segments.values())} TANs")
        
        return output_df

def create_analysis_report(result_df: pd.DataFrame, aggregated_results: List[Dict]) -> str:
    """Create a comprehensive analysis report"""
    report = []
    report.append("="*80)
    report.append("KEYPHRASE AGGREGATION ANALYSIS REPORT")
    report.append("="*80)
    
    # Basic statistics
    total_tans = len(result_df)
    total_keyphrases = sum(result_df['total_unique_count'])
    avg_keyphrases = result_df['total_unique_count'].mean()
    
    report.append(f"Total TANs processed: {total_tans}")
    report.append(f"Total unique keyphrases: {total_keyphrases}")
    report.append(f"Average keyphrases per TAN: {avg_keyphrases:.1f}")
    report.append("")
    
    # Distribution analysis
    report.append("KEYPHRASE COUNT DISTRIBUTION:")
    count_dist = result_df['total_unique_count'].value_counts().sort_index()
    for count, freq in count_dist.head(10).items():
        report.append(f"  {count} keyphrases: {freq} TANs")
    report.append("")
    
    # Top keyphrases across all TANs
    all_keyphrases = defaultdict(int)
    for result in aggregated_results:
        for kp in result['unique_predicted_keyphrases']:
            all_keyphrases[kp] += 1
    
    report.append("TOP 20 MOST COMMON KEYPHRASES:")
    top_keyphrases = sorted(all_keyphrases.items(), key=lambda x: x[1], reverse=True)
    for i, (kp, count) in enumerate(top_keyphrases[:20], 1):
        report.append(f"  {i:2d}. {kp} ({count} TANs)")
    report.append("")
    
    # Segment analysis
    segment_stats = defaultdict(int)
    for result in aggregated_results:
        segment_stats[result['segment']] += 1
    
    report.append("PRIMARY SEGMENT DISTRIBUTION:")
    for segment, count in sorted(segment_stats.items(), key=lambda x: x[1], reverse=True):
        report.append(f"  {segment}: {count} TANs ({count/total_tans*100:.1f}%)")
    
    return "\n".join(report)

def main():
    """Main function to run keyphrase aggregation"""
    # Configuration
    input_folder = "production_section_52_shipment_982560"
    output_file = "aggregated_keyphrases_section_52_shipment_982560.xlsx"
    
    # Check if input folder exists
    if not os.path.exists(input_folder):
        print(f"Error: Input folder '{input_folder}' does not exist")
        print("Please make sure you have run the production pipeline first")
        return
    
    # Create aggregator and process
    aggregator = KeyphraseAggregator()
    result_df = aggregator.process_folder(input_folder, output_file)
    
    if not result_df.empty:
        print("\nSample results:")
        print(result_df[['TAN_NAME', 'segment', 'total_unique_count']].head())
        
        # Show detailed breakdown for first few TANs
        print("\nDetailed breakdown for first 3 TANs:")
        for i, row in result_df.head(3).iterrows():
            print(f"\nTAN: {row['TAN_NAME']}")
            print(f"Primary segment: {row['segment']}")
            print(f"PDF file: {row['pdf_file']}")
            keyphrases_preview = row['unique_predicted_keyphrases'][:10]
            remaining = len(row['unique_predicted_keyphrases']) - 10
            print(f"Unique keyphrases ({row['total_unique_count']}): {keyphrases_preview}")
            if remaining > 0:
                print(f"  ... and {remaining} more")
        
        # Save analysis report
        print("\n" + "="*80)
        print("ANALYSIS SUMMARY")
        print("="*80)
        print(f"Successfully processed {len(result_df)} TANs")
        print(f"Total unique keyphrases: {sum(result_df['total_unique_count'])}")
        print(f"Average keyphrases per TAN: {result_df['total_unique_count'].mean():.1f}")
        print(f"Min keyphrases: {result_df['total_unique_count'].min()}")
        print(f"Max keyphrases: {result_df['total_unique_count'].max()}")
    
    else:
        print("No results generated. Please check the input folder and files.")

if __name__ == "__main__":
    main()
