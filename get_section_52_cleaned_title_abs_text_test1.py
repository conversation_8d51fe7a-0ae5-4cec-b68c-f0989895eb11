###get_section_52_cleaned_title_abs_text1.py

import os
from pathlib import Path
import json
import pandas as pd
from tqdm import tqdm

## ignore FutureWarning
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)

from get_title_abs import get_title_abstract_from_url
from get_clean_title_abs import clean_title_abs_text





def get_pdf_files_for_tan_batch(df: pd.DataFrame, root_folder: str) -> pd.DataFrame:
    """
    HIGHLY OPTIMIZED: Find PDF files efficiently using direct folder mapping
    
    Logic:
    - TAN_NAME = "09509282J"
    - subfolder_name = "09509282J" 
    - PDF file = "09509282J.article.001.pdf"
    """
    if not os.path.exists(root_folder):
        # print(f"ERROR: Root folder does not exist: {root_folder}")
        df = df.copy()
        df["pdf_path"] = [None] * len(df)
        return df

    # print(f" Optimized PDF scanning: {root_folder}")
    
    # Step 1: Get unique TAN_NAMEs from dataframe for fast lookup
    tan_names_set = set(df["TAN_NAME"].str.strip())
    tan_names_lower = {tan.lower(): tan for tan in tan_names_set}  # Case-insensitive mapping
    
    # print(f" Looking for {len(tan_names_set)} unique TANs")
    
    # Step 2: Create mapping dictionary
    tan_pdf_map = {}
    folders_scanned = 0
    folders_matched = 0
    
    try:
        # Step 3: Single walk through directory structure
        for subdir, _, files in os.walk(root_folder):
            subfolder_name = Path(subdir).name.strip()
            folders_scanned += 1
            
            # Step 4: Check if subfolder matches any TAN_NAME (exact or case-insensitive)
            matching_tan = None
            if subfolder_name in tan_names_set:
                matching_tan = subfolder_name
            elif subfolder_name.lower() in tan_names_lower:
                matching_tan = tan_names_lower[subfolder_name.lower()]
            
            if matching_tan:
                folders_matched += 1
                
                # Step 5: Look for PDF file with pattern {TAN_NAME}.article.*.pdf
                for file in files:
                    if file.lower().endswith(".pdf") and ".article." in file.lower():
                        # Check if filename starts with TAN_NAME.article.
                        expected_pattern = f"{matching_tan}.article."
                        expected_pattern_lower = expected_pattern.lower()
                        
                        if file.lower().startswith(expected_pattern_lower):
                            tan_pdf_map[matching_tan] = os.path.join(subdir, file)
                            # print(f"   {matching_tan} → {file}")
                            break  # Found the PDF for this TAN, move to next folder
                
                # If no PDF found in this TAN folder, log it
                if matching_tan not in tan_pdf_map:
                    # print(f"   {matching_tan} → No PDF found in folder")
                    pass
                    
        # print(f" Scanned {folders_scanned} folders, matched {folders_matched} TAN folders")
        # print(f" Found PDF files for {len(tan_pdf_map)} out of {len(tan_names_set)} TANs")
        
    except Exception as e:
        # print(f"ERROR: Could not access root folder: {e}")
        pass
    
    # Step 6: Map PDF paths back to DataFrame
    df = df.copy()
    df["pdf_path"] = df["TAN_NAME"].map(tan_pdf_map)
    
    # Summary statistics
    # found_count = df['pdf_path'].notna().sum()
    # missing_count = df['pdf_path'].isna().sum()
    
    # print(f" Final mapping: {found_count} found, {missing_count} missing")
    
    return df



def load_and_prepare_complete_dataset(section_code, curator_data_folder, pdf_folder, abbr_full_form_excel):
    """
    FIXED: Load all data once and prepare complete dataset for processing
    
    Args:
        section_code (str): Section code to filter by
        curator_data_folder (str): Path to curator data folder
        pdf_folder (str): Path to PDF folder
        abbr_full_form_excel (str): Path to abbreviations Excel file
    
    Returns:
        pd.DataFrame: Complete prepared dataset with all necessary columns
    """
    # print("\n" + "="*80)
    # print(" LOADING AND PREPARING COMPLETE DATASET")
    # print("="*80)
    
    # Step 1: Load all curator Excel files once
    # print(f" Reading all Excel files from {curator_data_folder}")
    all_data = []
    
    # curator_files = [f for f in os.listdir(curator_data_folder) if f.lower().endswith((".xlsx", ".xls"))]
    curator_files = [
    f for f in os.listdir(curator_data_folder)
    if f.lower().endswith((".xlsx", ".xls"))
    and "~" not in f ]

    # print(f" Found {len(curator_files)} Excel files to process")
    
    for file in curator_files:
        file_path = os.path.join(curator_data_folder, file)
        try:
            df = pd.read_excel(file_path, dtype=str)
            # df = df.head(100)
            # print(f"   {file}: columns = {list(df.columns)}")
            all_data.append(df)
            # print(f"   Loaded: {file} ({len(df)} rows)")
        except Exception as e:
            print(f"   Error reading {file}: {e}")

    if not all_data:
        raise ValueError("No valid Excel files found in the folder.")

    # Step 2: Combine and filter all data
    df_all = pd.concat(all_data, ignore_index=True)
    # print(f" Combined dataset: {len(df_all)} total rows")
    # print(f" Combined columns: {list(df_all.columns)}")
    
    # Clean & filter
    df_all = df_all.fillna("").astype(str)
    
    # Check if required columns exist - PRODUCTION VERSION (no CTH requirement)
    required_columns = ["TAN_NAME", "SECTION_CODE"]
    missing_columns = [col for col in required_columns if col not in df_all.columns]
    if missing_columns:
        # print(f" Missing required columns: {missing_columns}")
        # print(f"Available columns: {list(df_all.columns)}")
        raise ValueError(f"Missing required columns: {missing_columns}")

    # PRODUCTION: Filter only by section code, no keyphrase requirement
    df_filtered = df_all[df_all["SECTION_CODE"].str.strip() == section_code]
    # print(f" After filtering (section={section_code}, non-empty CTH): {len(df_filtered)} rows")
    # print(f" Filtered columns: {list(df_filtered.columns)}")

    if len(df_filtered) == 0:
        # print(" No data found after filtering. Check your section_code and data.")
        # print(f"Available section codes: {df_all['SECTION_CODE'].unique()}")
        raise ValueError("No data available after filtering")

    #  Step 3: FIXED - Group and aggregate by TAN_NAME
    # print(f" Grouping data by TAN_NAME...")
    

    def aggregate_group_fixed(group):
        """
        FIXED: Properly handle TAN_NAME during aggregation
        """
        # Get TAN_NAME from the group (it should be available in the group data)
        tan_name = group["TAN_NAME"].iloc[0] if "TAN_NAME" in group.columns else group.name
        
        # Safely get other fields
        shipment_name = group["SHIPMENT_NAME"].iloc[0] if "SHIPMENT_NAME" in group.columns else ""
        section_code = group["SECTION_CODE"].iloc[0] if "SECTION_CODE" in group.columns else ""
        subsection_name = group["SUBSECTION_NAME"].iloc[0] if "SUBSECTION_NAME" in group.columns else ""
        
        # PRODUCTION: No keyphrase extraction, just basic metadata
        return pd.Series({
            "TAN_NAME": tan_name,
            "SHIPMENT_NAME": shipment_name,
            "SECTION_CODE": section_code,
            "SUBSECTION_NAME": subsection_name
        })

    #  FIXED: Use transform approach to preserve TAN_NAME
    try:
        # print(f" Starting aggregation by TAN_NAME...")
        grouped = df_filtered.groupby("TAN_NAME", as_index=False)
        result_df = grouped.apply(aggregate_group_fixed, include_groups=False)
        result_df = result_df.reset_index(drop=True)
        
        # Ensure TAN_NAME column exists
        if 'TAN_NAME' not in result_df.columns:
            # print(" TAN_NAME missing after groupby, attempting to recover...")
            # PRODUCTION: Alternative approach using agg (no keyphrase columns)
            agg_dict = {
                'SHIPMENT_NAME': 'first',
                'SECTION_CODE': 'first',
                'SUBSECTION_NAME': 'first'
            }

            result_df = df_filtered.groupby("TAN_NAME").agg(agg_dict).reset_index()
        
        # unique_tan_count = len(result_df)
        # print(f" Aggregated into {unique_tan_count} unique TANs")
        # print(f" Result columns after aggregation: {list(result_df.columns)}")
        
    except Exception as e:
        # print(f" Error during grouping: {e}")
        # print(f" df_filtered shape: {df_filtered.shape}")
        # print(f" df_filtered columns: {list(df_filtered.columns)}")
        # print(f" Sample TAN_NAMEs: {df_filtered['TAN_NAME'].head().tolist()}")
        # raise
        pass

    # Step 4: Process title/abstract for all TANs at once
    # print(f" Processing title/abstract data for all TANs...")
    try:
        result_df = separate_title_abs_text(result_df, get_title_abstract_from_url)
        # print(f" Title/abstract processing completed")
    except Exception as e:
        # print(f" Error in title/abstract processing: {e}")
        # Add empty columns if processing fails
        result_df['title'] = ''
        result_df['abstract'] = ''

    # Step 5: Clean title/abstract text
    # print(f" Cleaning title/abstract text...")
    try:
        result_df = clean_title_abs_text(result_df, abbr_full_form_excel)
        # print(f" Text cleaning completed")
    except Exception as e:
        # print(f" Error in text cleaning: {e}")
        pass

    # Step 6: Find PDF files for all TANs at once
    # print(f" Finding PDF files for all TANs...")
    result_df = get_pdf_files_for_tan_batch(result_df, pdf_folder)
    
    # Count PDFs found
    # pdf_found_count = result_df['pdf_path'].notna().sum()
    # print(f" PDF files found: {pdf_found_count} out of {len(result_df)} TANs")

    # Step 7: Add additional processing columns
    # print(f" Adding processing columns...")
    result_df['RESEARCH_ARTICLE'] = result_df['SUBSECTION_NAME'].apply(lambda x: 0 if str(x).lower() == "reviews" else 1)
    result_df['processing_status'] = 'ready_for_extraction'
    # result_df['dataset_prepared_at'] = pd.Timestamp.now()

    # Step 8: Reorder columns for consistency - PRODUCTION VERSION
    column_order = [
        'SHIPMENT_NAME', 'TAN_NAME', 'SECTION_CODE', 'SUBSECTION_NAME', 'RESEARCH_ARTICLE',
        'title', 'abstract', 'pdf_path', 'processing_status'
    ]
    
    # Add any additional columns that might exist
    additional_cols = [col for col in result_df.columns if col not in column_order]
    final_column_order = column_order + additional_cols
    
    # Only reindex with columns that actually exist
    existing_columns = [col for col in final_column_order if col in result_df.columns]
    result_df = result_df.reindex(columns=existing_columns)
    
    # print(f"\n DATASET PREPARATION COMPLETED")
    # print("="*80)
    # print(f" Total TANs prepared: {len(result_df)}")
    # print(f" TANs with PDFs: {pdf_found_count}")
    # print(f" Research articles: {result_df['RESEARCH_ARTICLE'].sum()}")
    # print(f" Review articles: {len(result_df) - result_df['RESEARCH_ARTICLE'].sum()}")
    # print(f"  Columns prepared: {len(result_df.columns)}")
    # print(f" Final columns: {list(result_df.columns)}")

    return result_df


def separate_title_abs_text(df, get_text_func):
    """
    Iterates over DataFrame rows, uses get_text_func to retrieve text for SHIPMENT_NAME and TAN_NAME,
    and stores values in new columns: 'title', 'abstract', and 'title_abs_text'.
    """
    titles, abstracts, texts = [], [], []

    for idx, row in tqdm(df.iterrows(), desc="Processing title/abstract", total=len(df)):
        shipment_name = row["SHIPMENT_NAME"]
        tan_name = row["TAN_NAME"]

        try:
            text_output = get_text_func(shipment_name, tan_name)

            # Parse JSON string to dict if needed
            if isinstance(text_output, str):
                try:
                    text_dict = json.loads(text_output)
                except json.JSONDecodeError:
                    text_dict = {}
            elif isinstance(text_output, dict):
                text_dict = text_output
            else:
                text_dict = {}

            title = text_dict.get("title", "").strip()
            abstract = text_dict.get("abstract", "").strip()

            titles.append(title)
            abstracts.append(abstract)
            texts.append(f"{title} {abstract}".strip())

        except Exception as e:
            # print(f"Error fetching text for SHIPMENT_NAME={shipment_name}, TAN_NAME={tan_name}: {e}")
            titles.append("")
            abstracts.append("")
            texts.append("")

    df["title"] = titles
    df["abstract"] = abstracts
    # df["title_abs_text"] = texts

    return df

#  BACKWARD COMPATIBILITY FUNCTIONS
def get_clean_title_abs_text_dataset(section_code, curator_data_folder, pdf_folder, abbr_full_form_excel):
    """
    LEGACY COMPATIBILITY: Redirects to optimized function
    """
    # print("  Using legacy function - redirecting to optimized dataset preparation")
    return load_and_prepare_complete_dataset(section_code, curator_data_folder, pdf_folder, abbr_full_form_excel)

def get_all_tan_names(section_code, curator_data_folder):
    """
    OPTIMIZED: Get list of all TAN_NAMEs efficiently
    """
    try:
        # Use the optimized loader but just return TAN names
        temp_df = load_and_prepare_complete_dataset(section_code, curator_data_folder, "", "")
        tan_list = temp_df["TAN_NAME"].tolist()
        # print(f" Retrieved {len(tan_list)} TAN names efficiently")
        return tan_list
    except Exception as e:
        # print(f" Error getting TAN list: {e}")
        return []



if __name__ == "__main__":
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\curated_data_sample"
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\utility_data\text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982600"

    # Test the optimized dataset preparation

    complete_dataset = load_and_prepare_complete_dataset(
        section_code, curator_data_folder, pdf_folder, abbr_full_form_excel
    )
    
    print(f"\n Dataset ready for processing!")
    print(f" Shape: {complete_dataset.shape}")
    print(f"  Columns: {list(complete_dataset.columns)}")
    
    # Save the prepared dataset
    output_file = f"prepared_dataset_section_{section_code}.xlsx"
    complete_dataset.to_excel(output_file, index=False)
    print(f" Prepared dataset saved to: {output_file}")
