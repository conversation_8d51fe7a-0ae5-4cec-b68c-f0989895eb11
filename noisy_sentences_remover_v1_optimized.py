"""
Optimized version of noisy_sentences_remover_v1.py with batch processing, caching, and singleton pattern
"""

import re
import spacy
import numpy as np
from sentence_transformers import SentenceTransformer, util
from typing import List, Dict, Tuple, Optional, Set
from functools import lru_cache
import concurrent.futures
from threading import Lock
import time

class ModelSingleton:
    """Singleton for heavy ML models to avoid repeated loading"""
    _instance = None
    _initialized = False
    _lock = Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ModelSingleton, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    print("Loading models (one-time initialization)...")
                    self.nlp = spacy.load("en_core_web_sm")
                    self.embedder = SentenceTransformer(
                        r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_2025\models\paraphrase-MiniLM-L6-v2",
                        device='cpu'
                    )
                    self._initialized = True
                    print("Models loaded successfully!")

class OptimizedNoisySentenceRemover:
    """Highly optimized version of NoisySentenceRemover with batch processing and caching"""
    
    def __init__(self, keyphrases=None,
                similarity_threshold=0.25,
                keyphrase_sim_threshold=0.7,
                device='cpu'):
        
        # Use singleton for models
        self.models = ModelSingleton()
        
        self.keyphrases = [kp.lower() for kp in (keyphrases or [])]
        self.similarity_threshold = similarity_threshold
        self.keyphrase_sim_threshold = keyphrase_sim_threshold
        
        # Pre-compile regex patterns
        self.patterns = {
            'ref_pattern': re.compile(r"\[\s*\d+([,\-\s]*)\]$"),
            'para_split': re.compile(r"\|\|"),
            'sent_split': re.compile(r"\|"),
        }
        
        # Batch compute keyphrase embeddings
        if self.keyphrases:
            self.keyphrase_embeddings = self._batch_encode_keyphrases()
        else:
            self.keyphrase_embeddings = None
        
        # Caching
        self._embedding_cache = {}
        self._similarity_cache = {}
        self._cache_lock = Lock()
        self.cache_hits = 0
        self.cache_misses = 0
    
    def _batch_encode_keyphrases(self) -> np.ndarray:
        """Batch encode all keyphrases at once"""
        if not self.keyphrases:
            return None
        
        print(f"Encoding {len(self.keyphrases)} keyphrases...")
        embeddings = self.models.embedder.encode(
            self.keyphrases, 
            convert_to_tensor=True,
            batch_size=32,
            show_progress_bar=False
        )
        return embeddings
    
    @lru_cache(maxsize=5000)
    def get_cached_embedding(self, text: str) -> str:
        """Get cached embedding key for text"""
        # Return a hashable key for the embedding
        return f"emb_{hash(text)}"
    
    def batch_encode_sentences(self, sentences: List[str]) -> np.ndarray:
        """Batch encode multiple sentences efficiently"""
        if not sentences:
            return np.array([])
        
        # Check cache first
        cached_embeddings = []
        uncached_sentences = []
        uncached_indices = []
        
        with self._cache_lock:
            for i, sent in enumerate(sentences):
                cache_key = self.get_cached_embedding(sent)
                if cache_key in self._embedding_cache:
                    cached_embeddings.append((i, self._embedding_cache[cache_key]))
                    self.cache_hits += 1
                else:
                    uncached_sentences.append(sent)
                    uncached_indices.append(i)
                    self.cache_misses += 1
        
        # Encode uncached sentences in batch
        if uncached_sentences:
            new_embeddings = self.models.embedder.encode(
                uncached_sentences,
                convert_to_tensor=True,
                batch_size=32,
                show_progress_bar=False
            )
            
            # Cache new embeddings
            with self._cache_lock:
                for sent, emb, idx in zip(uncached_sentences, new_embeddings, uncached_indices):
                    cache_key = self.get_cached_embedding(sent)
                    self._embedding_cache[cache_key] = emb
                    cached_embeddings.append((idx, emb))
        
        # Sort by original index and return embeddings
        cached_embeddings.sort(key=lambda x: x[0])
        return np.array([emb for _, emb in cached_embeddings])
    
    def batch_compute_similarities(self, sentence_embeddings: np.ndarray, 
                                context_embedding: np.ndarray) -> np.ndarray:
        """Batch compute similarities between sentences and context"""
        if len(sentence_embeddings) == 0:
            return np.array([])
        
        # Use vectorized cosine similarity computation
        similarities = util.pytorch_cos_sim(sentence_embeddings, context_embedding.unsqueeze(0))
        return similarities.squeeze().cpu().numpy()
    
    def update_keyphrases_optimized(self, new_keyphrases: List[str]):
        """Optimized keyphrase update with batch embedding"""
        self.keyphrases = [kp.lower() for kp in new_keyphrases]
        
        if self.keyphrases:
            self.keyphrase_embeddings = self._batch_encode_keyphrases()
        else:
            self.keyphrase_embeddings = None
    
    def find_matching_keyphrases_vectorized(self, sentences: List[str]) -> Dict[str, List[int]]:
        """Vectorized keyphrase matching for better performance"""
        keyphrase_matches = {kp: [] for kp in self.keyphrases}
        
        # Convert sentences to lowercase for matching
        sentences_lower = [s.lower() for s in sentences]
        
        # Batch search for all keyphrases
        for kp in self.keyphrases:
            for i, sent_lower in enumerate(sentences_lower):
                if kp in sent_lower:
                    keyphrase_matches[kp].append(i)
        
        return keyphrase_matches
    
    def filter_sentences_optimized(self, sentences: List[str], title: str, abstract: str,
                                phrase_counts: Optional[Dict] = None,
                                important_indexes: Optional[Set[int]] = None) -> Tuple[Dict, Dict, List]:
        """Highly optimized sentence filtering with batch operations"""
        
        if not sentences:
            return {}, {}, list(self.keyphrases)
        
        # Initialize containers
        positive_examples = {kp: [] for kp in self.keyphrases}
        negative_examples = {kp: [] for kp in self.keyphrases}
        candidates_for_phrases = {kp: [] for kp in self.keyphrases}
        
        # Vectorized keyphrase matching
        keyphrase_matches = self.find_matching_keyphrases_vectorized(sentences)
        
        # Create context embedding from title and abstract
        context_text = f"{title} {abstract}".strip()
        context_emb = None
        if context_text and self.keyphrase_embeddings is not None:
            context_emb = self.models.embedder.encode(context_text, convert_to_tensor=True)
        
        # Process sentences with matching keyphrases
        sentences_to_embed = []
        sentence_metadata = []
        
        for kp, matching_indices in keyphrase_matches.items():
            if not matching_indices:
                continue
            
            freq = phrase_counts.get(kp, 0) if phrase_counts else 0
            
            for idx in matching_indices:
                sent = sentences[idx]
                if not sent:
                    continue
                
                # Check if we need similarity computation
                do_similarity = freq >= 3 and context_emb is not None
                
                if do_similarity:
                    sentences_to_embed.append(sent)
                    sentence_metadata.append({
                        'keyphrase': kp,
                        'index': idx,
                        'sentence': sent,
                        'important': important_indexes and idx in important_indexes,
                        'freq': freq
                    })
                else:
                    # Direct assignment for low-frequency keyphrases
                    candidates_for_phrases[kp].append((sent, None))
        
        # Batch compute embeddings and similarities
        if sentences_to_embed:
            sentence_embeddings = self.batch_encode_sentences(sentences_to_embed)
            similarities = self.batch_compute_similarities(sentence_embeddings, context_emb)
            
            # Process similarity results
            for meta, sim_score in zip(sentence_metadata, similarities):
                kp = meta['keyphrase']
                sent = meta['sentence']
                threshold = self.similarity_threshold
                
                if meta['important']:
                    threshold = max(threshold - 0.2, 0.0)
                
                if sim_score < threshold:
                    negative_examples[kp].append(sent)
                else:
                    candidates_for_phrases[kp].append((sent, sim_score))
        
        # Select best candidates for each keyphrase
        for kp, candidates in candidates_for_phrases.items():
            if not candidates:
                continue
            
            freq = phrase_counts.get(kp, 0) if phrase_counts else 0
            
            if freq < 3:
                # Add all candidates for low-frequency keyphrases
                for sent, _ in candidates:
                    positive_examples[kp].append(sent)
            else:
                # Select top candidates based on similarity scores
                exact_matches = [(sent, score) for sent, score in candidates if kp in sent.lower()]
                exact_matches.sort(key=lambda x: x[1] if x[1] is not None else 0, reverse=True)
                
                selected = exact_matches[:2]
                if len(selected) < 2:
                    other_candidates = [(sent, score) for sent, score in candidates if (sent, score) not in selected]
                    other_candidates.sort(key=lambda x: x[1] if x[1] is not None else 0, reverse=True)
                    selected.extend(other_candidates[:2 - len(selected)])
                
                for sent, _ in selected:
                    positive_examples[kp].append(sent)
        
        # Find unmatched keyphrases
        unmatched_phrases = [
            kp for kp in self.keyphrases 
            if not positive_examples.get(kp) and not negative_examples.get(kp)
        ]
        
        # Filter out empty lists
        positive_examples = {k: v for k, v in positive_examples.items() if v}
        negative_examples = {k: v for k, v in negative_examples.items() if v}
        
        return positive_examples, negative_examples, unmatched_phrases
    
    def process_optimized(self, text: str, title: str, abstract: str) -> Tuple[Dict, Dict, List]:
        """Optimized main processing function"""
        # Parse text structure
        paragraphs = [p.strip() for p in self.patterns['para_split'].split(text) if p.strip()]
        all_sentences = []
        bounds = []
        idx = 0
        
        for para in paragraphs:
            sents = [s.strip() for s in self.patterns['sent_split'].split(para) if s.strip()]
            all_sentences.extend(sents)
            bounds.append((idx, idx + len(sents) - 1))
            idx += len(sents)
        
        # Identify important sentence positions
        important_indexes = set()
        for start, end in bounds:
            length = end - start + 1
            if length >= 1:
                important_indexes.add(start)
            if length >= 2:
                important_indexes.add(start + 1)
            if length >= 3:
                important_indexes.add(end)
            if length >= 4:
                important_indexes.add(end - 1)
        
        # Process with optimized filtering
        return self.filter_sentences_optimized(
            all_sentences, title, abstract,
            phrase_counts=None,
            important_indexes=important_indexes
        )
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get caching performance statistics"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate_percent': round(hit_rate, 2),
            'embedding_cache_size': len(self._embedding_cache),
            'similarity_cache_size': len(self._similarity_cache)
        }
    
    def clear_cache(self):
        """Clear all caches"""
        with self._cache_lock:
            self._embedding_cache.clear()
            self._similarity_cache.clear()
        
        # Clear LRU cache
        self.get_cached_embedding.cache_clear()

# Backward compatibility
NoisySentenceRemover = OptimizedNoisySentenceRemover

if __name__ == "__main__":
    # Test the optimized remover
    print("Testing optimized NoisySentenceRemover...")
    
    keyphrases = ['electrochemical', 'lithium-ion', 'capacity', 'battery performance']
    
    start_time = time.time()
    
    remover = OptimizedNoisySentenceRemover(
        keyphrases=keyphrases,
        similarity_threshold=0.2,
        keyphrase_sim_threshold=0.8
    )
    
    # Sample text
    text = "The electrochemical properties were studied. | Battery performance was excellent. || The lithium-ion cells showed good capacity retention. | Temperature effects were minimal."
    title = "Electrochemical Study of Lithium-ion Batteries"
    abstract = "This work investigates battery performance and capacity in electrochemical systems."
    
    positive, negative, unmatched = remover.process_optimized(text, title, abstract)
    
    end_time = time.time()
    
    print(f"Processing completed in {end_time - start_time:.4f} seconds")
    print(f"Cache statistics: {remover.get_cache_stats()}")
    print(f"Positive examples: {len(positive)}")
    print(f"Negative examples: {len(negative)}")
    print(f"Unmatched keyphrases: {len(unmatched)}")
    
    for kp, sents in positive.items():
        print(f"  {kp}: {len(sents)} sentences")
