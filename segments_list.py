# document_lists.py

# Variations for different sections of a document
intro_list = [
    "INTRODUCTION",
    "I N T R O D U C T I O N",
    "I n t r o d u c t i o n",
    "Introduction",
    "Background",
    "Introduction and Background",
    "B A C K G R O U N D",

]
abstract_list = [
    "ABSTRACT",
    "Abstract",
    "A B S T R A C T",
    "a b s t r a c t",
    "Summary of Findings",
    "Abst.",
    "Summary",
    "S U M M A R Y",
]
keywords_list = [
    "KEYWORDS",
    "Keywords",
    "K E Y W O R D S",
    "Key Terms",
    "Topics",
]
experimental_list = [
    
    "EXPERIMENTAL PROCEDURES",
    "Experimental Procedure",
    "Experimental section",
    "Experimental Sections",
    "Experimental Section",
    "EXPERIMENTAL SECTION",
    "Experimental Section",
    "Experimental section",
    "■ EXPERIMENTAL SECTION",
    "EXPERIMENTAL PART",
    "Experimental Part",
    "Experimental part",
    "EXPERIMENTAL BIOLOGICAL PART",
    "EXPERIMENTAL CHEMICAL PART",
    "EXPERIMENTAL",
    "Experimental",
    "Experiment",
]
results_list = [
    "Results and discussions",
    "R E S U L T S  A N D  D I S C U S S I O N",
    "CALCULATIONS, RESULTS, AND DISCUSSION",
    "EXPERIMENT RESULTS AND DISCUSSION",
    "Experiment results and discussion",
    "Experiment Results and Discussion",
    "Experiment Results And Discussion",
    "Experimental Results And Discussion",
    "Experimental results and discussion",
    "Experimental Results and Discussion",
    "Experimental Results and discussion",
    "RESULTS AND DISCUSSION",
    "RESULTS AND DISCUSSIONS",
    "RESULT AND DISCUSSION",
    "Results and Discussion",
    "Result and discussion",
    "Result and Discussion",
    "Results and Discussions",
    "Result and Discussions",
    "Experimental results",
    "Experimental Results",
    "EXPERIMENTAL RESULTS",
    "Results and discussion",
    "Findings",
    "Outcomes",
    "RESULTS",
    "R E S U L T S",
    "Results",
    "Result",

]
materials_methods_list = [
    "Materials and methods",
    "Materials and Method",
    "Material and Methods",
    "Material and Method",
    "Methodology",
    "Methods",
    # "2 | METHODS",
    "Materials",
]
conclusion_list = [
    "Conclusions",
    # "• CONCLUSIONS:",
    "CONCLUSION OR SUMMARY AND OUTLOOK",
    "Conclusion or summary and outlook",
    "Conclusion or Summary and Outlook",
    "Conclusions and future prospects",
    "Conclusions and Future Directions",
    "Conclusions and research needs",
    "Conclusion and future research",
    "Conclusions and perspectives",
    "Conclusions and Perspectives",
    "Conclusions and Discussions",
    "Conclusion and Discussion",
    "Conclusions and Discussion",
    "Conclusions and discussion",
    "C O N C L U S I O N S",
    "C O N C L U S I O N",
    "SUMMARY AND OUTLOOK",
    "Summary and Outlook",
    "Summary And Outlook",
    "Summary and outlook",
    "Conclusion",
    "CONCLUSIONS",
    "CONCLUSION",
]
acknowledgments_list = [
    "ACKNOWLEDGMENTS",
    "ACKNOWLEDGEMENTS",
    "A C K N O W L E D G E M E N T S",
    "A C K N O W L E D G M E N T S",
    "Acknowledgments:",
    "Acknowledgments",
    "Acknowledgements",
    "Acknowledgement",
    "Acknowlegments",
    "ACKNOWLEDGMENTS",
    "ACKNOWLEDGEMENTS",
    "ACKNOWLEGMENTS",
    "A C K N O W L E D G M E N T S",
    "A C K N O W L E D G E M E N T S",
    "A C K N O W L E G M E N T S",
]
references_list = [
    "REFERENCES",
    "References and notes",
    "References and Notes",
    "Reference List",
    "R E F E R E N C E S",
    "R e f e r e n c e s",
    "References",
    "B I B L I O G R A P H Y",
    "B i b l i o g r a p h y",
    "Bibliography",
    "REFERENCES",
    "References",
    "BIBLIOGRAPHY",
    "B I B L I O G R A P H Y",
    "Bibliography",
    "CRediT authorship",
    "CrediT authorship",
    "Declaration of competing interest",
    "Declaration of Competing Interests",

]

not_required_list = [ "Discussions", "DISCUSSIONS", "DISCUSSION","Discussion", "CONFLICT  OF  INTEREST",]
title_list = [
    intro_list,
    abstract_list,
    keywords_list,
    experimental_list,
    results_list,
    materials_methods_list,
    conclusion_list,
    acknowledgments_list,
    references_list,
    not_required_list
]



# Configuration variables
research = True  # Default value
article_size = "Large"  # Default value

# Additional utility functions (optional)
def get_all_section_names():
    """Return all section names as a flat list"""
    all_names = []
    for section_list in title_list[:-1]:  # Exclude not_required_list
        all_names.extend(section_list)
    return all_names

def get_section_by_name(section_name):
    """Get specific section list by name"""
    section_mapping = {
        'intro': intro_list,
        'abstract': abstract_list,
        'keywords': keywords_list,
        'experimental': experimental_list,
        'results': results_list,
        'materials_methods': materials_methods_list,
        'conclusion': conclusion_list,
        'acknowledgments': acknowledgments_list,
        'references': references_list,
        'not_required': not_required_list
    }
    return section_mapping.get(section_name.lower(), [])
