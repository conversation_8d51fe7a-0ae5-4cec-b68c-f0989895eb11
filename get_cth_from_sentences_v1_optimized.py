import pandas as pd
import numpy as np
import time
import re
from typing import Tuple, List, Dict, Optional
from collections import defaultdict

def calculate_sentence_quality(sentence: str) -> float:
    """Calculate quality score for a sentence"""
    if not sentence or len(sentence) < 10:
        return 0.0
    
    # Word count factor
    words = sentence.split()
    word_count_score = min(len(words) / 20, 1.0)  # Optimal around 20 words
    
    # Special character ratio
    special_chars = len(re.findall(r'[^\w\s]', sentence))
    special_ratio = special_chars / len(sentence)
    special_score = max(0, 1 - special_ratio * 2)  # Penalize high special char ratio
    
    # Numeric ratio
    numbers = len(re.findall(r'\d', sentence))
    numeric_ratio = numbers / len(sentence)
    numeric_score = max(0, 1 - numeric_ratio * 3)  # Penalize high numeric ratio
    
    # Combine scores
    quality_score = (word_count_score + special_score + numeric_score) / 3
    
    return quality_score

def filter_sentences(df: pd.DataFrame, min_words: int = 5, max_words: int = 100,
                    max_numeric_ratio: float = 0.3, max_special_char_ratio: float = 0.3) -> pd.DataFrame:
    """Filter sentences based on quality criteria"""
    
    filtered_sentences = []
    
    for _, row in df.iterrows():
        sentence = str(row.get('sentence', ''))
        
        if not sentence or len(sentence.strip()) < 10:
            continue
        
        words = sentence.split()
        word_count = len(words)
        
        # Filter by word count
        if word_count < min_words or word_count > max_words:
            continue
        
        # Filter by numeric ratio
        numbers = len(re.findall(r'\d', sentence))
        numeric_ratio = numbers / len(sentence) if sentence else 0
        if numeric_ratio > max_numeric_ratio:
            continue
        
        # Filter by special character ratio
        special_chars = len(re.findall(r'[^\w\s]', sentence))
        special_ratio = special_chars / len(sentence) if sentence else 0
        if special_ratio > max_special_char_ratio:
            continue
        
        # Calculate quality score
        quality_score = calculate_sentence_quality(sentence)
        
        # Add quality score to row data
        row_data = row.to_dict()
        row_data['quality_score'] = quality_score
        row_data['word_count'] = word_count
        row_data['numeric_ratio'] = numeric_ratio
        row_data['special_ratio'] = special_ratio
        
        filtered_sentences.append(row_data)
    
    return pd.DataFrame(filtered_sentences)

def rank_sentences(df: pd.DataFrame, readability_weight: float = 0.15,
                grammar_weight: float = 0.20, semantic_weight: float = 0.30,
                term_context_weight: float = 0.25, complexity_weight: float = 0.10) -> pd.DataFrame:
    """Rank sentences by combined score"""
    
    ranked_sentences = []
    
    for _, row in df.iterrows():
        # Get base quality score
        quality_score = row.get('quality_score', 0.5)
        
        # Calculate additional scoring factors
        sentence = str(row.get('sentence', ''))
        keyphrases = str(row.get('keyphrases_found', ''))
        
        # Term context score (based on keyphrase presence)
        term_score = 1.0 if keyphrases and keyphrases != 'nan' else 0.3
        
        # Readability score (inverse of complexity)
        word_count = row.get('word_count', 10)
        readability_score = max(0, 1 - abs(word_count - 15) / 30)  # Optimal around 15 words
        
        # Grammar score (placeholder - could use language models)
        grammar_score = quality_score  # Use quality as proxy
        
        # Semantic score (placeholder - could use embeddings)
        semantic_score = quality_score  # Use quality as proxy
        
        # Complexity score
        complexity_score = 1 - row.get('special_ratio', 0)
        
        # Calculate final ranking score
        final_score = (
            readability_weight * readability_score +
            grammar_weight * grammar_score +
            semantic_weight * semantic_score +
            term_context_weight * term_score +
            complexity_weight * complexity_score
        )
        
        row_data = row.to_dict()
        row_data['final_ranking_score'] = final_score
        row_data['readability_score'] = readability_score
        row_data['grammar_score'] = grammar_score
        row_data['semantic_score'] = semantic_score
        row_data['term_context_score'] = term_score
        row_data['complexity_score'] = complexity_score
        
        ranked_sentences.append(row_data)
    
    # Sort by final score
    ranked_df = pd.DataFrame(ranked_sentences)
    ranked_df = ranked_df.sort_values('final_ranking_score', ascending=False)
    
    return ranked_df

def optimized_main_pipeline(
    input_excel: str,
    output_excel_ta: str,
    output_excel_other: str,
    rejected_file: str,
    max_sentences_per_term: int,
    min_words: int,
    max_words: int,
    max_numeric_ratio: float,
    max_special_char_ratio: float,
    max_repetition_ratio: float,
    readability_weight: float,
    grammar_weight: float,
    semantic_weight: float,
    term_context_weight: float,
    complexity_weight: float,
    apply_cross_references: bool,
    apply_filtering: bool,
    apply_ranking: bool,
    save_rejected: bool,
    verbose: bool,
    max_workers: int = None
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Optimized pipeline for sentence filtering and ranking
    """
    print(" Starting optimized sentence filtering pipeline...")
    
    pipeline_start = time.time()
    
    try:
        # Load input data
        if verbose:
            print(f" Loading data from: {input_excel}")
        
        df = pd.read_excel(input_excel)
        
        if verbose:
            print(f" Loaded {len(df)} records")
        
        # Apply filtering if requested
        if apply_filtering:
            if verbose:
                print(" Applying sentence filtering...")
            
            df_filtered = filter_sentences(
                df, min_words, max_words, max_numeric_ratio, max_special_char_ratio
            )
            
            if verbose:
                print(f" Filtered to {len(df_filtered)} sentences")
        else:
            df_filtered = df.copy()
        
        # Split into title/abstract and other sections
        if 'section_type' in df_filtered.columns:
            ta_mask = df_filtered['section_type'].isin(['title', 'abstract'])
            df_ta = df_filtered[ta_mask].copy()
            df_other = df_filtered[~ta_mask].copy()
        else:
            # If no section_type column, split randomly
            split_point = len(df_filtered) // 2
            df_ta = df_filtered.iloc[:split_point].copy()
            df_other = df_filtered.iloc[split_point:].copy()
        
        # Apply ranking if requested
        if apply_ranking:
            if verbose:
                print(" Applying sentence ranking...")
            
            df_ta = rank_sentences(
                df_ta, readability_weight, grammar_weight, semantic_weight,
                term_context_weight, complexity_weight
            )
            
            df_other = rank_sentences(
                df_other, readability_weight, grammar_weight, semantic_weight,
                term_context_weight, complexity_weight
            )
        
        # Limit sentences per term if specified
        if max_sentences_per_term > 0:
            df_ta = df_ta.head(max_sentences_per_term)
            df_other = df_other.head(max_sentences_per_term)
        
        # Save results
        df_ta.to_excel(output_excel_ta, index=False)
        df_other.to_excel(output_excel_other, index=False)
        
        if save_rejected and apply_filtering:
            # Save rejected sentences
            rejected_mask = ~df.index.isin(df_filtered.index)
            df_rejected = df[rejected_mask]
            df_rejected.to_excel(rejected_file, index=False)
            
            if verbose:
                print(f" Saved {len(df_rejected)} rejected sentences to: {rejected_file}")
        
        pipeline_end = time.time()
        
        if verbose:
            print("="*60)
            print(" PIPELINE COMPLETED SUCCESSFULLY")
            print("="*60)
            print(f"  Processing time: {pipeline_end - pipeline_start:.2f} seconds")
            print(f" Title/Abstract sentences: {len(df_ta)}")
            print(f" Other section sentences: {len(df_other)}")
            print(f" Output files:")
            print(f"   - {output_excel_ta}")
            print(f"   - {output_excel_other}")
            print("="*60)
        
        return df_ta, df_other
        
    except Exception as e:
        print(f" Error in optimized pipeline: {e}")
        
        # Create empty DataFrames as fallback
        empty_df = pd.DataFrame()
        return empty_df, empty_df




if __name__ == "__main__":
    # Test configuration
    INPUT_FILE = "test_input.xlsx"
    OUTPUT_FILE_TITLE_ABSTRACT = "title_abstract_sentences_final_ranked.xlsx"
    OUTPUT_FILE_OTHER_SECTIONS = "other_section_sentences_final_ranked.xlsx"
    REJECTED_ANALYSIS_FILE = "rejected_sentences_analysis.xlsx"
    
    df_ta_result, df_other_result = optimized_main_pipeline(
        input_excel=INPUT_FILE,
        output_excel_ta=OUTPUT_FILE_TITLE_ABSTRACT,
        output_excel_other=OUTPUT_FILE_OTHER_SECTIONS,
        rejected_file=REJECTED_ANALYSIS_FILE,
        max_sentences_per_term=50,
        min_words=5,
        max_words=100,
        max_numeric_ratio=0.3,
        max_special_char_ratio=0.3,
        max_repetition_ratio=0.3,
        readability_weight=0.15,
        grammar_weight=0.20,
        semantic_weight=0.30,
        term_context_weight=0.25,
        complexity_weight=0.10,
        apply_cross_references=True,
        apply_filtering=True,
        apply_ranking=True,
        save_rejected=True,
        verbose=True,
        max_workers=4
    )
    
    print(f" Processing completed!")
    print(f" Title/Abstract: {len(df_ta_result)} sentences")
    print(f" Other sections: {len(df_other_result)} sentences")



