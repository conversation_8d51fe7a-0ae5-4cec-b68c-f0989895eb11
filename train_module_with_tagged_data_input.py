# train_module_with_tagged_data_input.py
"""
Training module that uses pre-tagged debug files as input.
This module loads the "title_abstract_sentences_final_ranked_debug_tags.xlsx" file
and uses the existing tags for model training.
"""

import pandas as pd
import numpy as np
import ast
import os
from datetime import datetime
from pathlib import Path
from sklearn.model_selection import train_test_split
from datasets import Dataset, DatasetDict
from transformers import (
    AutoTokenizer,
    AutoModelForTokenClassification,
    TrainingArguments,
    Trainer,
    DataCollatorForTokenClassification
)
from config import LABELS, label2id, id2label, MAX_LENGTH, RANDOM_SEED
import torch
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings("ignore")


def create_training_directories(base_dir: str = "./training_outputs") -> Dict[str, str]:
    """
    Create dynamic directory structure for training outputs

    Args:
        base_dir: Base directory for all training outputs

    Returns:
        Dictionary with paths to created directories
    """
    # Create timestamp for unique directory naming
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Define directory structure
    dirs = {
        'base': base_dir,
        'models': os.path.join(base_dir, 'models'),
        'logs': os.path.join(base_dir, 'logs'),
        'checkpoints': os.path.join(base_dir, 'checkpoints'),
        'predictions': os.path.join(base_dir, 'predictions'),
        'current_run': os.path.join(base_dir, 'models', f'bert_ner_{timestamp}'),
        'current_logs': os.path.join(base_dir, 'logs', f'training_{timestamp}'),
        'current_checkpoints': os.path.join(base_dir, 'checkpoints', f'checkpoints_{timestamp}')
    }

    # Create all directories
    for dir_name, dir_path in dirs.items():
        try:
            os.makedirs(dir_path, exist_ok=True)
            print(f" Created directory: {dir_path}")
        except Exception as e:
            print(f" Error creating directory {dir_path}: {e}")
            raise

    print(f"\n Training directories created successfully!")
    print(f" Model will be saved to: {dirs['current_run']}")
    print(f"Logs will be saved to: {dirs['current_logs']}")
    print(f" Checkpoints will be saved to: {dirs['current_checkpoints']}")

    return dirs


def ensure_directory_exists(directory_path: str) -> str:
    """
    Ensure a directory exists, create if it doesn't

    Args:
        directory_path: Path to directory

    Returns:
        Absolute path to the directory
    """
    abs_path = os.path.abspath(directory_path)
    os.makedirs(abs_path, exist_ok=True)
    return abs_path

class PreTaggedDatasetLoader:
    """
    Loads pre-tagged debug files and prepares them for training
    without re-doing the tagging process.
    """
    
    def __init__(self, debug_file_path: str):
        self.debug_file_path = debug_file_path
        self.tokenizer = None
        
    def load_debug_file(self) -> pd.DataFrame:
        """Load the pre-tagged debug file"""
        print(f"Loading pre-tagged debug file: {self.debug_file_path}")
        
        if not os.path.exists(self.debug_file_path):
            raise FileNotFoundError(f"Debug file not found: {self.debug_file_path}")
            
        df = pd.read_excel(self.debug_file_path)
        print(f"Loaded {len(df)} pre-tagged records")
        print(f"Available columns: {list(df.columns)}")
        
        # Validate required columns
        required_columns = ['sentence', 'tokens', 'tags', 'term_found']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in debug file: {missing_columns}")
            
        return df
    
    def parse_list_column(self, value) -> List:
        """Parse string representation of lists back to actual lists"""
        if pd.isna(value):
            return []
        
        if isinstance(value, str):
            try:
                # Try to parse as literal list
                return ast.literal_eval(value)
            except (ValueError, SyntaxError):
                # If that fails, try splitting by common separators
                if ',' in value:
                    return [item.strip().strip("'\"") for item in value.split(',')]
                else:
                    return [value.strip().strip("'\"")]
        elif isinstance(value, list):
            return value
        else:
            return [str(value)]
    
    def prepare_training_data(self, df: pd.DataFrame) -> List[Dict]:
        """
        Convert pre-tagged debug data to training format
        """
        print("Preparing training data from pre-tagged records...")
        
        training_records = []
        
        for idx, row in df.iterrows():
            sentence = row['sentence']
            tokens = self.parse_list_column(row['tokens'])
            tags = self.parse_list_column(row['tags'])
            term_found = self.parse_list_column(row['term_found'])
            
            # Skip if any required data is missing
            if not sentence or not tokens or not tags:
                continue
                
            # Skip if tokens and tags don't match in length
            if len(tokens) != len(tags):
                print(f"Warning: Token/tag length mismatch in row {idx}, skipping...")
                continue
            
            # Convert tags to label IDs
            label_ids = []
            for tag in tags:
                if tag in label2id:
                    label_ids.append(label2id[tag])
                elif tag == "SPECIAL":
                    label_ids.append(-100)  # Special tokens
                else:
                    label_ids.append(label2id["O"])  # Default to O if unknown
            
            training_records.append({
                'sentence': sentence,
                'tokens': tokens,
                'tags': tags,
                'label_ids': label_ids,
                'term_found': term_found
            })
        
        print(f"Prepared {len(training_records)} training records")
        return training_records
    
    def create_huggingface_dataset(self, training_records: List[Dict], 
                                test_size: float = 0.1, 
                                val_size: float = 0.1) -> DatasetDict:
        """
        Create HuggingFace dataset from training records
        """
        print("Creating HuggingFace dataset...")
        
        if len(training_records) < 3:
            # Too few records for proper split
            ds_train = Dataset.from_list(training_records)
            ds_val = Dataset.from_list([])
            ds_test = Dataset.from_list([])
        else:
            # Split the data
            train_val, test = train_test_split(training_records, test_size=test_size, random_state=RANDOM_SEED)
            if len(train_val) < 2:
                train = train_val
                val = []
            else:
                train, val = train_test_split(train_val, test_size=val_size/(1-test_size), random_state=RANDOM_SEED)
            
            ds_train = Dataset.from_list(train)
            ds_val = Dataset.from_list(val if val else [])
            ds_test = Dataset.from_list(test)
        
        dataset = DatasetDict({
            "train": ds_train, 
            "validation": ds_val, 
            "test": ds_test
        })
        
        print(f"Dataset splits - Train: {len(dataset['train'])}, Val: {len(dataset['validation'])}, Test: {len(dataset['test'])}")
        return dataset


class BERTTrainer:
    """
    BERT model trainer that uses pre-tagged data
    """
    
    def __init__(self, model_name: str = "bert-base-uncased"):
        self.model_name = model_name
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = None
        
    def prepare_model(self):
        """Initialize the BERT model for token classification"""
        print(f"Loading BERT model: {self.model_name}")
        
        self.model = AutoModelForTokenClassification.from_pretrained(
            self.model_name,
            num_labels=len(LABELS),
            id2label=id2label,
            label2id=label2id
        )
        
        print(f"Model loaded with {len(LABELS)} labels: {LABELS}")
        
    def tokenize_dataset(self, dataset: DatasetDict) -> DatasetDict:
        """
        Tokenize the pre-tagged dataset for BERT training
        """
        print("Tokenizing dataset for BERT training...")
        
        def tokenize_function(examples):
            # Use the pre-existing tokens and labels from debug file
            tokenized = {
                'input_ids': [],
                'attention_mask': [],
                'labels': []
            }
            
            for i in range(len(examples['sentence'])):
                tokens = examples['tokens'][i]
                label_ids = examples['label_ids'][i]
                
                # Convert tokens to input_ids
                input_ids = self.tokenizer.convert_tokens_to_ids(tokens)
                
                # Create attention mask
                attention_mask = [1] * len(input_ids)
                
                # Pad or truncate to MAX_LENGTH
                if len(input_ids) > MAX_LENGTH:
                    input_ids = input_ids[:MAX_LENGTH]
                    attention_mask = attention_mask[:MAX_LENGTH]
                    label_ids = label_ids[:MAX_LENGTH]
                else:
                    padding_length = MAX_LENGTH - len(input_ids)
                    input_ids.extend([self.tokenizer.pad_token_id] * padding_length)
                    attention_mask.extend([0] * padding_length)
                    label_ids.extend([-100] * padding_length)
                
                tokenized['input_ids'].append(input_ids)
                tokenized['attention_mask'].append(attention_mask)
                tokenized['labels'].append(label_ids)
            
            return tokenized
        
        # Apply tokenization to all splits
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=['sentence', 'tokens', 'tags', 'term_found']
        )
        
        return tokenized_dataset
    
    def train_model(self, tokenized_dataset: DatasetDict,
                output_dir: str = "./bert_ner_model",
                num_epochs: int = 3,
                batch_size: int = 16,
                learning_rate: float = 2e-5,
                training_dirs: Optional[Dict[str, str]] = None) -> Trainer:
        """
        Train the BERT model with dynamic directory creation
        """
        print("Starting BERT model training...")

        # Create or ensure directories exist
        if training_dirs is None:
            print(" Creating training directories...")
            training_dirs = create_training_directories()
            output_dir = training_dirs['current_run']
            logging_dir = training_dirs['current_logs']
        else:
            output_dir = ensure_directory_exists(output_dir)
            logging_dir = ensure_directory_exists(f"{output_dir}/logs")

        print(f" Model output directory: {output_dir}")
        print(f"Logging directory: {logging_dir}")

        # Training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            learning_rate=learning_rate,
            weight_decay=0.01,
            logging_dir=logging_dir,
            logging_steps=10,
            evaluation_strategy="epoch" if len(tokenized_dataset['validation']) > 0 else "no",
            save_strategy="epoch",
            load_best_model_at_end=True if len(tokenized_dataset['validation']) > 0 else False,
            metric_for_best_model="eval_loss" if len(tokenized_dataset['validation']) > 0 else None,
            seed=RANDOM_SEED,
            report_to=None,  # Disable wandb logging
            save_total_limit=3,  # Keep only 3 best checkpoints
            dataloader_num_workers=0  # Avoid multiprocessing issues
        )
        
        # Data collator
        data_collator = DataCollatorForTokenClassification(
            tokenizer=self.tokenizer,
            padding=True
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=tokenized_dataset['train'],
            eval_dataset=tokenized_dataset['validation'] if len(tokenized_dataset['validation']) > 0 else None,
            data_collator=data_collator,
            tokenizer=self.tokenizer
        )
        
        # Train the model
        print("Training started...")
        trainer.train()
        
        # Save the final model
        trainer.save_model()
        self.tokenizer.save_pretrained(output_dir)
        
        print(f"Training completed! Model saved to: {output_dir}")
        return trainer


def main_training_pipeline(debug_file_path: str,
                        output_model_dir: Optional[str] = None,
                        test_size: float = 0.1,
                        val_size: float = 0.1,
                        num_epochs: int = 3,
                        batch_size: int = 16,
                        learning_rate: float = 2e-5,
                        create_dynamic_dirs: bool = True):
    """
    Main training pipeline using pre-tagged debug file with dynamic directory creation
    """
    print("="*80)
    print("BERT NER TRAINING PIPELINE - USING PRE-TAGGED DATA")
    print("="*80)

    # Step 1: Create training directories
    training_dirs = None
    if create_dynamic_dirs:
        print(" Setting up training directories...")
        training_dirs = create_training_directories()
        if output_model_dir is None:
            output_model_dir = training_dirs['current_run']
    else:
        if output_model_dir is None:
            output_model_dir = "./bert_ner_model"
        output_model_dir = ensure_directory_exists(output_model_dir)

    print(f" Training will save to: {output_model_dir}")

    # Step 2: Load pre-tagged data
    loader = PreTaggedDatasetLoader(debug_file_path)
    df = loader.load_debug_file()

    # Step 3: Prepare training data
    training_records = loader.prepare_training_data(df)

    # Step 4: Create HuggingFace dataset
    dataset = loader.create_huggingface_dataset(training_records, test_size, val_size)

    # Step 5: Initialize trainer
    trainer = BERTTrainer()
    trainer.prepare_model()

    # Step 6: Tokenize dataset
    tokenized_dataset = trainer.tokenize_dataset(dataset)

    # Step 7: Train model with dynamic directories
    trained_model = trainer.train_model(
        tokenized_dataset,
        output_model_dir,
        num_epochs,
        batch_size,
        learning_rate,
        training_dirs
    )
    
    print("="*80)
    print("TRAINING PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*80)
    print(f" Model saved to: {output_model_dir}")
    if training_dirs:
        print(f"Logs saved to: {training_dirs['current_logs']}")
        print(f" Checkpoints saved to: {training_dirs['current_checkpoints']}")

    return trained_model, output_model_dir


def evaluate_model(trainer: Trainer, tokenized_dataset: DatasetDict) -> Dict:
    """
    Evaluate the trained model on test set
    """
    print("Evaluating model on test set...")

    if len(tokenized_dataset['test']) == 0:
        print("No test data available for evaluation")
        return {}

    # Evaluate on test set
    eval_results = trainer.evaluate(eval_dataset=tokenized_dataset['test'])

    print("Evaluation Results:")
    for key, value in eval_results.items():
        print(f"  {key}: {value:.4f}")

    return eval_results


def predict_sample(model_dir: str, sample_sentences: List[str]) -> List[Dict]:
    """
    Test the trained model on sample sentences
    """
    print(f"Loading trained model from: {model_dir}")

    # Load trained model and tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = AutoModelForTokenClassification.from_pretrained(model_dir)

    predictions = []

    for sentence in sample_sentences:
        print(f"\nPredicting for: {sentence}")

        # Tokenize
        inputs = tokenizer(sentence, return_tensors="pt", truncation=True, max_length=MAX_LENGTH)

        # Predict
        with torch.no_grad():
            outputs = model(**inputs)
            predictions_tensor = torch.argmax(outputs.logits, dim=2)

        # Convert predictions to labels
        tokens = tokenizer.convert_ids_to_tokens(inputs["input_ids"][0])
        predicted_labels = [id2label[pred.item()] for pred in predictions_tensor[0]]

        # Extract keyphrases
        keyphrases = []
        current_phrase = []

        for token, label in zip(tokens, predicted_labels):
            if label == "B-KEY":
                if current_phrase:
                    keyphrases.append(tokenizer.convert_tokens_to_string(current_phrase))
                current_phrase = [token]
            elif label == "I-KEY" and current_phrase:
                current_phrase.append(token)
            else:
                if current_phrase:
                    keyphrases.append(tokenizer.convert_tokens_to_string(current_phrase))
                    current_phrase = []

        if current_phrase:
            keyphrases.append(tokenizer.convert_tokens_to_string(current_phrase))

        predictions.append({
            'sentence': sentence,
            'tokens': tokens,
            'predicted_labels': predicted_labels,
            'extracted_keyphrases': keyphrases
        })

        print(f"Extracted keyphrases: {keyphrases}")

    return predictions


if __name__ == "__main__":
    # Configuration
    DEBUG_FILE_PATH = "all_keyphrases_combined_tagged_data.xlsx"    ## Combine two files from the output of  "get_cth_from_sentences_v1_optimized.py"
                                                                    ## Add tagging by using "dataset_module_v1.py" , then get all excel sheets combined which contain "_final_ranked_debug_tags.xlsx" ---> which gives -----> all_keyphrases_combined_tagged_data.xlsx
    # Training parameters 
    TEST_SIZE = 0.1
    VAL_SIZE = 0.1
    NUM_EPOCHS = 3
    BATCH_SIZE = 8  
    LEARNING_RATE = 2e-5

    # Directory settings
    CREATE_DYNAMIC_DIRS = True 
    CUSTOM_OUTPUT_DIR = None    

    # Run training pipeline
    try:
        print(" Starting BERT NER training with pre-tagged data...")
        print(f"Dataset: {DEBUG_FILE_PATH}")
        print(f" Batch size: {BATCH_SIZE} (optimized for large dataset)")
        print(f" Epochs: {NUM_EPOCHS}")
        print(f" Learning rate: {LEARNING_RATE}")

        trained_model, final_model_dir = main_training_pipeline(
            debug_file_path=DEBUG_FILE_PATH,
            output_model_dir=CUSTOM_OUTPUT_DIR,
            test_size=TEST_SIZE,
            val_size=VAL_SIZE,
            num_epochs=NUM_EPOCHS,
            batch_size=BATCH_SIZE,
            learning_rate=LEARNING_RATE,
            create_dynamic_dirs=CREATE_DYNAMIC_DIRS
        )

        print(f"\n Training completed successfully!")
        print(f" Model saved to: {final_model_dir}")

        # Test the model with sample sentences
        sample_sentences = [
            "The synthesis of organic compounds involves various chemical reactions.",
            "Protein folding is crucial for biological activity and enzyme function.",
            "Machine learning algorithms can predict molecular properties effectively."
        ]

        print(f"\n Testing trained model on sample sentences...")
        predictions = predict_sample(final_model_dir, sample_sentences)

        print(f"\n Training and testing completed!")
        print(f" You can now use the trained model for keyphrase extraction!")
        print(f" Model location: {final_model_dir}")

    except Exception as e:
        print(f" Error during training: {e}")
        import traceback
        traceback.print_exc()
        raise
