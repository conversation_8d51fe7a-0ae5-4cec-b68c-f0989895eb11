import re
import ast
import time
import pandas as pd
import inflect
from tqdm import tqdm
from typing import List, Dict, Set
import os
import glob

from data_preprocessor_v1 import SegmentPreprocessor
from section_weighted_extractor import BasicKeyphraseExtractor
from noisy_sentences_remover_v1 import NoisySentenceRemover

import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="transformers")
warnings.filterwarnings("ignore", category=FutureWarning, module="torch")

def load_curated_data(curator_data_folder: str, section_code: str) -> pd.DataFrame:
    """Load REAL curated data from Excel files"""
    
    # Look for Excel files in the curator data folder
    excel_files = glob.glob(os.path.join(curator_data_folder, "*.xlsx"))
    print(f"{excel_files =}")
    if not excel_files:
        print(f" No Excel files found in {curator_data_folder}")
        return pd.DataFrame()
    
    all_data = []
    for excel_file in excel_files:
        try:
            df = pd.read_excel(excel_file)
            
            # Filter by section code if column exists
            if 'SECTION_CODE' in df.columns:
                df = df[df['SECTION_CODE'].astype(str) == str(section_code)]
            
            if not df.empty:
                all_data.append(df)
                print(f" Loaded {len(df)} records from {os.path.basename(excel_file)}")
                
        except Exception as e:
            print(f" Error loading {excel_file}: {e}")
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f" Total curated records: {len(combined_df)}")
        return combined_df
    
    return pd.DataFrame()

def extract_keyphrases_from_curated_data(df: pd.DataFrame) -> List[str]:
    """Extract keyphrases from curated data"""
    keyphrases = set()
    
    # Look for common keyphrase columns
    keyphrase_columns = ['keyphrase', 'keyphrases', 'term', 'terms', 'keyword', 'keywords', 'CTH_TERMS']
    
    print(f"Available columns: {list(df.columns)}")
    
    for col in df.columns:
        if any(kw in col.upper() for kw in [c.upper() for c in keyphrase_columns]):
            print(f"Processing keyphrase column: {col}")
            for value in df[col].dropna():
                if isinstance(value, str):
                    # Handle comma-separated values
                    terms = [term.strip() for term in value.split(',')]
                    keyphrases.update(terms)
                elif isinstance(value, list):
                    keyphrases.update(value)
    
    # Filter out empty strings and very short terms
    keyphrases = [kp for kp in keyphrases if kp and len(kp) > 2]
    
    print(f" Extracted {len(keyphrases)} unique keyphrases")
    if len(keyphrases) > 0:
        print(f"Sample keyphrases: {list(keyphrases)[:10]}")
    
    return list(keyphrases)

def create_sample_pdf_data(pdf_folder: str, keyphrases: List[str]) -> pd.DataFrame:
    """Create sample PDF data for testing"""
    
    # Get shipment number from folder path
    shipment = pdf_folder.split('\\')[-1] if '\\' in pdf_folder else pdf_folder.split('/')[-1]
    
    # Create sample data with keyphrases
    sample_sentences = []
    
    for i, keyphrase in enumerate(keyphrases[:20]):  # Limit to first 20 keyphrases
        # Create sample sentences containing the keyphrase
        sentences = [
            f"The study investigated {keyphrase} and its applications in chemical processes.",
            f"Results showed that {keyphrase} plays a crucial role in the reaction mechanism.",
            f"The {keyphrase} was analyzed using advanced spectroscopic techniques.",
            f"Our findings demonstrate the importance of {keyphrase} in industrial applications."
        ]
        
        for j, sentence in enumerate(sentences):
            sample_sentences.append({
                'pdf_file': f'sample_{i}_{j}.pdf',
                'section_type': ['title', 'abstract', 'results', 'conclusion'][j],
                'sentence': sentence,
                'keyphrases_found': keyphrase,
                'has_keyphrase': True,
                'TAN_NAME': f'TAN_{shipment}_{i}_{j}',
                'shipment': shipment
            })
    
    return pd.DataFrame(sample_sentences)

def main_processing_pipeline(section_code: str, curator_data_folder: str, 
                        pdf_folder: str, abbr_full_form_excel: str,
                        shipment_name: str = None) -> str:
    """Main processing pipeline for dataset creation with unique naming"""
    
    print("="*80)
    print("MAIN PROCESSING PIPELINE")
    print("="*80)
    
    start_time = time.time()
    
    # Extract shipment name if not provided
    if not shipment_name:
        shipment_name = os.path.basename(pdf_folder.rstrip('\\').rstrip('/'))
    
    # Step 1: Load REAL curated data (not sample data)
    print("Step 1: Loading REAL curated data...")
    curated_df = load_curated_data(curator_data_folder, section_code)
    
    if curated_df.empty:
        print(" No curated data found!")
        return None
    
    print(f" Loaded {len(curated_df)} curated records")
    
    # Step 2: Group by TAN_NAME and aggregate keyphrases
    print("Step 2: Grouping data by TAN_NAME...")
    tan_groups = group_data_by_tan(curated_df)
    print(f" Found {len(tan_groups)} unique TANs")
    
    # Step 3: Process each unique TAN only once
    print("Step 3: Processing PDFs...")
    
    # Create unique intermediate folder
    intermediate_folder = f"intermediate_section_{section_code}_shipment_{shipment_name}"
    os.makedirs(intermediate_folder, exist_ok=True)
    
    # Process each unique TAN_NAME only once
    processed_files = []
    for tan_name, tan_data in tan_groups.items():
        try:
            # Create unique filename for each TAN
            tan_output_file = os.path.join(
                intermediate_folder, 
                f"section_{section_code}_shipment_{shipment_name}_tan_{tan_name}.xlsx"
            )
            
            # Process individual TAN only once
            if process_single_tan_unique(tan_name, tan_data, pdf_folder, tan_output_file):
                processed_files.append(tan_output_file)
                
        except Exception as e:
            print(f" Error processing TAN {tan_name}: {e}")
            continue
    
    # Step 4: Combine all processed files
    if processed_files:
        final_output_file = f"final_section_{section_code}_shipment_{shipment_name}_combined.xlsx"
        combine_processed_files(processed_files, final_output_file)
        return final_output_file
    
    return None

def group_data_by_tan(curated_df: pd.DataFrame) -> dict:
    """Group curated data by TAN_NAME and aggregate all keyphrases"""
    
    tan_groups = {}
    
    for index, row in curated_df.iterrows():
        try:
            # Safely get TAN_NAME
            tan_name = str(row.get('TAN_NAME', 'unknown')).strip()
            if not tan_name or tan_name == 'nan':
                continue
            
            # Get keyphrases from curated data - check multiple possible columns
            keyphrases = []
            for col in ['CTH_TERMS', 'CTH', 'terms_list', 'keyphrases']:
                if col in row and pd.notna(row[col]):
                    kp_value = str(row[col])
                    if kp_value and kp_value != 'nan':
                        keyphrases = [kp.strip() for kp in kp_value.replace(';', ',').split(',') if kp.strip()]
                        break
            
            # Add to group
            if tan_name not in tan_groups:
                tan_groups[tan_name] = {
                    'keyphrases': set(),
                    'row_data': row
                }
            
            # Add keyphrases to the set (automatically handles duplicates)
            tan_groups[tan_name]['keyphrases'].update(keyphrases)
            
        except Exception as e:
            print(f" Error processing row {index}: {e}")
            continue
    
    # Convert sets back to lists
    for tan_name in tan_groups:
        tan_groups[tan_name]['keyphrases'] = list(tan_groups[tan_name]['keyphrases'])
    
    return tan_groups

def process_single_tan_unique(tan_name: str, tan_data: dict, pdf_folder: str, output_file: str) -> bool:
    """Process single TAN only once with all aggregated keyphrases"""
    
    try:
        keyphrases = tan_data['keyphrases']
        
        if not keyphrases:
            print(f" No keyphrases found for TAN: {tan_name}")
            return False
        
        print(f" Processing TAN: {tan_name} with {len(keyphrases)} keyphrases")
        
        # Look for subfolder with TAN_NAME
        tan_subfolder = os.path.join(pdf_folder, tan_name)
        
        if not os.path.exists(tan_subfolder):
            # Try case-insensitive search
            for item in os.listdir(pdf_folder):
                item_path = os.path.join(pdf_folder, item)
                if os.path.isdir(item_path) and item.lower() == tan_name.lower():
                    tan_subfolder = item_path
                    break
            else:
                print(f" No subfolder found for TAN: {tan_name}")
                return False
        
        # Find PDF file with pattern {tan_name}.article.*.pdf
        pdf_pattern = os.path.join(tan_subfolder, f"{tan_name}.article.*.pdf")
        pdf_files = glob.glob(pdf_pattern)
        
        if not pdf_files:
            # Try case-insensitive pattern
            pdf_pattern_ci = os.path.join(tan_subfolder, "*.pdf")
            all_pdfs = glob.glob(pdf_pattern_ci)
            pdf_files = [f for f in all_pdfs if f"{tan_name}.article.".lower() in os.path.basename(f).lower()]
        
        if not pdf_files:
            print(f" No PDF found for TAN: {tan_name} in {tan_subfolder}")
            return False
        
        # Process PDF ONLY ONCE and extract sentences with all keyphrases 
        sentences_data = []
        pdf_file = pdf_files[0]  # Use first PDF file found
        
        try:
            # Use existing PDF extraction
            from pdf_text_extraction.main_pdf_content import get_pdf_text_main
            import segments_list as doc_lists
            
            print(f"Extracting content from: {os.path.basename(pdf_file)}")
            
            # Extract PDF content ONCE with error handling
            try:
                pdf_content, total_pages, article_size = get_pdf_text_main(
                    pdf_file, tan_name, doc_lists.title_list, "Large", 1
                )
                
                # Ensure pdf_content is a dictionary
                if not isinstance(pdf_content, dict):
                    if isinstance(pdf_content, str):
                        all_text = pdf_content
                    else:
                        all_text = str(pdf_content) if pdf_content else ""
                else:
                    # Handle dictionary content as before
                    all_text = ""
                    for section_name, section_content in pdf_content.items():
                        if isinstance(section_content, str):
                            all_text += section_content + " "
                        elif isinstance(section_content, dict):
                            for subsection, text in section_content.items():
                                if isinstance(text, str):
                                    all_text += text + " "
                                    
            except Exception as e:
                print(f" Error in PDF extraction: {e}")
                all_text = ""
            
            # Clean the text - remove line breaks and normalize whitespace
            all_text = clean_text_for_sentences(all_text)
            
            # Split into sentences
            sentences = [s.strip() for s in all_text.split('.') if len(s.strip()) > 20]
            
            # Process sentences - CREATE SEPARATE ROW FOR EACH TERM FOUND
            for sentence in sentences:
                # Clean sentence further
                sentence = clean_sentence(sentence)
                
                # Find actual keyphrases in sentence
                found_terms = []
                for keyphrase in keyphrases:
                    if isinstance(keyphrase, str) and keyphrase.lower() in sentence.lower():
                        found_terms.append(keyphrase)
                
                # Create SEPARATE ROW for each term found in the sentence
                for term in found_terms:
                    sentences_data.append({
                        'TAN_NAME': tan_name,
                        'sentence': sentence,
                        'term_found': term,  # Single term per row
                        'pdf_file': os.path.basename(pdf_file)
                    })
                    
        except Exception as e:
            print(f" Error extracting PDF content for {pdf_file}: {e}")
            return False
        
        # Save to Excel
        if sentences_data:
            df = pd.DataFrame(sentences_data)
            df.to_excel(output_file, index=False)
            print(f" Processed TAN {tan_name}: {len(sentences_data)} sentence-term pairs")
            return True
        else:
            print(f" No sentences with keyphrases found for TAN: {tan_name}")
            
    except Exception as e:
        print(f" Error processing TAN {tan_name}: {e}")
    
    return False

def clean_text_for_sentences(text: str) -> str:
    """Clean text by removing line breaks and normalizing whitespace"""
    if not isinstance(text, str):
        return ""
    
    # Remove line breaks and replace with spaces
    text = text.replace('\n', ' ').replace('\r', ' ')
    
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters that might interfere with sentence splitting
    text = text.replace('\t', ' ').replace('\f', ' ').replace('\v', ' ')
    
    return text.strip()

def clean_sentence(sentence: str) -> str:
    """Clean individual sentence"""
    if not isinstance(sentence, str):
        return ""
    
    # Remove any remaining line breaks
    sentence = sentence.replace('\n', ' ').replace('\r', ' ')
    
    # Normalize whitespace
    sentence = re.sub(r'\s+', ' ', sentence)
    
    # Remove leading/trailing whitespace
    sentence = sentence.strip()
    
    # Remove sentences that are too short or contain mostly special characters
    if len(sentence) < 20:
        return ""
    
    return sentence

def combine_processed_files(processed_files: list, output_file: str) -> bool:
    """Combine all processed TAN files into one final file"""
    
    try:
        all_data = []
        total_sentences = 0
        
        for file_path in processed_files:
            if os.path.exists(file_path):
                df = pd.read_excel(file_path)
                
                # Clean sentences in the dataframe
                if 'sentence' in df.columns:
                    df['sentence'] = df['sentence'].apply(clean_sentence)
                    # Remove empty sentences
                    df = df[df['sentence'].str.len() > 0]
                
                all_data.append(df)
                total_sentences += len(df)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Final cleanup
            if 'sentence' in combined_df.columns:
                combined_df['sentence'] = combined_df['sentence'].apply(clean_sentence)
                combined_df = combined_df[combined_df['sentence'].str.len() > 0]
            
            combined_df.to_excel(output_file, index=False)
            print(f" Combined {len(processed_files)} files into {output_file}")
            print(f" Total sentence-term pairs: {len(combined_df)}")
            return True
            
    except Exception as e:
        print(f" Error combining files: {e}")
    
    return False

if __name__ == "__main__":
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized\curated_data_sample"
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized\utility_data\text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"
    
    result = main_processing_pipeline(section_code, curator_data_folder, pdf_folder, abbr_full_form_excel)
    
    if result:
        print(f" Pipeline completed successfully: {result}")
    else:
        print(" Pipeline failed")
