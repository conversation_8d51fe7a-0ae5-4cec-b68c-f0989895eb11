import re
import ast
import time
import pandas as pd
import inflect
from tqdm import tqdm
from typing import List, Dict, Set, Optional
import os
import glob

from data_preprocessor_v1 import SegmentPreprocessor
from section_weighted_extractor import BasicKeyphraseExtractor
from noisy_sentences_remover_v1 import NoisySentenceRemover
from predict_module import predict_keyphrases

import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="transformers")
warnings.filterwarnings("ignore", category=FutureWarning, module="torch")

def load_curated_data(curator_data_folder: str, section_code: str) -> pd.DataFrame:
    """Load REAL curated data from Excel files"""
    
    # Look for Excel files in the curator data folder
    excel_files = glob.glob(os.path.join(curator_data_folder, "*.xlsx"))
    print(f"{excel_files =}")
    if not excel_files:
        print(f" No Excel files found in {curator_data_folder}")
        return pd.DataFrame()
    
    all_data = []
    for excel_file in excel_files:
        try:
            df = pd.read_excel(excel_file)
            
            # Filter by section code if column exists
            if 'SECTION_CODE' in df.columns:
                df = df[df['SECTION_CODE'].astype(str) == str(section_code)]
            
            if not df.empty:
                all_data.append(df)
                print(f" Loaded {len(df)} records from {os.path.basename(excel_file)}")
                
        except Exception as e:
            print(f" Error loading {excel_file}: {e}")
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f" Total curated records: {len(combined_df)}")
        return combined_df
    
    return pd.DataFrame()

def extract_keyphrases_from_curated_data(df: pd.DataFrame) -> List[str]:
    """PRODUCTION: No keyphrases available in curated data - return empty list"""
    print("PRODUCTION MODE: No keyphrases extracted from curated data")
    return []

def create_sample_pdf_data(pdf_folder: str, keyphrases: List[str]) -> pd.DataFrame:
    """Create sample PDF data for testing"""
    
    # Get shipment number from folder path
    shipment = pdf_folder.split('\\')[-1] if '\\' in pdf_folder else pdf_folder.split('/')[-1]
    
    # Create sample data with keyphrases
    sample_sentences = []
    
    for i, keyphrase in enumerate(keyphrases[:20]):  # Limit to first 20 keyphrases
        # Create sample sentences containing the keyphrase
        sentences = [
            f"The study investigated {keyphrase} and its applications in chemical processes.",
            f"Results showed that {keyphrase} plays a crucial role in the reaction mechanism.",
            f"The {keyphrase} was analyzed using advanced spectroscopic techniques.",
            f"Our findings demonstrate the importance of {keyphrase} in industrial applications."
        ]
        
        for j, sentence in enumerate(sentences):
            sample_sentences.append({
                'pdf_file': f'sample_{i}_{j}.pdf',
                'section_type': ['title', 'abstract', 'results', 'conclusion'][j],
                'sentence': sentence,
                'keyphrases_found': keyphrase,
                'has_keyphrase': True,
                'TAN_NAME': f'TAN_{shipment}_{i}_{j}',
                'shipment': shipment
            })
    
    return pd.DataFrame(sample_sentences)

def main_processing_pipeline(section_code: str, curator_data_folder: str,
                        pdf_folder: str, abbr_full_form_excel: str,
                        shipment_name: Optional[str] = None) -> Optional[str]:
    """Main processing pipeline for dataset creation with unique naming"""
    
    print("="*80)
    print("MAIN PROCESSING PIPELINE")
    print("="*80)
    
    start_time = time.time()
    
    # Extract shipment name if not provided
    if not shipment_name:
        shipment_name = os.path.basename(pdf_folder.rstrip('\\').rstrip('/'))
    
    # Step 1: Load REAL curated data (not sample data)
    print("Step 1: Loading REAL curated data...")
    curated_df = load_curated_data(curator_data_folder, section_code)
    
    if curated_df.empty:
        print(" No curated data found!")
        return None
    
    print(f" Loaded {len(curated_df)} curated records")
    
    # Step 2: Group by TAN_NAME and aggregate keyphrases
    print("Step 2: Grouping data by TAN_NAME...")
    tan_groups = group_data_by_tan(curated_df)
    print(f" Found {len(tan_groups)} unique TANs")
    
    # Step 3: Process each unique TAN only once
    print("Step 3: Processing PDFs...")
    
    # Create unique intermediate folder
    intermediate_folder = f"intermediate_section_{section_code}_shipment_{shipment_name}"
    os.makedirs(intermediate_folder, exist_ok=True)
    
    # Process each unique TAN_NAME only once
    processed_files = []
    for tan_name, tan_data in tan_groups.items():
        try:
            # Create unique filename for each TAN
            tan_output_file = os.path.join(
                intermediate_folder, 
                f"section_{section_code}_shipment_{shipment_name}_tan_{tan_name}.xlsx"
            )
            
            # Process individual TAN only once - PRODUCTION VERSION
            if process_single_tan_production(tan_name, tan_data, pdf_folder, tan_output_file):
                processed_files.append(tan_output_file)
                
        except Exception as e:
            print(f" Error processing TAN {tan_name}: {e}")
            continue
    
    # Step 4: Combine all processed files
    if processed_files:
        final_output_file = f"final_section_{section_code}_shipment_{shipment_name}_combined.xlsx"
        combine_processed_files(processed_files, final_output_file)
        return final_output_file
    
    return None

def group_data_by_tan(curated_df: pd.DataFrame) -> dict:
    """PRODUCTION: Group curated data by TAN_NAME without keyphrases"""

    tan_groups = {}

    for index, row in curated_df.iterrows():
        try:
            # Safely get TAN_NAME
            tan_name = str(row.get('TAN_NAME', 'unknown')).strip()
            if not tan_name or tan_name == 'nan':
                continue

            # Add to group - PRODUCTION: No keyphrases
            if tan_name not in tan_groups:
                tan_groups[tan_name] = {
                    'keyphrases': [],  # Empty list for production
                    'row_data': row
                }

        except Exception as e:
            print(f" Error processing row {index}: {e}")
            continue

    return tan_groups

def process_single_tan_production(tan_name: str, tan_data: dict, pdf_folder: str, output_file: str) -> bool:
    """PRODUCTION: Process single TAN without keyphrases using quality-based filtering"""

    try:
        print(f" Processing TAN: {tan_name} (PRODUCTION MODE - no keyphrases)")
        
        # Look for subfolder with TAN_NAME
        tan_subfolder = os.path.join(pdf_folder, tan_name)

        if not os.path.exists(tan_subfolder):
            # Try case-insensitive search
            for item in os.listdir(pdf_folder):
                item_path = os.path.join(pdf_folder, item)
                if os.path.isdir(item_path) and item.lower() == tan_name.lower():
                    tan_subfolder = item_path
                    break
            else:
                print(f" No subfolder found for TAN: {tan_name}")
                return False

        # Find PDF file with pattern {tan_name}.article.*.pdf
        pdf_pattern = os.path.join(tan_subfolder, f"{tan_name}.article.*.pdf")
        pdf_files = glob.glob(pdf_pattern)

        if not pdf_files:
            # Try case-insensitive pattern
            pdf_pattern_ci = os.path.join(tan_subfolder, "*.pdf")
            all_pdfs = glob.glob(pdf_pattern_ci)
            pdf_files = [f for f in all_pdfs if f"{tan_name}.article.".lower() in os.path.basename(f).lower()]

        if not pdf_files:
            print(f" No PDF found for TAN: {tan_name} in {tan_subfolder}")
            return False

        # PRODUCTION: Process PDF and extract quality sentences using imported modules
        sentences_data = []
        pdf_file = pdf_files[0]  # Use first PDF file found

        # Initialize processing modules
        segment_preprocessor = SegmentPreprocessor()
        keyphrase_extractor = BasicKeyphraseExtractor()
        noise_remover = NoisySentenceRemover(keyphrases=[])  # Empty keyphrases for production
        
        try:
            # Use existing PDF extraction
            from pdf_text_extraction.main_pdf_content import get_pdf_text_main
            import segments_list as doc_lists

            print(f"Extracting content from: {os.path.basename(pdf_file)}")

            # Extract PDF content ONCE with error handling
            try:
                pdf_content, _, _ = get_pdf_text_main(
                    pdf_file, tan_name, doc_lists.title_list, "Large", 1
                )

                # PRODUCTION: Process segmented content using imported modules
                if isinstance(pdf_content, dict):
                    # Process each segment separately
                    for segment_name, segment_content in pdf_content.items():
                        if isinstance(segment_content, str) and segment_content.strip():
                            # Clean and preprocess the segment
                            cleaned_text = segment_preprocessor.clean_text(segment_content)

                            # Extract sentences from the segment
                            segment_sentences = segment_preprocessor.extract_sentences(cleaned_text)

                            # Filter out noisy sentences
                            good_sentences, _ = noise_remover.process_sentences(
                                [{'sentence': s} for s in segment_sentences]
                            )

                            # Add quality sentences to results
                            for sentence_data in good_sentences:
                                sentences_data.append({
                                    'TAN_NAME': tan_name,
                                    'sentence': sentence_data['sentence'],
                                    'segment': segment_name,
                                    'pdf_file': os.path.basename(pdf_file),
                                    'quality_score': sentence_data.get('quality_score', 0.5)
                                })

                        elif isinstance(segment_content, dict):
                            # Handle nested segment structure
                            for subsection, text in segment_content.items():
                                if isinstance(text, str) and text.strip():
                                    cleaned_text = segment_preprocessor.clean_text(text)
                                    segment_sentences = segment_preprocessor.extract_sentences(cleaned_text)

                                    good_sentences, _ = noise_remover.process_sentences(
                                        [{'sentence': s} for s in segment_sentences]
                                    )

                                    for sentence_data in good_sentences:
                                        sentences_data.append({
                                            'TAN_NAME': tan_name,
                                            'sentence': sentence_data['sentence'],
                                            'segment': f"{segment_name}_{subsection}",
                                            'pdf_file': os.path.basename(pdf_file),
                                            'quality_score': sentence_data.get('quality_score', 0.5)
                                        })
                else:
                    # Handle non-dictionary content
                    text_content = str(pdf_content) if pdf_content else ""
                    if text_content.strip():
                        cleaned_text = segment_preprocessor.clean_text(text_content)
                        segment_sentences = segment_preprocessor.extract_sentences(cleaned_text)

                        good_sentences, _ = noise_remover.process_sentences(
                            [{'sentence': s} for s in segment_sentences]
                        )

                        for sentence_data in good_sentences:
                            sentences_data.append({
                                'TAN_NAME': tan_name,
                                'sentence': sentence_data['sentence'],
                                'segment': 'unknown',
                                'pdf_file': os.path.basename(pdf_file),
                                'quality_score': sentence_data.get('quality_score', 0.5)
                            })

            except Exception as e:
                print(f" Error in PDF extraction: {e}")
                return False
                    
        except Exception as e:
            print(f" Error extracting PDF content for {pdf_file}: {e}")
            return False
        
        # PRODUCTION: Optionally predict keyphrases from quality sentences
        if sentences_data:
            try:
                # Extract sentences for keyphrase prediction
                sentences_for_prediction = [item['sentence'] for item in sentences_data]

                # Predict keyphrases using the trained model
                print(f" Predicting keyphrases for {len(sentences_for_prediction)} sentences...")
                predicted_keyphrases_per_sentence = predict_keyphrases(sentences_for_prediction)

                # Add predicted keyphrases to sentence data
                for i, sentence_data in enumerate(sentences_data):
                    if i < len(predicted_keyphrases_per_sentence):
                        sentence_data['predicted_keyphrases'] = predicted_keyphrases_per_sentence[i]
                    else:
                        sentence_data['predicted_keyphrases'] = []

                print(f" Keyphrase prediction completed")

            except Exception as e:
                print(f" Warning: Keyphrase prediction failed: {e}")
                # Add empty keyphrases if prediction fails
                for sentence_data in sentences_data:
                    sentence_data['predicted_keyphrases'] = []

        # Save to Excel
        if sentences_data:
            df = pd.DataFrame(sentences_data)
            df.to_excel(output_file, index=False)
            print(f" Processed TAN {tan_name}: {len(sentences_data)} quality sentences")
            return True
        else:
            print(f" No quality sentences found for TAN: {tan_name}")

    except Exception as e:
        print(f" Error processing TAN {tan_name}: {e}")

    return False

def clean_text_for_sentences(text: str) -> str:
    """Clean text by removing line breaks and normalizing whitespace"""
    if not isinstance(text, str):
        return ""
    
    # Remove line breaks and replace with spaces
    text = text.replace('\n', ' ').replace('\r', ' ')
    
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters that might interfere with sentence splitting
    text = text.replace('\t', ' ').replace('\f', ' ').replace('\v', ' ')
    
    return text.strip()

def clean_sentence(sentence: str) -> str:
    """Clean individual sentence"""
    if not isinstance(sentence, str):
        return ""
    
    # Remove any remaining line breaks
    sentence = sentence.replace('\n', ' ').replace('\r', ' ')
    
    # Normalize whitespace
    sentence = re.sub(r'\s+', ' ', sentence)
    
    # Remove leading/trailing whitespace
    sentence = sentence.strip()
    
    # Remove sentences that are too short or contain mostly special characters
    if len(sentence) < 20:
        return ""
    
    return sentence

def combine_processed_files(processed_files: list, output_file: str) -> bool:
    """Combine all processed TAN files into one final file"""
    
    try:
        all_data = []
        total_sentences = 0
        
        for file_path in processed_files:
            if os.path.exists(file_path):
                df = pd.read_excel(file_path)
                
                # Clean sentences in the dataframe
                if 'sentence' in df.columns:
                    df['sentence'] = df['sentence'].apply(clean_sentence)
                    # Remove empty sentences
                    df = df[df['sentence'].str.len() > 0]
                
                all_data.append(df)
                total_sentences += len(df)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Final cleanup
            if 'sentence' in combined_df.columns:
                combined_df['sentence'] = combined_df['sentence'].apply(clean_sentence)
                combined_df = combined_df[combined_df['sentence'].str.len() > 0]
            
            combined_df.to_excel(output_file, index=False)
            print(f" Combined {len(processed_files)} files into {output_file}")
            print(f" Total quality sentences: {len(combined_df)}")
            return True
            
    except Exception as e:
        print(f" Error combining files: {e}")
    
    return False

def production_pipeline(section_code: str, curator_data_folder: str,
                    pdf_folder: str, abbr_full_form_excel: str,
                    shipment_name: Optional[str] = None, predict_keyphrases_flag: bool = True) -> Optional[str]:
    """
    PRODUCTION PIPELINE: Process PDFs without relying on curated keyphrases

    Args:
        section_code: Section code to filter by
        curator_data_folder: Path to curator data folder
        pdf_folder: Path to PDF folder
        abbr_full_form_excel: Path to abbreviations Excel file
        shipment_name: Optional shipment name
        predict_keyphrases_flag: Whether to predict keyphrases using ML model

    Returns:
        Path to output file or None if failed
    """

    print("="*80)
    print("PRODUCTION PIPELINE - QUALITY-BASED SENTENCE EXTRACTION")
    print("="*80)

    start_time = time.time()

    # Extract shipment name if not provided
    if not shipment_name:
        shipment_name = os.path.basename(pdf_folder.rstrip('\\').rstrip('/'))

    # Step 1: Load curated data (for TAN names and metadata only)
    print("Step 1: Loading curated data for TAN metadata...")
    curated_df = load_curated_data(curator_data_folder, section_code)

    if curated_df.empty:
        print(" No curated data found!")
        return None

    print(f" Loaded {len(curated_df)} curated records")

    # Step 2: Group by TAN_NAME (no keyphrases)
    print("Step 2: Grouping data by TAN_NAME...")
    tan_groups = group_data_by_tan(curated_df)
    print(f" Found {len(tan_groups)} unique TANs")

    # Step 3: Process each unique TAN with quality-based filtering
    print("Step 3: Processing PDFs with quality-based filtering...")

    # Create unique intermediate folder
    intermediate_folder = f"production_section_{section_code}_shipment_{shipment_name}"
    os.makedirs(intermediate_folder, exist_ok=True)

    # Process each unique TAN_NAME
    processed_files = []
    for tan_name, tan_data in tan_groups.items():
        try:
            # Create unique filename for each TAN
            tan_output_file = os.path.join(
                intermediate_folder,
                f"production_section_{section_code}_shipment_{shipment_name}_tan_{tan_name}.xlsx"
            )

            # Process individual TAN with production method
            if process_single_tan_production(tan_name, tan_data, pdf_folder, tan_output_file):
                processed_files.append(tan_output_file)

        except Exception as e:
            print(f" Error processing TAN {tan_name}: {e}")
            continue

    # Step 4: Combine all processed files
    if processed_files:
        final_output_file = f"production_section_{section_code}_shipment_{shipment_name}_combined.xlsx"
        combine_processed_files(processed_files, final_output_file)

        end_time = time.time()
        print(f"\n Production pipeline completed in {end_time - start_time:.2f} seconds")
        print(f" Output file: {final_output_file}")

        return final_output_file

    return None


if __name__ == "__main__":
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\curated_data_sample"
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\utility_data\text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"

    # Use the new production pipeline
    result = production_pipeline(
        section_code=section_code,
        curator_data_folder=curator_data_folder,
        pdf_folder=pdf_folder,
        abbr_full_form_excel=abbr_full_form_excel,
        predict_keyphrases_flag=True
    )

    if result:
        print(f"\n Production pipeline completed successfully!")
        print(f" Output file: {result}")
    else:
        print("\n Production pipeline failed")
