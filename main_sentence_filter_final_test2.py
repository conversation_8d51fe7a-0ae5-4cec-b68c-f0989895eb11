import re
import ast
import time
import pandas as pd
import inflect
from tqdm import tqdm
from typing import List, Dict, Set, Optional
import os
import glob
from collections import defaultdict

from data_preprocessor_v1 import SegmentPreprocessor
# from section_weighted_extractor import BasicKeyphraseExtractor
from noisy_sentences_remover_v1 import NoisySentenceRemover
from predict_module import predict_keyphrases

# Import for semantic similarity
try:
    from sentence_transformers import SentenceTransformer, util
    SENTENCE_TRANSFORMER_AVAILABLE = True
except ImportError:
    print("Warning: sentence-transformers not available. Install with: pip install sentence-transformers")
    SENTENCE_TRANSFORMER_AVAILABLE = False

import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="transformers")


class RelevanceFilter:
    """Filter sentences based on relevance to title and abstract content"""

    def __init__(self, use_semantic_similarity: bool = True, config: dict = None):
        self.use_semantic_similarity = use_semantic_similarity and SENTENCE_TRANSFORMER_AVAILABLE
        self.model = None

        # Load configuration
        if config is None:
            try:
                from relevance_config import get_config
                self.config = get_config('moderate')
            except ImportError:
                # Fallback configuration
                self.config = {
                    'min_relevance_score': 0.1,
                    'max_sentences_per_segment': {
                        'title': 3, 'abstract': 15, 'results': 25, 'conclusion': 10,
                        'material_methods': 20, 'experimental_procedures': 20, 'default': 20
                    },  #'introduction': 6, 
                    'keyword_weight': 0.2,
                    'semantic_weight': 0.2,
                    'strict_filtering_multiplier': 1.2
                }
        else:
            self.config = config

        if self.use_semantic_similarity:
            try:
                # Use a lightweight model for semantic similarity
                model_name = self.config.get('semantic_model', 'all-MiniLM-L6-v2')
                self.model = SentenceTransformer(model_name)
                print(f"Loaded semantic similarity model: {model_name}")
            except Exception as e:
                print(f"Warning: Could not load semantic model: {e}")
                self.use_semantic_similarity = False

    def extract_key_terms(self, text: str) -> Set[str]:
        """Extract key terms from text using simple keyword extraction"""
        if not text:
            return set()

        # Simple keyword extraction - remove common words and get meaningful terms
        import re

        # Clean text and split into words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

        # Common stop words to filter out
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'was', 'were', 'been', 'have', 'has', 'had', 'will', 'would',
            'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'are', 'is',
            'also', 'however', 'therefore', 'thus', 'furthermore', 'moreover',
            'study', 'studies', 'research', 'analysis', 'results', 'conclusion',
            'method', 'methods', 'data', 'using', 'used', 'showed', 'found'
        }

        # Filter out stop words and get meaningful terms
        key_terms = {word for word in words if word not in stop_words and len(word) > 3}

        return key_terms

    def calculate_keyword_overlap(self, sentence: str, reference_terms: Set[str]) -> float:
        """Calculate keyword overlap between sentence and reference terms"""
        if not reference_terms:
            return 0.0

        sentence_terms = self.extract_key_terms(sentence)
        if not sentence_terms:
            return 0.0

        # Calculate Jaccard similarity
        intersection = len(sentence_terms.intersection(reference_terms))
        union = len(sentence_terms.union(reference_terms))

        return intersection / union if union > 0 else 0.0

    def calculate_semantic_similarity(self, sentence: str, reference_text: str) -> float:
        """Calculate semantic similarity using sentence transformers"""
        if not self.use_semantic_similarity or not self.model:
            return 0.0

        try:
            # Encode sentences
            sentence_embedding = self.model.encode([sentence])
            reference_embedding = self.model.encode([reference_text])

            # Calculate cosine similarity
            similarity = util.cos_sim(sentence_embedding, reference_embedding)[0][0].item()
            return similarity

        except Exception as e:
            print(f"Warning: Semantic similarity calculation failed: {e}")
            return 0.0

    def calculate_relevance_score(self, sentence: str, title_text: str, abstract_text: str) -> float:
        """Calculate overall relevance score for a sentence"""

        # Combine title and abstract for reference
        reference_text = f"{title_text} {abstract_text}".strip()
        reference_terms = self.extract_key_terms(reference_text)

        # Calculate keyword overlap score
        keyword_score = self.calculate_keyword_overlap(sentence, reference_terms)

        # Calculate semantic similarity score if available
        if self.use_semantic_similarity:
            semantic_score = self.calculate_semantic_similarity(sentence, reference_text)
            # Combine both scores with weights
            relevance_score = 0.4 * keyword_score + 0.6 * semantic_score
        else:
            # Use only keyword score
            relevance_score = keyword_score

        return relevance_score

    def filter_sentences_by_relevance(self, sentences_data: List[Dict],
                                    title_text: str, abstract_text: str,
                                    min_relevance_score: Optional[float] = None,
                                    max_sentences_per_segment: Optional[dict] = None) -> List[Dict]:
        """Filter sentences based on relevance to title and abstract"""

        if not sentences_data:
            return []

        # Use configuration defaults if not provided
        if min_relevance_score is None:
            min_relevance_score = self.config.get('min_relevance_score', 0.1)

        if max_sentences_per_segment is None:
            max_sentences_per_segment = self.config.get('max_sentences_per_segment', {})

        # Group sentences by segment
        segment_groups = defaultdict(list)
        for sentence_data in sentences_data:
            segment = sentence_data.get('segment', 'unknown')
            segment_groups[segment].append(sentence_data)

        filtered_sentences = []

        # Get priority segments from config
        priority_segments = self.config.get('priority_segments',
                                        ['title', 'abstract', 'results', 'conclusion', 'introduction', 'material_methods'])

        for segment in priority_segments:
            # Find matching segments (case-insensitive, partial match)
            matching_segments = [s for s in segment_groups.keys()
                            if segment.lower() in s.lower()]

            for seg_name in matching_segments:
                segment_sentences = segment_groups[seg_name]

                # Calculate relevance scores
                for sentence_data in segment_sentences:
                    sentence = sentence_data.get('sentence', '')
                    relevance_score = self.calculate_relevance_score(sentence, title_text, abstract_text)
                    sentence_data['relevance_score'] = relevance_score

                # Filter by minimum relevance score
                relevant_sentences = [s for s in segment_sentences
                                    if s.get('relevance_score', 0) >= min_relevance_score]

                # Sort by relevance score and limit count
                relevant_sentences.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

                # Get segment-specific limit
                max_count = max_sentences_per_segment.get(segment, max_sentences_per_segment.get('default', 3))

                filtered_sentences.extend(relevant_sentences[:max_count])

                # Remove processed segments
                if seg_name in segment_groups:
                    del segment_groups[seg_name]

        # Process remaining segments with stricter filtering
        strict_multiplier = self.config.get('strict_filtering_multiplier', 2.0)
        default_limit = max_sentences_per_segment.get('default', 3)

        for segment, segment_sentences in segment_groups.items():
            # Calculate relevance scores
            for sentence_data in segment_sentences:
                sentence = sentence_data.get('sentence', '')
                relevance_score = self.calculate_relevance_score(sentence, title_text, abstract_text)
                sentence_data['relevance_score'] = relevance_score

            # Apply stricter filtering for non-priority segments
            strict_threshold = min_relevance_score * strict_multiplier
            relevant_sentences = [s for s in segment_sentences
                                if s.get('relevance_score', 0) >= strict_threshold]

            # Sort and limit
            relevant_sentences.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            filtered_sentences.extend(relevant_sentences[:default_limit // 2])

        return filtered_sentences
warnings.filterwarnings("ignore", category=FutureWarning, module="torch")

def load_curated_data(curator_data_folder: str, section_code: str) -> pd.DataFrame:
    """Load REAL curated data from Excel files"""
    
    # Look for Excel files in the curator data folder
    excel_files = glob.glob(os.path.join(curator_data_folder, "*.xlsx"))
    print(f"{excel_files =}")
    if not excel_files:
        print(f" No Excel files found in {curator_data_folder}")
        return pd.DataFrame()
    
    all_data = []
    for excel_file in excel_files:
        try:
            df = pd.read_excel(excel_file)
            
            # Filter by section code if column exists
            if 'SECTION_CODE' in df.columns:
                df = df[df['SECTION_CODE'].astype(str) == str(section_code)]
            
            if not df.empty:
                all_data.append(df)
                print(f" Loaded {len(df)} records from {os.path.basename(excel_file)}")
                
        except Exception as e:
            print(f" Error loading {excel_file}: {e}")
    
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f" Total curated records: {len(combined_df)}")
        return combined_df
    
    return pd.DataFrame()

def extract_keyphrases_from_curated_data(df: pd.DataFrame) -> List[str]:
    """PRODUCTION: No keyphrases available in curated data - return empty list"""
    print("PRODUCTION MODE: No keyphrases extracted from curated data")
    return []

def create_sample_pdf_data(pdf_folder: str, keyphrases: List[str]) -> pd.DataFrame:
    """Create sample PDF data for testing"""
    
    # Get shipment number from folder path
    shipment = pdf_folder.split('\\')[-1] if '\\' in pdf_folder else pdf_folder.split('/')[-1]
    
    # Create sample data with keyphrases
    sample_sentences = []
    
    for i, keyphrase in enumerate(keyphrases[:20]):  # Limit to first 20 keyphrases
        # Create sample sentences containing the keyphrase
        sentences = [
            f"The study investigated {keyphrase} and its applications in chemical processes.",
            f"Results showed that {keyphrase} plays a crucial role in the reaction mechanism.",
            f"The {keyphrase} was analyzed using advanced spectroscopic techniques.",
            f"Our findings demonstrate the importance of {keyphrase} in industrial applications."
        ]
        
        for j, sentence in enumerate(sentences):
            sample_sentences.append({
                'pdf_file': f'sample_{i}_{j}.pdf',
                'section_type': ['title', 'abstract', 'results', 'conclusion'][j],
                'sentence': sentence,
                'keyphrases_found': keyphrase,
                'has_keyphrase': True,
                'TAN_NAME': f'TAN_{shipment}_{i}_{j}',
                'shipment': shipment
            })
    
    return pd.DataFrame(sample_sentences)

def main_processing_pipeline(section_code: str, curator_data_folder: str,
                        pdf_folder: str, abbr_full_form_excel: str,
                        shipment_name: Optional[str] = None) -> Optional[str]:
    """Main processing pipeline for dataset creation with unique naming"""
    
    print("="*80)
    print("MAIN PROCESSING PIPELINE")
    print("="*80)
    
    start_time = time.time()
    
    # Extract shipment name if not provided
    if not shipment_name:
        shipment_name = os.path.basename(pdf_folder.rstrip('\\').rstrip('/'))
    
    # Step 1: Load REAL curated data (not sample data)
    print("Step 1: Loading REAL curated data...")
    curated_df = load_curated_data(curator_data_folder, section_code)
    
    if curated_df.empty:
        print(" No curated data found!")
        return None
    
    print(f" Loaded {len(curated_df)} curated records")
    
    # Step 2: Group by TAN_NAME and aggregate keyphrases
    print("Step 2: Grouping data by TAN_NAME...")
    tan_groups = group_data_by_tan(curated_df)
    print(f" Found {len(tan_groups)} unique TANs")
    
    # Step 3: Process each unique TAN only once
    print("Step 3: Processing PDFs...")
    
    # Create unique intermediate folder
    intermediate_folder = f"intermediate_section_{section_code}_shipment_{shipment_name}"
    os.makedirs(intermediate_folder, exist_ok=True)
    
    # Process each unique TAN_NAME only once
    processed_files = []
    for tan_name, tan_data in tan_groups.items():
        try:
            # Create unique filename for each TAN
            tan_output_file = os.path.join(
                intermediate_folder, 
                f"section_{section_code}_shipment_{shipment_name}_tan_{tan_name}.xlsx"
            )
            
            # Process individual TAN only once - PRODUCTION VERSION
            if process_single_tan_production(tan_name, tan_data, pdf_folder, tan_output_file):
                processed_files.append(tan_output_file)
                
        except Exception as e:
            print(f" Error processing TAN {tan_name}: {e}")
            continue
    
    # Step 4: Combine all processed files
    if processed_files:
        final_output_file = f"final_section_{section_code}_shipment_{shipment_name}_combined.xlsx"
        combine_processed_files(processed_files, final_output_file)
        return final_output_file
    
    return None

def group_data_by_tan(curated_df: pd.DataFrame) -> dict:
    """PRODUCTION: Group curated data by TAN_NAME without keyphrases"""

    tan_groups = {}

    for index, row in curated_df.iterrows():
        try:
            # Safely get TAN_NAME
            tan_name = str(row.get('TAN_NAME', 'unknown')).strip()
            if not tan_name or tan_name == 'nan':
                continue

            # Add to group - PRODUCTION: No keyphrases
            if tan_name not in tan_groups:
                tan_groups[tan_name] = {
                    'keyphrases': [],  # Empty list for production
                    'row_data': row
                }

        except Exception as e:
            print(f" Error processing row {index}: {e}")
            continue

    return tan_groups

def process_single_tan_production(tan_name: str, tan_data: dict, pdf_folder: str, output_file: str) -> bool:
    """PRODUCTION: Process single TAN without keyphrases using quality-based filtering"""

    try:
        print(f" Processing TAN: {tan_name} (PRODUCTION MODE - no keyphrases)")
        
        # Look for subfolder with TAN_NAME
        tan_subfolder = os.path.join(pdf_folder, tan_name)

        if not os.path.exists(tan_subfolder):
            # Try case-insensitive search
            for item in os.listdir(pdf_folder):
                item_path = os.path.join(pdf_folder, item)
                if os.path.isdir(item_path) and item.lower() == tan_name.lower():
                    tan_subfolder = item_path
                    break
            else:
                print(f" No subfolder found for TAN: {tan_name}")
                return False

        # Find PDF file with pattern {tan_name}.article.*.pdf
        pdf_pattern = os.path.join(tan_subfolder, f"{tan_name}.article.*.pdf")
        pdf_files = glob.glob(pdf_pattern)

        if not pdf_files:
            # Try case-insensitive pattern
            pdf_pattern_ci = os.path.join(tan_subfolder, "*.pdf")
            all_pdfs = glob.glob(pdf_pattern_ci)
            pdf_files = [f for f in all_pdfs if f"{tan_name}.article.".lower() in os.path.basename(f).lower()]

        if not pdf_files:
            print(f" No PDF found for TAN: {tan_name} in {tan_subfolder}")
            return False

        # PRODUCTION: Process PDF and extract quality sentences using imported modules
        sentences_data = []
        pdf_file = pdf_files[0]  # Use first PDF file found

        # Initialize processing modules
        segment_preprocessor = SegmentPreprocessor()
        # keyphrase_extractor = BasicKeyphraseExtractor()
        noise_remover = NoisySentenceRemover(keyphrases=[])  # Empty keyphrases for production
        relevance_filter = RelevanceFilter(use_semantic_similarity=True)  # NEW: Relevance filtering
        
        try:
            # Use existing PDF extraction
            from pdf_text_extraction.main_pdf_content import get_pdf_text_main
            import segments_list as doc_lists

            print(f"Extracting content from: {os.path.basename(pdf_file)}")

            # Extract PDF content ONCE with error handling
            try:
                pdf_content, _, _ = get_pdf_text_main(
                    pdf_file, tan_name, doc_lists.title_list, "Large", 1
                )

                # PRODUCTION: Process segmented content with relevance filtering
                if isinstance(pdf_content, dict):
                    # Step 1: Extract title and abstract content first
                    title_text = ""
                    abstract_text = ""

                    # Find title and abstract segments
                    for segment_name, segment_content in pdf_content.items():
                        segment_lower = segment_name.lower()

                        if 'title' in segment_lower:
                            if isinstance(segment_content, str):
                                title_text += segment_content + " "
                            elif isinstance(segment_content, dict):
                                for text in segment_content.values():
                                    if isinstance(text, str):
                                        title_text += text + " "

                        elif 'abstract' in segment_lower:
                            if isinstance(segment_content, str):
                                abstract_text += segment_content + " "
                            elif isinstance(segment_content, dict):
                                for text in segment_content.values():
                                    if isinstance(text, str):
                                        abstract_text += text + " "

                    # Clean title and abstract
                    title_text = title_text.strip()
                    abstract_text = abstract_text.strip()

                    print(f"  Title length: {len(title_text)} chars")
                    print(f"  Abstract length: {len(abstract_text)} chars")

                    # Step 2: Process all segments and collect sentences
                    all_sentences_data = []

                    for segment_name, segment_content in pdf_content.items():
                        if isinstance(segment_content, str) and segment_content.strip():
                            # Clean and preprocess the segment
                            cleaned_text = segment_preprocessor.clean_text(segment_content)

                            # Extract sentences from the segment
                            segment_sentences = segment_preprocessor.extract_sentences(cleaned_text)

                            # Filter out noisy sentences
                            good_sentences, _ = noise_remover.process_sentences(
                                [{'sentence': s} for s in segment_sentences]
                            )

                            # Add quality sentences to collection
                            for sentence_data in good_sentences:
                                all_sentences_data.append({
                                    'TAN_NAME': tan_name,
                                    'sentence': sentence_data['sentence'],
                                    'segment': segment_name,
                                    'pdf_file': os.path.basename(pdf_file),
                                    'quality_score': sentence_data.get('quality_score', 0.5)
                                })

                        elif isinstance(segment_content, dict):
                            # Handle nested segment structure
                            for subsection, text in segment_content.items():
                                if isinstance(text, str) and text.strip():
                                    cleaned_text = segment_preprocessor.clean_text(text)
                                    segment_sentences = segment_preprocessor.extract_sentences(cleaned_text)

                                    good_sentences, _ = noise_remover.process_sentences(
                                        [{'sentence': s} for s in segment_sentences]
                                    )

                                    for sentence_data in good_sentences:
                                        all_sentences_data.append({
                                            'TAN_NAME': tan_name,
                                            'sentence': sentence_data['sentence'],
                                            'segment': f"{segment_name}_{subsection}",
                                            'pdf_file': os.path.basename(pdf_file),
                                            'quality_score': sentence_data.get('quality_score', 0.5)
                                        })

                    # Step 3: Apply relevance filtering
                    print(f"  Total sentences before relevance filtering: {len(all_sentences_data)}")

                    if title_text or abstract_text:
                        # Filter sentences based on relevance to title and abstract
                        # Uses configuration from relevance_config.py
                        sentences_data = relevance_filter.filter_sentences_by_relevance(
                            all_sentences_data,
                            title_text,
                            abstract_text
                        )
                        print(f"  Sentences after relevance filtering: {len(sentences_data)}")

                        # Show filtering statistics
                        if sentences_data:
                            avg_relevance = sum(s.get('relevance_score', 0) for s in sentences_data) / len(sentences_data)
                            print(f"  Average relevance score: {avg_relevance:.3f}")

                            # Show segment distribution
                            segment_counts = defaultdict(int)
                            for s in sentences_data:
                                segment_counts[s.get('segment', 'unknown')] += 1
                            print(f"  Segment distribution: {dict(segment_counts)}")
                    else:
                        # If no title/abstract found, use basic filtering
                        print("  Warning: No title/abstract found, using basic filtering")
                        sentences_data = all_sentences_data[:50]  # Limit to first 50 sentences
                else:
                    # Handle non-dictionary content
                    text_content = str(pdf_content) if pdf_content else ""
                    if text_content.strip():
                        cleaned_text = segment_preprocessor.clean_text(text_content)
                        segment_sentences = segment_preprocessor.extract_sentences(cleaned_text)

                        good_sentences, _ = noise_remover.process_sentences(
                            [{'sentence': s} for s in segment_sentences]
                        )

                        for sentence_data in good_sentences:
                            sentences_data.append({
                                'TAN_NAME': tan_name,
                                'sentence': sentence_data['sentence'],
                                'segment': 'unknown',
                                'pdf_file': os.path.basename(pdf_file),
                                'quality_score': sentence_data.get('quality_score', 0.5)
                            })

            except Exception as e:
                print(f" Error in PDF extraction: {e}")
                return False
                    
        except Exception as e:
            print(f" Error extracting PDF content for {pdf_file}: {e}")
            return False
        
        # PRODUCTION: Optionally predict keyphrases from quality sentences
        if sentences_data:
            try:
                # Extract sentences for keyphrase prediction
                sentences_for_prediction = [item['sentence'] for item in sentences_data]

                # Predict keyphrases using the trained model
                print(f" Predicting keyphrases for {len(sentences_for_prediction)} sentences...")
                predicted_keyphrases_per_sentence = predict_keyphrases(sentences_for_prediction)

                # Add predicted keyphrases to sentence data
                for i, sentence_data in enumerate(sentences_data):
                    if i < len(predicted_keyphrases_per_sentence):
                        sentence_data['predicted_keyphrases'] = predicted_keyphrases_per_sentence[i]
                    else:
                        sentence_data['predicted_keyphrases'] = []

                print(f" Keyphrase prediction completed")

            except Exception as e:
                print(f" Warning: Keyphrase prediction failed: {e}")
                # Add empty keyphrases if prediction fails
                for sentence_data in sentences_data:
                    sentence_data['predicted_keyphrases'] = []

        # Save to Excel
        if sentences_data:
            df = pd.DataFrame(sentences_data)
            df.to_excel(output_file, index=False)
            print(f" Processed TAN {tan_name}: {len(sentences_data)} quality sentences")
            return True
        else:
            print(f" No quality sentences found for TAN: {tan_name}")

    except Exception as e:
        print(f" Error processing TAN {tan_name}: {e}")

    return False

def clean_text_for_sentences(text: str) -> str:
    """Clean text by removing line breaks and normalizing whitespace"""
    if not isinstance(text, str):
        return ""
    
    # Remove line breaks and replace with spaces
    text = text.replace("-n", "").replace('\n', ' ').replace('\r', ' ')
    
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters that might interfere with sentence splitting
    text = text.replace('\t', ' ').replace('\f', ' ').replace('\v', ' ')
    
    return text.strip()

def clean_sentence(sentence: str) -> str:
    """Clean individual sentence"""
    if not isinstance(sentence, str):
        return ""
    
    # Remove any remaining line breaks
    sentence = sentence.replace('\n', ' ').replace('\r', ' ')
    
    # Normalize whitespace
    sentence = re.sub(r'\s+', ' ', sentence)
    
    # Remove leading/trailing whitespace
    sentence = sentence.strip()
    
    # Remove sentences that are too short or contain mostly special characters
    if len(sentence) < 20:
        return ""
    
    return sentence

def combine_processed_files(processed_files: list, output_file: str) -> bool:
    """Combine all processed TAN files into one final file"""
    
    try:
        all_data = []
        total_sentences = 0
        
        for file_path in processed_files:
            if os.path.exists(file_path):
                df = pd.read_excel(file_path)
                
                # Clean sentences in the dataframe
                if 'sentence' in df.columns:
                    df['sentence'] = df['sentence'].apply(clean_sentence)
                    # Remove empty sentences
                    df = df[df['sentence'].str.len() > 0]
                
                all_data.append(df)
                total_sentences += len(df)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Final cleanup
            if 'sentence' in combined_df.columns:
                combined_df['sentence'] = combined_df['sentence'].apply(clean_sentence)
                combined_df = combined_df[combined_df['sentence'].str.len() > 0]
            
            combined_df.to_excel(output_file, index=False)
            print(f" Combined {len(processed_files)} files into {output_file}")
            print(f" Total quality sentences: {len(combined_df)}")
            return True
            
    except Exception as e:
        print(f" Error combining files: {e}")
    
    return False

def production_pipeline(section_code: str, curator_data_folder: str,
                    pdf_folder: str, abbr_full_form_excel: str,
                    shipment_name: Optional[str] = None, predict_keyphrases_flag: bool = True) -> Optional[str]:
    """
    PRODUCTION PIPELINE: Process PDFs without relying on curated keyphrases

    Args:
        section_code: Section code to filter by
        curator_data_folder: Path to curator data folder
        pdf_folder: Path to PDF folder
        abbr_full_form_excel: Path to abbreviations Excel file
        shipment_name: Optional shipment name
        predict_keyphrases_flag: Whether to predict keyphrases using ML model

    Returns:
        Path to output file or None if failed
    """

    print("="*80)
    print("PRODUCTION PIPELINE - QUALITY-BASED SENTENCE EXTRACTION")
    print("="*80)

    start_time = time.time()

    # Extract shipment name if not provided
    if not shipment_name:
        shipment_name = os.path.basename(pdf_folder.rstrip('\\').rstrip('/'))

    # Step 1: Load curated data (for TAN names and metadata only)
    print("Step 1: Loading curated data for TAN metadata...")
    curated_df = load_curated_data(curator_data_folder, section_code)

    if curated_df.empty:
        print(" No curated data found!")
        return None

    print(f" Loaded {len(curated_df)} curated records")

    # Step 2: Group by TAN_NAME (no keyphrases)
    print("Step 2: Grouping data by TAN_NAME...")
    tan_groups = group_data_by_tan(curated_df)
    print(f" Found {len(tan_groups)} unique TANs")

    # Step 3: Process each unique TAN with quality-based filtering
    print("Step 3: Processing PDFs with quality-based filtering...")

    # Create unique intermediate folder
    intermediate_folder = f"production_section_{section_code}_shipment_{shipment_name}"
    os.makedirs(intermediate_folder, exist_ok=True)

    # Process each unique TAN_NAME
    processed_files = []
    for tan_name, tan_data in tan_groups.items():
        try:
            # Create unique filename for each TAN
            tan_output_file = os.path.join(
                intermediate_folder,
                f"production_section_{section_code}_shipment_{shipment_name}_tan_{tan_name}.xlsx"
            )

            # Process individual TAN with production method
            if process_single_tan_production(tan_name, tan_data, pdf_folder, tan_output_file):
                processed_files.append(tan_output_file)

        except Exception as e:
            print(f" Error processing TAN {tan_name}: {e}")
            continue

    # Step 4: Combine all processed files
    if processed_files:
        final_output_file = f"production_section_{section_code}_shipment_{shipment_name}_combined.xlsx"
        combine_processed_files(processed_files, final_output_file)

        end_time = time.time()
        print(f"\n Production pipeline completed in {end_time - start_time:.2f} seconds")
        print(f" Output file: {final_output_file}")

        return final_output_file

    return None


if __name__ == "__main__":
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\curated_data_sample"
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\utility_data\text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"

    # Use the new production pipeline
    result = production_pipeline(
        section_code=section_code,
        curator_data_folder=curator_data_folder,
        pdf_folder=pdf_folder,
        abbr_full_form_excel=abbr_full_form_excel,
        predict_keyphrases_flag=True
    )

    if result:
        print(f"\n Production pipeline completed successfully!")
        print(f" Output file: {result}")
    else:
        print("\n Production pipeline failed")
