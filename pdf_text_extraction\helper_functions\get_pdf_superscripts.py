"""
Module for extracting text from PDF files using PyMuPDF (fitz).

This module provides functions for extracting text from PDF files, handling ligatures,
hyphenated word breaks, and dehyphenation. The extracted text can be used for further
processing or analysis.

Date 19-06-24
@author: <PERSON>
"""


# Import necessary modules
import re
import logging
import fitz



## Get pdf text by fitz
def get_pdf_text(pdf_path):
    """
        Extract text from a PDF file using PyMuPDF (fitz).

        Args:
            pdf_path (str): Path to the PDF file.

        Returns:
            str: The extracted text from the PDF file.
    """

    with fitz.open(pdf_path) as doc:
        text = ""
        for page in doc:
            page_text = page.get_text("text")
            # Remove ligatures (if any)
            ligatures = {'ﬂ': 'fl', 'ﬁ': 'fi', 'ﬀ': 'ff', 'ﬃ': 'ffi', 'ﬄ': 'ffl'}
            for ligature, replacement in ligatures.items():
                page_text = page_text.replace(ligature, replacement)

            ## Resolving hyphenated word breaks at the end of line
            page_text = page_text.replace("\n\n", "\n").replace("  ", " ")

            # Correct words starting with "anti-" followed by a hyphen
            # page_text = re.sub(r'\b(anti)-(\w+)\b', r'\1\2', page_text)

            # Dehyphenation (merge hyphenated words)
            page_text = re.sub(r'(\w+)-\n(\w+)', r'\1\2', page_text)

            # Replace hyphens between words with a space
            page_text = re.sub(r'(\w+)-(\w+)', r'\1 \2', page_text)

            page_text = page_text.replace("  ", " ")
            text += " " +page_text

    return text



def is_superscript(word):
    """
    Check if a given word represents a superscript.

    Args:
        word (str): The input word to check.

    Returns:
        bool: True if the word represents a superscript, False otherwise.
    """
    try:
        # Define a regex pattern for superscripts in square brackets\
         #or hyphens or commas or dashes
        superscript_pattern = re.compile(r'\[\d{1,3}\]|\[\d{1,2}-\d{1,2}\]')

        # Check if the word is short enough to be a superscript
        if len(word) < 10:

            # Clean the word by replacing characters and splitting by various delimiters
            word = word.strip().replace("-", ",").replace("–", ",").replace(" ", ",")

            # Check superscript format for square brackets
            if "[" in word and "]" in word:
                word = word.replace("[", "").replace("]", "")
                if "," in word:
                    return any(part.isdigit() for part in word.split(','))

            # Check superscript format for hyphens or en dashes
            elif "-" in word or "–" in word:
                word = word.replace("–", "-")
                return any(part.isdigit() for part in word.split('-') if len(part) > 0)

            # Check superscript format for spaces
            elif " " in word:
                word = word.replace(" ", "")
                return any(part.isdigit() for part in word.split(' '))

            # Check if word matches superscript pattern
            elif superscript_pattern.search(word):
                return True

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("is_superscript Error: %s", str(error))
        return False



def find_bracketed_superscript(pdf_path):
    """
    Extract bracketed superscripts from a PDF file.

    Args:
        pdf_path (str): Path to the PDF file.

    Returns:
        list: List of bracketed superscripts found in the PDF.
    """
    try:
        font_info_list = []
        complete_text = " "
        with fitz.open(pdf_path) as doc:  # type: ignore

            for page_num in range(doc.page_count):
                page = doc[page_num]
                page_text = page.get_text("text")
                complete_text += page_text
                for text_object in page.get_text("dict")["blocks"]:

                    if "lines" in text_object:

                        for line in text_object["lines"]:

                            for span in line["spans"]:

                                font_info_list.append({
                                    'page_number': page_num,
                                    'font_name'  : span['font'],
                                    'font_size'  : span['size'],
                                    'font_flags' : span['flags'],
                                    'font_color' : span['color'],
                                    'text'       : span['text']
                                })
        superscript_list = []

        # Extract font-related information
        for font_info in font_info_list:
            page_num = font_info['page_number']
            font_flags = font_info['font_flags']
            text = font_info['text']

            # Check if the text is a superscript with specific font flags
            if (font_flags == 5 or font_flags == 4) and\
                  len(text) != 0 and is_superscript(text):
                font_color = font_info['font_color']
                superscript_list.append(text.strip())

        superscript_list = list(set(superscript_list))

        # Regex pattern to match superscripts in various formats like [1], [5–10], etc.
        pattern = r'\[\d+(?:[\u2013\u2014\u2212,-−]?\d+)?\]'

        # Extract superscript text using regex
        for font_info in font_info_list:
            # text = font_info['text']
            text = complete_text

            # Extract superscripts from the text
            matches = re.findall(pattern, text)

            # check if the matches are not empty
            if matches:
                # print(f"{matches =}")
                superscript_list.extend(matches)

        superscript_list = list(set(superscript_list))
        # print(f"{len(superscript_list) =}")
        superscript_list = [item for item in superscript_list if \
                            all(int(part) <= 100 for part in \
                            re.findall(r'\d+', item))]
        return superscript_list

    except Exception as error:

        # Log the error if any exception occurs
        logging.error("find_bracketed_superscript Error: %s", str(error))

        return []  # Return an empty list in case of an error



def get_superscript_digits(word):
    """
    Extract digits from a word that may contain superscript information.

    Args:
        word (str): The input word to extract digits from.

    Returns:
        list: List of digits extracted from the word.
    """
    return [part for part in word.split(',') + word.split('-') \
            + word.split('.') if part.isdigit() and int(part) <= 100]



def is_valid_superscript_0(superscript):
    """
    Check if a given superscript is in a valid format.

    Args:
        superscript (str): The input superscript string.

    Returns:
        bool: True if the superscript is valid, False otherwise.
    """
    try:
        # Check if the superscript ends with non-digit characters
        if superscript and not superscript[-1].isdigit():
            return False

        # Check if the superscript is a single digit
        if superscript is not None:

            # Check if the superscript is having "." or "-" or "," in it
            if "." in superscript:
                parts = superscript.split('.')

                if len(parts) == 2 and not all(part.isdigit() for part in parts):
                    return True

                elif "-" in superscript:
                    start, end = superscript.split('-')
                    if start.isdigit() and end.isdigit():
                        return True

                elif "," in superscript and all(part.isdigit() \
                                    for part in superscript.split(',')):
                    return True

                elif superscript.isdigit() and int(superscript) <= 100:
                    return True

                return False

            # Check if the superscript is having ")" in it
            elif ")" in superscript:
                parts = superscript.split(')')

                if len(parts) == 2 and not all(part.isdigit() for part in parts):
                    return True

                elif "-" in superscript:
                    start, end = superscript.split('-')
                    if start.isdigit() and end.isdigit():
                        return True

                elif "," in superscript and all(part.isdigit()\
                                     for part in superscript.split(',')):
                    return True

                elif superscript.isdigit() and int(superscript) <= 100:
                    return True

                return False

            # Check if the superscript is having "," in it
            elif "," in superscript:
                parts = superscript.split(',')

                if len(parts) == 2 and not all(part.isdigit() for part in parts):
                    return True

                elif "-" in superscript:
                    start, end = superscript.split('-')
                    if start.isdigit() and end.isdigit():
                        return True

                elif "," in superscript and all(part.isdigit()\
                                for part in superscript.split(',')):
                    return True

                elif superscript.isdigit() and int(superscript) <= 100:
                    return True

                return False

            # Check if the superscript is having " " in it
            elif " " in superscript:
                parts = superscript.split(' ')

                if len(parts) == 2 and not all(part.isdigit() for part in parts):
                    return True

                elif "-" in superscript:
                    start, end = superscript.split('-')
                    if start.isdigit() and end.isdigit():
                        return True

                elif "," in superscript and all(part.isdigit()\
                                 for part in superscript.split(',')):
                    return True

                elif superscript.isdigit() and int(superscript) <= 100:
                    return True

                return False

        else:
            return True

    except Exception as error:

        # Log the error if any exception occurs
        logging.error("is_valid_superscript Error: %s", str(error))

        # Return False in case of an error
        return False


def is_valid_superscript(superscript):
    """
    Check if a given superscript is in a valid format.

    Args:
        superscript (str): The input superscript string.

    Returns:
        bool: True if the superscript is valid, False otherwise.
    """
    try:
        # Ensure superscript is a string
        if not isinstance(superscript, str):
            return False

        # Check if the superscript ends with non-digit characters
        if superscript and not superscript[-1].isdigit():
            return False

        # Check for formats involving ".", "-", "," or spaces
        if "." in superscript:
            parts = superscript.split('.')
            if len(parts) == 2 and all(part.isdigit() for part in parts):
                return True

        if "-" in superscript:
            parts = superscript.split('-')
            if len(parts) == 2 and all(part.isdigit() for part in parts):
                return True

        if "," in superscript:
            parts = superscript.split(',')
            if all(part.isdigit() for part in parts):
                return True

        if ")" in superscript:
            parts = superscript.split(')')
            if len(parts) == 2 and parts[0].isdigit():
                return True

        if " " in superscript:
            parts = superscript.split(' ')
            if all(part.isdigit() for part in parts):
                return True

        # Check if superscript is a single number <= 100
        if superscript.isdigit() and int(superscript) <= 100:
            return True

        # If none of the valid conditions are met, return False
        return False

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("is_valid_superscript Error: %s", str(error))
        return False




def clean_superscript(superscript):
    """
    Clean the given superscript string by removing non-digit characters.

    Args:
        superscript (str): The input superscript string.

    Returns:
        str: The cleaned superscript string.
    """
    try:
        # Check if the superscript is None
        if superscript is None:
            return []

        # Check if there are any digits in the superscript
        if any(c.isdigit() for c in str(superscript)):

            # Split the superscript at "."
            parts = str(superscript).split('.')

            if ")" in parts[0]:

                parts_list = parts[0].split(")")
                first_part = parts_list[0].replace("(", "").replace(")", "")

                # Check if the first part is a digit
                if first_part.isdigit():
                    return []
                else:
                    return superscript
            elif parts and parts[0].isdigit():
                return []

            # Remove any non-digit characters that appear after the last digit
            last_digit_index = max([superscript.rfind(c)\
                     for c in str(superscript) if c.isdigit()], default=-1)

            cleaned_superscript = str(superscript)[:last_digit_index + 1]

            return cleaned_superscript
        else:
            return str(superscript)

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("clean_superscript Error: %s", str(error))

        # Return an empty list in case of an error
        return []



def is_power_superscript(word):
    """
    Check if a given word is a valid superscript.

    Args:
        word (str): The input word to check.

    Returns:
        str: The valid superscript if the word is valid, None otherwise.
    """
    try:
        # Regex pattern to match valid superscript formats
        pattern = re.compile(r'^(\[\d+(-\d+)?(\s*,\s*\d+(-\d+)?)*)\]$|^\d+(-\d+)?$')

        # Check if the word matches the pattern
        match = pattern.match(word)

        # If the word matches the pattern, get the digits in the superscript
        word = get_superscript_digits(word)

        # Check if the word is a valid superscript
        word = is_valid_superscript(word)

        # If the word is a valid superscript, clean the superscript
        word = clean_superscript(word)

        # If it's a valid format, return the word; otherwise, return None
        return word if match else None

    except Exception as error:

        # Log the error if any exception occurs
        logging.error("is_power_superscript Error: %s", str(error))

        return None  # Return None in case of an error



def is_invalid_number_string_combination(superscript):
    """
    Check if a given superscript contains an invalid\
          combination of numbers and strings.

    Args:
        superscript (str): The input superscript string.

    Returns:
        bool: True if the combination is invalid, False otherwise.
    """
    try:
        parts = superscript.split('.')
        if len(parts) == 2 and parts[0].isdigit() and any(c.isalpha() for c in parts[1]):
            return True
        return False

    except Exception as error:

        # Log the error if any exception occurs
        logging.error("is_invalid_number_string_combination Error: %s", str(error))

        # Return False in case of an error
        return False



def is_single_number(superscript):
    """
    Check if a given superscript is a single number.

    Args:
        superscript (str): The input superscript string.

    Returns:
        bool: True if the superscript is a single number, False otherwise.
    """
    try:
        return superscript.isdigit()

    except Exception as error:

        # Log the error if any exception occurs
        logging.error("is_single_number Error: %s", str(error))

        # Return False in case of an error
        return False



def find_power_superscripts(pdf_path):
    """
    Extract power superscripts from a PDF file.

    Args:
        pdf_path (str): The path to the PDF file.

    Returns:
        list: A list of unique power superscripts.
    """
    superscript_list = []

    try:
        # Open the PDF file using fitz to get text from the PDF
        with fitz.open(pdf_path) as doc: # type: ignore
            page_numbers = doc.page_count

            for page_num in range(page_numbers):
                page = doc[page_num]
                words = page.get_text("words")

                # for n, word_info in enumerate(words):
                for num, _ in enumerate(words):

                    # Get the actual word at index 4
                    # word = word_info[4]

                    # Get the bottom y-coordinate of the bounding box
                    y_bottom = words[num][1]

                    # Get the top y-coordinate of the bounding box
                    # y_top = words[n][3]

                    word_position = words[num][7]
                    word_position0 = words[num-1][7]
                    y_bottom0 = words[num-1][1]
                    # y_top0 = words[num-1][3]

                    # Check if the word is a superscript
                    if abs(y_bottom0 - y_bottom) > 0.5:
                        if abs(word_position - word_position0) == 1:
                            superscript0 = words[num-1][4]
                            superscript1 = words[num][4]

                            # Check if the superscript is a valid power superscript
                            if ("," in superscript0 or "." in superscript0 \
                                or ")" in superscript0) and (":" not in superscript0):
                                cleaned_superscript0 = clean_superscript(superscript0)

                                # Check if the superscript is a valid power superscript
                                if is_valid_superscript(cleaned_superscript0)\
                                and \
                                not is_invalid_number_string_combination(cleaned_superscript0)\
                                and not is_single_number(cleaned_superscript0):
                                    superscript_list.\
                                        append(cleaned_superscript0.strip()) # type: ignore

                            # check if the superscript is a valid power superscript
                            if ("," in superscript1 or "." in superscript1 \
                                or ")" in superscript1) and (":" not in superscript1):
                                cleaned_superscript1 = clean_superscript(superscript1)

                                # Check if the superscript is a valid power superscript
                                if is_valid_superscript(cleaned_superscript1)\
                                    and not \
                                    is_invalid_number_string_combination(cleaned_superscript1)\
                                    and not is_single_number(cleaned_superscript1):
                                    superscript_list.\
                                    append(cleaned_superscript1.strip()) # type: ignore

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("find_power_superscripts Error: %s", str(error))

    # Remove duplicates from the list
    return list(set(superscript_list))



def get_all_superscripts(pdf_path):
    """
    Extract all superscripts from a PDF file.
    """
    superscript_list = []
    try:
        powered_superscript = find_power_superscripts(pdf_path)
        superscript_list.extend(powered_superscript)

    except Exception as error:
        # Log the error if any exception occurs
        logging.error("get_all_superscripts Error: %s", error)

    try:
        bracketed_superscript = find_bracketed_superscript(pdf_path)
        superscript_list.extend(bracketed_superscript)

    except Exception as error:
    # Log the error if any exception occurs
        logging.error("get_all_superscripts Error: %s", error)

    return superscript_list

