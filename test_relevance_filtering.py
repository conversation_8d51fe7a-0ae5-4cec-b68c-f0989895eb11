#!/usr/bin/env python3
"""
Test Relevance Filtering
========================

This script tests the new relevance filtering functionality that reduces
the number of extracted sentences by focusing on content most relevant
to the title and abstract.
"""

import os
import sys
import pandas as pd
from pathlib import Path

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from main_sentence_filter_final_test2 import production_pipeline


def test_relevance_filtering():
    """Test the production pipeline with relevance filtering"""
    
    print("="*80)
    print("TESTING RELEVANCE FILTERING FOR SENTENCE REDUCTION")
    print("="*80)
    
    # Configuration
    section_code = "52"
    curator_data_folder = "curated_data_sample"
    abbr_full_form_excel = "utility_data/text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"
    
    print(f"Section Code: {section_code}")
    print(f"PDF Folder: {pdf_folder}")
    print()
    
    # Check if folders exist
    if not os.path.exists(curator_data_folder):
        print(f"Error: Curator data folder '{curator_data_folder}' not found")
        return
    
    if not os.path.exists(pdf_folder):
        print(f"Error: PDF folder '{pdf_folder}' not found")
        return
    
    # Run the production pipeline with relevance filtering
    print("Running production pipeline with relevance filtering...")
    try:
        result_file = production_pipeline(
            section_code=section_code,
            curator_data_folder=curator_data_folder,
            pdf_folder=pdf_folder,
            abbr_full_form_excel=abbr_full_form_excel,
            predict_keyphrases_flag=True
        )
        
        if result_file and os.path.exists(result_file):
            print(f"✓ Pipeline completed successfully!")
            print(f"✓ Output file: {result_file}")
            
            # Analyze the results
            analyze_results(result_file)
            
        else:
            print("✗ Pipeline failed or no output generated")
            
    except Exception as e:
        print(f"✗ Error in pipeline: {e}")
        import traceback
        traceback.print_exc()


def analyze_results(result_file: str):
    """Analyze the results to show the impact of relevance filtering"""
    
    print("\n" + "="*60)
    print("RELEVANCE FILTERING ANALYSIS")
    print("="*60)
    
    try:
        df = pd.read_excel(result_file)
        
        print(f"Total sentences extracted: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        
        # Analyze by segment
        if 'segment' in df.columns:
            segment_counts = df['segment'].value_counts()
            print(f"\nSentences per segment:")
            for segment, count in segment_counts.items():
                print(f"  {segment}: {count} sentences")
        
        # Analyze relevance scores if available
        if 'relevance_score' in df.columns:
            print(f"\nRelevance score statistics:")
            print(f"  Mean: {df['relevance_score'].mean():.3f}")
            print(f"  Min: {df['relevance_score'].min():.3f}")
            print(f"  Max: {df['relevance_score'].max():.3f}")
            
            # Show distribution
            score_ranges = [
                (0.0, 0.1, "Very Low"),
                (0.1, 0.2, "Low"),
                (0.2, 0.4, "Medium"),
                (0.4, 0.6, "High"),
                (0.6, 1.0, "Very High")
            ]
            
            print(f"\nRelevance score distribution:")
            for min_score, max_score, label in score_ranges:
                count = len(df[(df['relevance_score'] >= min_score) & (df['relevance_score'] < max_score)])
                if count > 0:
                    print(f"  {label} ({min_score}-{max_score}): {count} sentences")
        
        # Show sample high-relevance sentences
        if 'relevance_score' in df.columns:
            high_relevance = df[df['relevance_score'] >= 0.3].head(5)
            if not high_relevance.empty:
                print(f"\nSample high-relevance sentences:")
                for idx, row in high_relevance.iterrows():
                    print(f"\nSegment: {row['segment']}")
                    print(f"Relevance: {row['relevance_score']:.3f}")
                    print(f"Sentence: {row['sentence'][:150]}...")
        
        # Analyze predicted keyphrases if available
        if 'predicted_keyphrases' in df.columns:
            # Count non-empty keyphrase predictions
            non_empty_predictions = df[df['predicted_keyphrases'].str.len() > 0]
            print(f"\nKeyphrase prediction results:")
            print(f"  Sentences with predicted keyphrases: {len(non_empty_predictions)}")
            print(f"  Sentences without keyphrases: {len(df) - len(non_empty_predictions)}")
            
            if not non_empty_predictions.empty:
                print(f"\nSample predicted keyphrases:")
                for idx, row in non_empty_predictions.head(3).iterrows():
                    print(f"  {row['predicted_keyphrases']}")
        
    except Exception as e:
        print(f"Error analyzing results: {e}")


def compare_with_previous_results():
    """Compare with previous results if available"""
    
    print("\n" + "="*60)
    print("COMPARISON WITH PREVIOUS RESULTS")
    print("="*60)
    
    # Look for previous output files
    previous_files = [
        "production_section_52_shipment_982560_combined.xlsx",
        "aggregated_keyphrases_section_52_shipment_982560.xlsx"
    ]
    
    for prev_file in previous_files:
        if os.path.exists(prev_file):
            try:
                prev_df = pd.read_excel(prev_file)
                print(f"\nPrevious file: {prev_file}")
                print(f"  Total rows: {len(prev_df)}")
                print(f"  Columns: {list(prev_df.columns)}")
                
                if 'segment' in prev_df.columns:
                    prev_segments = prev_df['segment'].value_counts()
                    print(f"  Segments: {dict(prev_segments)}")
                    
            except Exception as e:
                print(f"  Error reading {prev_file}: {e}")
        else:
            print(f"Previous file not found: {prev_file}")


def show_filtering_benefits():
    """Show the benefits of relevance filtering"""
    
    print("\n" + "="*60)
    print("BENEFITS OF RELEVANCE FILTERING")
    print("="*60)
    
    benefits = [
        "✓ Reduces number of extracted sentences by focusing on relevance",
        "✓ Prioritizes sentences similar to title and abstract content",
        "✓ Uses both keyword overlap and semantic similarity",
        "✓ Applies different limits per segment (more from title/abstract)",
        "✓ Filters out sentences with low relevance scores",
        "✓ Maintains quality while reducing noise",
        "✓ Improves keyphrase prediction accuracy",
        "✓ Reduces processing time for downstream tasks"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print(f"\nConfiguration options:")
    print(f"  - min_relevance_score: Minimum threshold for sentence inclusion")
    print(f"  - max_sentences_per_segment: Limit sentences per segment")
    print(f"  - use_semantic_similarity: Enable/disable semantic similarity")
    print(f"  - Segment priorities: title > abstract > results > conclusion > others")


if __name__ == "__main__":
    test_relevance_filtering()
    compare_with_previous_results()
    show_filtering_benefits()
