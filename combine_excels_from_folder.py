import os
import glob
import pandas as pd
from datetime import datetime
from pathlib import Path

def combine_final_keyphrase_excels(input_folder, output_file=None, output_folder="combined_outputs"):

    print("\n" + "="*80)
    print(" COMBINING FINAL KEYPHRASE EXCEL FILES")
    print("="*80)
    
    # Step 1: Validate input folder
    if not os.path.exists(input_folder):
        print(f" Input folder does not exist: {input_folder}")
        return None
    
    # Step 2: Find all matching Excel files
    search_pattern = os.path.join(input_folder, '*_final_with_keyphrases.xlsx')
    matching_files = glob.glob(search_pattern)
    
    print(f" Searching for files matching pattern: *_final_with_keyphrases.xlsx")
    print(f" Search folder: {input_folder}")
    print(f" Found {len(matching_files)} matching files")
    
    if not matching_files:
        print(f" No files found matching pattern '*_final_with_keyphrases.xlsx' in {input_folder}")
        
        # Show available files for debugging
        all_files = [f for f in os.listdir(input_folder) if f.endswith('.xlsx')]
        if all_files:
            print(f" Available Excel files in folder:")
            for file in all_files[:10]:  # Show first 10 files
                print(f"  - {file}")
            if len(all_files) > 10:
                print(f"  ... and {len(all_files) - 10} more files")
        return None
    
    # Step 3: Display found files
    print(f"\n Files to be combined:")
    for i, file in enumerate(matching_files, 1):
        file_size = os.path.getsize(file) / 1024  # Size in KB
        print(f"  {i}. {os.path.basename(file)} ({file_size:.1f} KB)")
    
    # Step 4: Load and combine all Excel files
    combined_dataframes = []
    successful_loads = 0
    failed_loads = 0
    total_rows = 0
    
    print(f"\n Loading and combining Excel files...")
    
    for file_path in matching_files:
        try:
            print(f"   Loading: {os.path.basename(file_path)}")
            
            # Load the Excel file
            df = pd.read_excel(file_path)
            
            # Add source file information
            df['source_file'] = os.path.basename(file_path)
            df['combined_timestamp'] = datetime.now()
            
            combined_dataframes.append(df)
            successful_loads += 1
            total_rows += len(df)
            
            print(f"     Loaded {len(df)} rows, {len(df.columns)} columns")
            
        except Exception as e:
            print(f"     Failed to load {os.path.basename(file_path)}: {str(e)}")
            failed_loads += 1
    
    # Step 5: Check if any files were loaded successfully
    if not combined_dataframes:
        print(f" No Excel files could be loaded successfully")
        return None
    
    print(f"\n Loading Summary:")
    print(f"   Successfully loaded: {successful_loads} files")
    print(f"   Failed to load: {failed_loads} files")
    print(f"   Total rows to combine: {total_rows}")
    
    # Step 6: Combine all DataFrames
    print(f" Combining all DataFrames...")
    try:
        final_combined_df = pd.concat(combined_dataframes, ignore_index=True)
        print(f" Combined successfully: {len(final_combined_df)} total rows")
    except Exception as e:
        print(f" Error combining DataFrames: {str(e)}")
        return None
    
    # Step 7: Generate output filename if not provided
    if output_file is None:
        # Create output folder if it doesn't exist
        os.makedirs(output_folder, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_folder, f"combined_final_keyphrases_{timestamp}.xlsx")
    else:
        # Ensure output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
    
    # Step 8: Save combined Excel file
    print(f" Saving combined Excel file...")
    try:
        final_combined_df.to_excel(output_file, index=False)
        file_size = os.path.getsize(output_file) / 1024 / 1024   
        
        print(f" Combined Excel file saved successfully!")
        print(f" Output file: {output_file}")
        print(f" Final file size: {file_size:.2f} MB")
        print(f" Total rows: {len(final_combined_df)}")
        print(f"  Total columns: {len(final_combined_df.columns)}")
        
    except Exception as e:
        print(f" Error saving combined file: {str(e)}")
        return None
    
    # Step 9: Display column summary
    print(f"\n  Column Summary:")
    for i, col in enumerate(final_combined_df.columns, 1):
        non_null_count = final_combined_df[col].notna().sum()
        print(f"  {i:2d}. {col}: {non_null_count} non-null values")
    
    print(f"\n Combination completed successfully!")
    return output_file


if __name__ == "__main__":
    # Combine all keyphrase files from a folder
    combined_file = combine_final_keyphrase_excels(
        input_folder="processed_datasets",
        output_file="tagged_dataset_all_shipments_combined.xlsx"
    )