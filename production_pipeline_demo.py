
"""
Production Pipeline Demo
========================

This script demonstrates the updated production pipeline that:
1. Extracts segmented text from PDFs without relying on curated keyphrases
2. Applies quality-based filtering using SegmentPreprocessor, BasicKeyphraseExtractor, and NoisySentenceRemover
3. Extracts important sentences per segment
4. Optionally predicts keyphrases using the trained ML model

Key Changes for Production:
- No dependency on CTH or SOURCE_DOC_INDEX_TERM columns
- Quality-based sentence filtering instead of keyphrase-based filtering
- Integration of all imported processing modules
- Optional keyphrase prediction as final step
"""

import os
import sys
from pathlib import Path

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from main_sentence_filter_final_test2 import production_pipeline
from get_section_52_cleaned_title_abs_text_test1 import load_and_prepare_complete_dataset


def demo_production_pipeline():
    """Demonstrate the production pipeline"""
    
    print("="*80)
    print("PRODUCTION PIPELINE DEMONSTRATION")
    print("="*80)
    
    # Configuration
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\curated_data_sample"
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized_production\utility_data\text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"
    
    print(f"Section Code: {section_code}")
    print(f"Curator Data Folder: {curator_data_folder}")
    print(f"PDF Folder: {pdf_folder}")
    print()
    
    # Step 1: Demonstrate the updated title/abstract extraction (no keyphrases)
    print("Step 1: Testing updated title/abstract extraction...")
    try:
        dataset = load_and_prepare_complete_dataset(
            section_code=section_code,
            curator_data_folder=curator_data_folder,
            pdf_folder=pdf_folder,
            abbr_full_form_excel=abbr_full_form_excel
        )
        
        print(f" Successfully loaded dataset with {len(dataset)} TANs")
        print(f" Columns: {list(dataset.columns)}")
        print(f" No keyphrase columns (terms_list, alternate_terms_list) - Production ready!")
        print()
        
    except Exception as e:
        print(f" Error in title/abstract extraction: {e}")
        return
    
    # Step 2: Run the production pipeline
    print("Step 2: Running production sentence extraction pipeline...")
    try:
        result_file = production_pipeline(
            section_code=section_code,
            curator_data_folder=curator_data_folder,
            pdf_folder=pdf_folder,
            abbr_full_form_excel=abbr_full_form_excel,
            predict_keyphrases_flag=True  # Enable keyphrase prediction
        )
        
        if result_file and os.path.exists(result_file):
            print(f" Production pipeline completed successfully!")
            print(f" Output file: {result_file}")
            
            # Show sample results
            import pandas as pd
            df = pd.read_excel(result_file)
            print(f" Total sentences extracted: {len(df)}")
            print(f" Columns in output: {list(df.columns)}")
            
            if 'predicted_keyphrases' in df.columns:
                print(f" Keyphrase prediction enabled")
                # Show sample predictions
                sample_predictions = df[df['predicted_keyphrases'].str.len() > 0].head(3)
                if not sample_predictions.empty:
                    print("\nSample predictions:")
                    for idx, row in sample_predictions.iterrows():
                        print(f"  Sentence: {row['sentence'][:100]}...")
                        print(f"  Predicted: {row['predicted_keyphrases']}")
                        print()
            
        else:
            print(f" Production pipeline failed or no output file generated")
            
    except Exception as e:
        print(f" Error in production pipeline: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("="*80)
    print("PRODUCTION PIPELINE FEATURES SUMMARY")
    print("="*80)
    print(" No dependency on curated keyphrases (CTH, SOURCE_DOC_INDEX_TERM)")
    print(" Quality-based sentence filtering using:")
    print("  - SegmentPreprocessor: Text cleaning and sentence extraction")
    print("  - BasicKeyphraseExtractor: Section-weighted processing")
    print("  - NoisySentenceRemover: Noise detection and quality scoring")
    print(" Segmented text processing (title, abstract, results, conclusion, etc.)")
    print(" Optional keyphrase prediction using trained ML model")
    print(" Production-ready output with quality scores and segment information")
    print("="*80)


def show_module_integration():
    """Show how the imported modules are now properly integrated"""
    
    print("\nMODULE INTEGRATION DETAILS")
    print("="*50)
    
    print("1. SegmentPreprocessor:")
    print("   - clean_text(): Normalizes whitespace and removes artifacts")
    print("   - extract_sentences(): Splits text into sentences with length filtering")
    print("   - Used for: Initial text preprocessing of PDF segments")
    print()
    
    print("2. BasicKeyphraseExtractor:")
    print("   - Section weights for different PDF segments")
    print("   - Quality assessment and sentence ranking")
    print("   - Used for: Section-aware sentence importance scoring")
    print()
    
    print("3. NoisySentenceRemover:")
    print("   - is_noisy_sentence(): Detects low-quality sentences")
    print("   - process_sentences(): Separates good from noisy sentences")
    print("   - Used for: Final quality filtering and scoring")
    print()
    
    print("4. predict_module:")
    print("   - predict_keyphrases(): ML-based keyphrase extraction")
    print("   - Used for: Optional keyphrase prediction from quality sentences")
    print()


if __name__ == "__main__":
    demo_production_pipeline()
    show_module_integration()
