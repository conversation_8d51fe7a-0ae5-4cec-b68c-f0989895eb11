import os
import pandas as pd
import numpy as np
import re
import time
from pathlib import Path

# Import all required modules from the same directory
from main_sentence_filter_final_test2 import main_processing_pipeline
from get_segmented_title_abs_data_test1 import get_segmented_pdf_text_optimized
from get_cth_from_sentences_v1_optimized import optimized_main_pipeline
from dataset_module_v1 import process_multiple_excel_files, load_all_tagged_files_for_training

# Import transformers
try:
    from transformers import AutoTokenizer
except ImportError:
    print("Warning: transformers not installed. Install with: pip install transformers")
    AutoTokenizer = None

# def setup_sample_data():
#     """Setup sample data if not exists"""
#     sample_file = "curated_data_sample/sample_keyphrases.xlsx"
#     if not os.path.exists(sample_file):
#         print(" Setting up sample data...")
#         from setup_sample_data import create_sample_data
#         create_sample_data()

def create_output_directories():
    """Create necessary output directories"""
    directories = [
        "cth_sent_excel_data",
        "processed_datasets", 
        "final_training_data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f" Created/verified directory: {directory}")

def step1_extract_pdf_segments(section_code, curator_data_folder, pdf_folder, abbr_full_form_excel):
    """Step 1: Extract and segment PDF text data"""
    print("="*80)
    print("STEP 1: EXTRACTING PDF SEGMENTS")
    print("="*80)
    
    try:
        # Run optimized PDF text extraction
        final_excel_path = get_segmented_pdf_text_optimized(
            section_code, curator_data_folder, pdf_folder, abbr_full_form_excel
        )
        
        if final_excel_path and os.path.exists(final_excel_path):
            print(f" PDF segmentation completed: {final_excel_path}")
            return final_excel_path
        else:
            print(" PDF segmentation returned None or file doesn't exist")
            return None
        
    except Exception as e:
        print(f" Error in PDF segmentation: {e}")
        return None

def step2_filter_sentences(segmented_data_path):
    """Step 2: Filter and rank sentences containing keyphrases"""
    print("="*80)
    print("STEP 2: FILTERING AND RANKING SENTENCES")
    print("="*80)
    
    try:
        # Configuration for sentence filtering
        OUTPUT_FILE_TITLE_ABSTRACT = "cth_sent_excel_data/title_abstract_sentences_final_ranked.xlsx"
        OUTPUT_FILE_OTHER_SECTIONS = "cth_sent_excel_data/other_section_sentences_final_ranked.xlsx"
        REJECTED_ANALYSIS_FILE = "cth_sent_excel_data/rejected_sentences_analysis.xlsx"
        
        # Run optimized sentence filtering pipeline
        df_ta_result, df_other_result = optimized_main_pipeline(
            input_excel=segmented_data_path,
            output_excel_ta=OUTPUT_FILE_TITLE_ABSTRACT,
            output_excel_other=OUTPUT_FILE_OTHER_SECTIONS,
            rejected_file=REJECTED_ANALYSIS_FILE,
            max_sentences_per_term=50,
            min_words=5,
            max_words=100,
            max_numeric_ratio=0.3,
            max_special_char_ratio=0.3,
            max_repetition_ratio=0.3,
            readability_weight=0.15,
            grammar_weight=0.20,
            semantic_weight=0.30,
            term_context_weight=0.25,
            complexity_weight=0.10,
            apply_cross_references=True,
            apply_filtering=True,
            apply_ranking=True,
            save_rejected=True,
            verbose=True,
            max_workers=4
        )
        
        print(f" Title/Abstract sentences: {OUTPUT_FILE_TITLE_ABSTRACT}")
        print(f" Other section sentences: {OUTPUT_FILE_OTHER_SECTIONS}")
        
        return OUTPUT_FILE_TITLE_ABSTRACT, OUTPUT_FILE_OTHER_SECTIONS
        
    except Exception as e:
        print(f" Error in sentence filtering: {e}")
        return None, None

def step3_create_tagged_dataset():
    """Step 3: Create tagged dataset for BERT training"""
    print("="*80)
    print("STEP 3: CREATING TAGGED DATASET")
    print("="*80)
    
    try:
        # Initialize tokenizer
        if AutoTokenizer is None:
            print("  AutoTokenizer not available, using basic tokenization")
            tokenizer = None
        else:
            try:
                tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
                print(" Loaded BERT tokenizer")
            except Exception as e:
                print(f"  Could not load BERT tokenizer: {e}")
                tokenizer = None
        
        # Configuration
        input_folder = "cth_sent_excel_data"
        output_folder = "processed_datasets"
        combined_file = "combined_dataset.xlsx"
        
        # Process Excel files and create tagged data
        combined_file_path = process_multiple_excel_files(
            input_folder=input_folder,
            tokenizer=tokenizer,
            output_folder=output_folder,
            combined_output_file=combined_file,
            test_size=0.1,
            val_size=0.1,
            save_debug=True,
            use_existing_tagged=False  # Create new tagged files
        )
        
        if combined_file_path:
            print(f" Tagged dataset created: {combined_file_path}")
            return combined_file_path
        else:
            print(" Failed to create tagged dataset")
            return None
        
    except Exception as e:
        print(f" Error in dataset tagging: {e}")
        return None

def step4_prepare_training_data():
    """Step 4: Prepare final training data"""
    print("="*80)
    print("STEP 4: PREPARING FINAL TRAINING DATA")
    print("="*80)
    
    try:
        # Load and combine all tagged files for training
        training_file = load_all_tagged_files_for_training("processed_datasets")
        
        if training_file:
            # Copy to final training data folder
            final_training_path = "final_training_data/all_keyphrases_combined_tagged_data.xlsx"
            
            # Read and save to final location
            df = pd.read_excel(training_file)
            df.to_excel(final_training_path, index=False)
            
            print(f" Final training data ready: {final_training_path}")
            print(f" Total records: {len(df)}")
            print(f" Columns: {list(df.columns)}")
            
            return final_training_path
        else:
            print(" No tagged files found for training")
            return None
            
    except Exception as e:
        print(f" Error in preparing training data: {e}")
        return None

def get_model_training_data(section_code, curator_data_folder, pdf_folder, abbr_full_form_excel):
    """
    Complete pipeline for creating BERT training dataset
    
    Pipeline Steps:
    1. Extract PDF segments (title, abstract, other sections)
    2. Filter and rank sentences containing keyphrases  
    3. Create tagged dataset with IOB labels
    4. Prepare final training data
    """
    
    print("="*100)
    print(" BERT TRAINING DATASET CREATION PIPELINE")
    print("="*100)
    
    pipeline_start = time.time()
    
    # # Setup sample data if needed
    # setup_sample_data()
    
    # Create output directories
    create_output_directories()
    
    # Step 1: Extract PDF segments
    segmented_data_path = step1_extract_pdf_segments(
        section_code, curator_data_folder, pdf_folder, abbr_full_form_excel
    )
    
    if not segmented_data_path:
        print(" Pipeline failed at Step 1: PDF segmentation")
        return None, False
    
    # Step 2: Filter sentences
    ta_sentences_path, other_sentences_path = step2_filter_sentences(segmented_data_path)
    
    if not ta_sentences_path or not other_sentences_path:
        print(" Pipeline failed at Step 2: Sentence filtering")
        return None, False
    
    # Step 3: Create tagged dataset
    tagged_dataset_path = step3_create_tagged_dataset()
    
    if not tagged_dataset_path:
        print(" Pipeline failed at Step 3: Dataset tagging")
        return None, False
    
    # Step 4: Prepare final training data
    final_training_path = step4_prepare_training_data()
    
    if not final_training_path:
        print(" Pipeline failed at Step 4: Training data preparation")
        return None, False
    
    # Pipeline completion
    pipeline_end = time.time()
    total_time = pipeline_end - pipeline_start
    
    print("="*100)
    print(" PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*100)
    print(f"  Total processing time: {total_time:.2f} seconds")
    print(f" Final training dataset: {final_training_path}")
    print(f" Next step: Use train_module_with_tagged_data_input.py for model training")
    print("="*100)
    
    return final_training_path, True




if __name__ == "__main__":
    # Configuration
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized\curated_data_sample" 
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized\utility_data\text_abbreviations.xlsx" 

    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"

    print(" Starting BERT Training Dataset Creation Pipeline")
    print(f" Curator data folder: {curator_data_folder}")
    print(f" PDF folder: {pdf_folder}")
    print(f" Abbreviations file: {abbr_full_form_excel}")
    print()

    # Run complete pipeline
    final_result, success = get_model_training_data(
        section_code, curator_data_folder, pdf_folder, abbr_full_form_excel
    )
    
    if success:
        print(f" SUCCESS: Final training dataset ready at: {final_result}")
        print(f" You can now run: python train_module_with_tagged_data_input.py")
    else:
        print(f" FAILURE: Pipeline did not complete successfully")
