"""
This module provides functionality to extract and process not_required_content sections from academic papers.

The main function not_required_content() takes a list of text content from a not_required_content section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed not_required_content text and page number.

Author: <PERSON> Jadhav
Date: 13-02-25
"""


import re


def get_not_required_content(content_list, not_required_location):
    """
    Extract the not_required_content content from the material_methods section of a paper.
    Args:
        content_list (list): List of strings from the not_required_content section.

    Returns:
        str: A string containing the not_required_content content.
    """
    section_content = {}
    full_text = " "
    # print(f"{content_list =}")
    if content_list:
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text += item

        section_content["not_required_content"] = full_text.strip()
        section_content["page_number"] = not_required_location

        return section_content
    else:
        return full_text
