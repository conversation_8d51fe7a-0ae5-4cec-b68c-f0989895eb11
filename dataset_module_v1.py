
import os
import pandas as pd
import numpy as np
from typing import List, Dict
import glob

try:
    from transformers import AutoTokenizer
except ImportError:
    AutoTokenizer = None

from config import LABELS, label2id, id2label, MAX_LENGTH

def tokenize_and_align_labels(sentence: str, keyphrases: list, tokenizer=None):
    """Tokenize sentence and create IOB labels in required format"""
    
    if tokenizer is None:
        # Basic tokenization
        tokens = sentence.split()
        labels = ['O'] * len(tokens)
        
        # Simple keyphrase matching
        for keyphrase in keyphrases:
            keyphrase_tokens = keyphrase.split()
            for i in range(len(tokens) - len(keyphrase_tokens) + 1):
                if tokens[i:i+len(keyphrase_tokens)] == keyphrase_tokens:
                    labels[i] = 'B-KEY'
                    for j in range(1, len(keyphrase_tokens)):
                        if i+j < len(labels):
                            labels[i+j] = 'I-KEY'
        
        return {
            'tokens': tokens,
            'labels': labels
        }
    
    # BERT tokenization with special tokens
    tokenized = tokenizer(
        sentence,
        add_special_tokens=True,
        return_offsets_mapping=True,
        truncation=True,
        max_length=512
    )
    
    tokens = tokenizer.convert_ids_to_tokens(tokenized['input_ids'])
    offsets = tokenized['offset_mapping']
    
    # Find keyphrase spans in original sentence
    spans = []
    for keyphrase in keyphrases:
        keyphrase_lower = keyphrase.lower()
        sentence_lower = sentence.lower()
        start = 0
        while True:
            pos = sentence_lower.find(keyphrase_lower, start)
            if pos == -1:
                break
            spans.append((pos, pos + len(keyphrase)))
            start = pos + 1
    
    # Create IOB tags
    labels = []
    for i, (token, (start, end)) in enumerate(zip(tokens, offsets)):
        if start == end == 0:  # Special tokens
            labels.append('SPECIAL')
            continue
        
        tag = 'O'
        for span_start, span_end in spans:
            if start >= span_start and start < span_end:
                tag = 'B-KEY' if start == span_start else 'I-KEY'
                break
        labels.append(tag)
    
    return {
        'tokens': tokens,
        'labels': labels
    }

def process_multiple_excel_files(input_folder, tokenizer=None, output_folder="processed_datasets", 
                                combined_output_file="combined_dataset.xlsx", test_size=0.1, val_size=0.1, 
                                save_debug=True, use_existing_tagged=False):
    """Process multiple Excel files and create tagged dataset in the required format"""
    
    os.makedirs(output_folder, exist_ok=True)
    all_data = []
    
    for file_name in os.listdir(input_folder):
        if file_name.endswith('.xlsx'):
            file_path = os.path.join(input_folder, file_name)
            print(f"Processing: {file_path}")
            
            try:
                df = pd.read_excel(file_path)
                
                # Process each row to create the required format
                for _, row in df.iterrows():
                    sentence = str(row.get('sentence', ''))
                    keyphrases_str = str(row.get('term_found', ''))
                    
                    if sentence and sentence != 'nan':
                        # Parse keyphrases
                        if keyphrases_str and keyphrases_str != 'nan':
                            keyphrases = [kp.strip() for kp in keyphrases_str.split(',') if kp.strip()]
                        else:
                            keyphrases = []
                        
                        # Tokenize and create labels
                        result = tokenize_and_align_labels(sentence, keyphrases, tokenizer)
                        
                        # Create single row in required format
                        all_data.append({
                            'sentence': sentence,
                            'tokens': result['tokens'],
                            'tags': result['labels'],
                            'term_found': keyphrases
                        })
                        
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    # Create combined dataset
    if all_data:
        df_combined = pd.DataFrame(all_data)
        combined_path = os.path.join(output_folder, combined_output_file)
        df_combined.to_excel(combined_path, index=False)
        print(f"Combined dataset saved: {combined_path}")
        return combined_path
    
    return None

def load_all_tagged_files_for_training(processed_folder: str) -> str:
    """Load all tagged files for training"""
    
    # Look for combined dataset first
    combined_files = glob.glob(os.path.join(processed_folder, "*combined*.xlsx"))
    
    if combined_files:
        latest_file = max(combined_files, key=os.path.getctime)
        print(f"Found combined dataset: {latest_file}")
        return latest_file
    
    # Look for debug files
    debug_files = glob.glob(os.path.join(processed_folder, "*debug*.xlsx"))
    
    if debug_files:
        print(f"Found {len(debug_files)} debug files, combining...")
        
        all_data = []
        for file_path in debug_files:
            try:
                df = pd.read_excel(file_path)
                all_data.append(df)
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            output_path = os.path.join(processed_folder, "combined_tagged_dataset_for_training.xlsx")
            combined_df.to_excel(output_path, index=False)
            print(f"Created combined training file: {output_path}")
            return output_path
    
    print("No tagged files found for training")
    return None

if __name__ == "__main__":
    # Test the module
    input_folder = "cth_sent_excel_data"
    output_folder = "processed_datasets"
    
    # Initialize tokenizer if available
    tokenizer = None
    if AutoTokenizer:
        try:
            tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
        except Exception as e:
            print(f"Could not load tokenizer: {e}")
    
    # Process files
    result = process_multiple_excel_files(
        input_folder=input_folder,
        tokenizer=tokenizer,
        output_folder=output_folder,
        save_debug=True
    )
    
    if result:
        print(f" Processing completed: {result}")
    else:
        print(" Processing failed")
