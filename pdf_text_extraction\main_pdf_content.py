
###pdf_text_extraction\main_pdf_content.py

"""
This module processes PDF files to extract structured content including:
- Abstract
- Introduction and research aims
- Experimental procedures
- Results and discussions
- Conclusions
- Figures and tables

The module uses various helper functions to parse and extract content from different sections
of PDF documents. It processes text content, figures and tables to create a structured representation of the PDF content.

Author: Anand Jadhav
Date: 13-06-25
"""

## Imports required for the module
import sys
import os
import re
import fitz  # type: ignore
from bs4 import BeautifulSoup  # type: ignore
import time
import logging



current_dir = os.path.dirname(os.path.abspath(__file__))

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

## Custom Imports
from helper_functions.get_headingwise_paragraphs import pdf_to_html, extract_paragraphs_from_html,extract_paragraphs_from_html_for_smaller_articles

# from helper_functions.get_introduction import get_aim_of_paper
from helper_functions.get_abstract_content import get_abstract
from helper_functions.get_abstract_aim import get_aim_of_abstract
from helper_functions.get_conclusion import get_conclusion
from helper_functions.get_results import get_results
from helper_functions.get_experimental_procedures import get_experimental
# from helper_functions.get_image_text import extract_and_clean_text
from helper_functions.get_figure_titles import extract_figure_paragraphs
# from helper_functions.get_pdf_table_text import extract_tables_with_page_numbers
from helper_functions.get_pdf_superscripts import get_all_superscripts
from helper_functions.get_results_superscripts import get_results_without_references
from helper_functions.get_material_methods import get_materials
from helper_functions.get_not_required import get_not_required_content

# ## Custom Imports
# from helper_functions.get_headingwise_paragraphs import pdf_to_html, extract_paragraphs_from_html,extract_paragraphs_from_html_for_smaller_articles

# # from helper_functions.get_introduction import get_aim_of_paper
# from helper_functions.get_abstract_content import get_abstract
# from helper_functions.get_abstract_aim import get_aim_of_abstract
# from helper_functions.get_conclusion import get_conclusion
# from helper_functions.get_results import get_results
# from helper_functions.get_experimental_procedures import get_experimental
# # from helper_functions.get_image_text import extract_and_clean_text
# from helper_functions.get_figure_titles import extract_figure_paragraphs
# # from helper_functions.get_pdf_table_text import extract_tables_with_page_numbers
# from helper_functions.get_pdf_superscripts import get_all_superscripts
# from helper_functions.get_results_superscripts import get_results_without_references
# from helper_functions.get_material_methods import get_materials
# from helper_functions.get_not_required import get_not_required_content




def get_pdf_text_main(pdf_file, tan_name,  title_list, article_size, research):
    """
        Process a PDF file to extract structured content from different sections.

        Args:
            pdf_file (str): Path to the PDF file to process
            tan_name (str): Name of the TAN document
            title_list (list): List of section titles to look for in the PDF
            article_size (str): Size category of the article ('SMALL' or 'LARGE')
            research (bool): Flag indicating if this is a research paper (True) or not (False)

        Returns:
            dict: Dictionary containing extracted content with keys for different sections:
                - abstract: Abstract content
                - introduction: Introduction and research aims
                - experimental: Experimental procedures
                - results: Results and discussions
                - conclusions: Conclusions
                - figures: Figures and their captions
                - tables: Tables and their content
            int: Total number of pages in the PDF
    """

    pdf_content = {}
    total_pages =0
    article_size = "LARGE"
    try:
        ## Extract superscripts from pdf document
        superscript_list = get_all_superscripts(pdf_file)
        # print(f"{superscript_list=}")


        # Extract paragraphs and headers
        header_location, header_paragraph, total_pages, article_size = extract_paragraphs_from_html(
            pdf_file,tan_name,  title_list
        )
        # print(f"1.{header_location=}")
        # print(f"{header_paragraph=}")
        # print(f"{header_paragraph['introduction']=}")


        # Safely get segments from header_paragraph
        # intro_list =        header_paragraph.get("INTRODUCTION", [])
        abstract_list =     header_paragraph.get("ABSTRACT", [])
        experimental_list = header_paragraph.get("EXPERIMENTAL PROCEDURES", [])
        results_list =      header_paragraph.get("Results and discussions", [])
        conclusion_list =   header_paragraph.get("Conclusions", [])
        materials_methods_list =   header_paragraph.get("Materials and methods", [])
        not_required_list =   header_paragraph.get("Discussions", [])

        # print(f"{materials_methods_list=}")


        ## Get the segment location on pdf pages
        # intro_location =        header_location.get("INTRODUCTION", [])
        abstract_location =     header_location.get("ABSTRACT", [])
        experimental_location = header_location.get("EXPERIMENTAL PROCEDURES", [])
        results_location =      header_location.get("Results and discussions", [])
        conclusion_location =   header_location.get("Conclusions", [])
        materials_methods_location =   header_location.get("Materials and methods", [])
        not_required_list_location =   header_location.get("Discussions", [])


        # # 1. Get aim of paper from the INTRODUCTION segment
        # # print(f"{intro_list=}")
        # # print(f"{intro_location=}")
        # introduction_content = get_aim_of_paper(intro_list, intro_location, superscript_list)
        # # print(f"{introduction_content=}")
        # if introduction_content:
        #     # print(f"{introduction_content=}")
        #     pdf_content["introduction"] = introduction_content

        # 2. Get abstract from the ABSTRACT segment
        if research:
            # 2a. Get abstract from the ABSTRACT segment for research papers
            abstract_content = get_aim_of_abstract(abstract_list, abstract_location)
            if abstract_content:
                pdf_content["abstract"] = abstract_content
        else:
            # 2b. Get abstract from the ABSTRACT segment for non-research papers
            abstract_content = get_abstract(abstract_list, abstract_location)
            if abstract_content:
                pdf_content["abstract"] = abstract_content


        # 3. Get experimental procedures from the EXPERIMENTAL PROCEDURES segment
        experimental_content = get_experimental(experimental_list, experimental_location)
        if experimental_content:
            pdf_content["experimental_procedures"] = experimental_content


        # 4. Get results from the RESULTS segment
        results_content = get_results(results_list, results_location)
        # print(f"{results_content=}")
        ## References not cleaned up
        # if results_content:
        #     pdf_content["results"] = results_content
        ## References cleaned up from results_content

        cleaned_results_content = get_results_without_references(results_content, results_location, superscript_list)
        # print(f"{cleaned_results_content=}")
        if cleaned_results_content:
            pdf_content["results"] = cleaned_results_content


        # 5. Get conclusion from the CONCLUSION segment
        conclusion_content = get_conclusion(conclusion_list, conclusion_location)
        # print("conclusion_content", conclusion_content)
        if conclusion_content:
            pdf_content["conclusion"] = conclusion_content

        # # 6. Get table content from the PDF document
        # table_data_list = extract_tables_with_page_numbers(pdf_file)
        # if table_data_list:
        #     pdf_content["table_content"] = table_data_list

        # # 7. Get figure titles from the PDF document
        # figure_titles = extract_figure_paragraphs(header_paragraph, header_location)
        # if figure_titles:
        #     # print(f"{figure_titles =}")
        #     pdf_content["figure_titles"] = figure_titles

        # # 8. Get figure content from the PDF document
        # transformed_figure_texts = extract_and_clean_text(pdf_file)
        # if transformed_figure_texts:
        #     pdf_content["figures_content"] = transformed_figure_texts


        # 9. Get materials and methods  from the PDF document
        materials_content = get_materials(materials_methods_list, materials_methods_location)
        # # print("materials_content", materials_content)
        if materials_content:
            pdf_content["materials_methods"] = materials_content

        # 10. Get Not Required Items from the PDF document
        not_required_content = get_not_required_content(not_required_list, not_required_list_location)
        # print("not_required_content", not_required_content)
        if materials_content:
            pdf_content["not_required_content"] = not_required_content


        # if total_pages <=5:
        pdf_content, total_pages, article_size = process_smaller_pdf_content(pdf_content, pdf_file, tan_name, title_list, total_pages)

        return pdf_content, total_pages, article_size

    except Exception as error:
        ### Log the error
        logging.error("MAIN function Error: %s", str(error))
        return pdf_content, total_pages, article_size
        # pass



def process_pdf_content(pdf_content, pdf_file, tan_name, title_list, total_pages):
    article_size = None
    # Keys to check for content presence
    keys_to_check = [
        "abstract", "introduction", "experimental_procedures", "results",
        "conclusion", "table_content",  "figure_titles", "figures_content"
    ]

    # Function to validate content based on inner keys
    def has_valid_content(value):
        if isinstance(value, dict):
            # Check if any inner key's value has length greater than 10
            return any(len(str(subvalue).strip()) > 10 for subvalue in value.values())
        return len(str(value).strip()) > 10  # For non-dictionary values

    # Collect results for all keys
    results = [
        has_valid_content(pdf_content.get(key, {}))  # Defaults to empty dict if key is missing
        for key in keys_to_check
    ]
    # print(f"{results =}")
    # If all results are False and total_pages is less than 4, return the original inputs
    if  not any(results) and total_pages < 5:
        # print(f"1.{results =}")

        # Extract paragraphs and headers
        header_location, header_paragraph, total_pages, article_size = extract_paragraphs_from_html(
            pdf_file,tan_name,  title_list
        )
        # Safely get segments from header_paragraph
        # pdf_info_list =        header_paragraph.get("PDF_INFO", [])
        return header_paragraph, total_pages, article_size
    else:
        return pdf_content, total_pages, article_size




def process_smaller_pdf_content(pdf_content, pdf_file, tan_name, title_list, total_pages):
    article_size = "LARGE"
    try:
    # print(f"{pdf_content =}")
    # Keys to check for content presence
        keys_to_check = [

            "introduction",
            "abstract",
            "experimental_procedures",
            "results",
            "conclusion"
        ]

        # Function to validate content based on inner keys
        def has_valid_content(value):
            if isinstance(value, dict):
                # Check if any inner key's value has length greater than 10
                return any(len(str(subvalue).strip()) > 10 for subvalue in value.values())
            return len(str(value).strip()) > 10  # For non-dictionary values


        # Collect results for all keys
        results = [
            has_valid_content(pdf_content.get(key, {}))  # Defaults to empty dict if key is missing
            for key in keys_to_check
        ]
        # print(f"{results =}")
        # If all results are False and total_pages is less than 4, return the original inputs
        if  not any(results) or total_pages <= 5:  ###and
            # return pdf_file, tan_name, title_list, research
            pdf_to_html(pdf_file)
            pdf_content, total_pages, article_size = extract_paragraphs_from_html_for_smaller_articles(pdf_file, tan_name, title_list)
            # print(f"1.{pdf_content =}")

            # Otherwise, return the pdf_content for further processing
            return pdf_content, total_pages, article_size
        else:
            # print(f"2.{pdf_content =}")
            return pdf_content, total_pages, article_size
    except Exception as error:
        ### Log the error
        logging.error("process_smaller_pdf_content Error: %s", str(error))
        return pdf_content, total_pages, article_size
        # pass




if __name__ == "__main__":
    logging.basicConfig(
        filename="main_pdf_content.log",
        level=logging.ERROR,
        format="%(asctime)s - %(levelname)s: %(message)s",
    )
    start = time.time()

    # Variations for different sections of a document
    intro_list = [
        "INTRODUCTION",
        "I N T R O D U C T I O N",
        "I n t r o d u c t i o n",
        "Introduction",
        "Background",
        "Introduction and Background",
        "B A C K G R O U N D",

    ]
    abstract_list = [
        "ABSTRACT",
        "Abstract",
        "A B S T R A C T",
        "a b s t r a c t",
        "Summary of Findings",
        "Abst.",
        "Summary",
        "S U M M A R Y",
    ]
    keywords_list = [
        "KEYWORDS",
        "Keywords",
        "K E Y W O R D S",
        "Key Terms",
        "Topics",
    ]
    experimental_list = [
        "EXPERIMENTAL PROCEDURES",
        "Experimental section",
        "Experimental Sections",
        "Experimental Section",
        "EXPERIMENTAL SECTION",
        "Experimental Section",
        "Experimental section",
        "■ EXPERIMENTAL SECTION",
        "EXPERIMENTAL PART",
        "Experimental Part",
        "Experimental part",
        "EXPERIMENTAL BIOLOGICAL PART",
        "EXPERIMENTAL CHEMICAL PART",
        "EXPERIMENTAL",
        "Experimental",
        "Experiment",
    ]
    results_list = [
        "Results and discussions",
        "R E S U L T S  A N D  D I S C U S S I O N",
        "CALCULATIONS, RESULTS, AND DISCUSSION",
        "EXPERIMENT RESULTS AND DISCUSSION",
        "Experiment results and discussion",
        "Experiment Results and Discussion",
        "Experiment Results And Discussion",
        "Experimental Results And Discussion",
        "Experimental results and discussion",
        "Experimental Results and Discussion",
        "Experimental Results and discussion",
        "RESULTS AND DISCUSSION",
        "RESULTS AND DISCUSSIONS",
        "RESULT AND DISCUSSION",
        "Results and Discussion",
        "Result and discussion",
        "Result and Discussion",
        "Results and Discussions",
        "Result and Discussions",
        "Experimental results",
        "Experimental Results",
        "EXPERIMENTAL RESULTS",
        "Results and discussion",
        "Findings",
        "Outcomes",
        "RESULTS",
        "R E S U L T S",
        "Results",
        "Result",

    ]
    materials_methods_list = [
        "Materials and methods",
        "Materials and Method",
        "Material and Methods",
        "Material and Method",
        "Methodology",
        "Methods",
        # "2 | METHODS",
        "Materials",
    ]
    conclusion_list = [
        "Conclusions",
        # "• CONCLUSIONS:",
        "CONCLUSION OR SUMMARY AND OUTLOOK",
        "Conclusion or summary and outlook",
        "Conclusion or Summary and Outlook",
        "Conclusions and future prospects",
        "Conclusions and Future Directions",
        "Conclusions and research needs",
        "Conclusion and future research",
        "Conclusions and perspectives",
        "Conclusions and Perspectives",
        "Conclusions and Discussions",
        "Conclusion and Discussion",
        "Conclusions and Discussion",
        "Conclusions and discussion",
        "C O N C L U S I O N S",
        "C O N C L U S I O N",
        "SUMMARY AND OUTLOOK",
        "Summary and Outlook",
        "Summary And Outlook",
        "Summary and outlook",
        "Conclusion",
        "CONCLUSIONS",
        "CONCLUSION",
    ]
    acknowledgments_list = [
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E D G M E N T S",
        "Acknowledgments:",
        "Acknowledgments",
        "Acknowledgements",
        "Acknowledgement",
        "Acknowlegments",
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "ACKNOWLEGMENTS",
        "A C K N O W L E D G M E N T S",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E G M E N T S",
    ]
    references_list = [
        "REFERENCES",
        "References and notes",
        "References and Notes",
        "Reference List",
        "R E F E R E N C E S",
        "R e f e r e n c e s",
        "References",
        "B I B L I O G R A P H Y",
        "B i b l i o g r a p h y",
        "Bibliography",
        "REFERENCES",
        "References",
        "BIBLIOGRAPHY",
        "B I B L I O G R A P H Y",
        "Bibliography",
    ]

    not_required_list = [ "Discussions", "DISCUSSIONS", "DISCUSSION","Discussion", "CONFLICT  OF  INTEREST",]
    title_list = [
        intro_list,
        abstract_list,
        keywords_list,
        experimental_list,
        results_list,
        materials_methods_list,
        conclusion_list,
        acknowledgments_list,
        references_list,
        not_required_list
    ]


    # pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06000703B\06000703B.article.002.pdf"

    # pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\05966057Z\05966057Z.article.001.pdf"

    # pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\05968330N\05968330N.article.002.pdf"

    # pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06086824H\06086824H.article.001.pdf"

    # pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06040964J\06040964J.article.001.pdf" ## small

    # pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06072810C\06072810C.article.001.pdf"

    pdf_file = r"\\***************\cas_app_files\UII\INTEGRATED\982560\06723376C\06723376C.article.001.pdf"



    tan_name = (pdf_file.split("\\")[-1]).split(".")[0]

    ## Get the Document type Research or non-research
    research = True  ## False
    article_size = "Large"


    ## Main function
    pdf_content, total_pages,article_size = get_pdf_text_main(pdf_file, tan_name,  title_list,article_size, research)
    # Write to text file
    with open(f"text_folder\\pdf_content_{tan_name}.txt", "w", encoding="utf8") as f:
        for key, value in pdf_content.items():
            # f.write(f"{key}\n")
            f.write(f"\t {value}\n")
    print()
    print(f"Time required is {round((time.time() -start),2)} Seconds...")

######################################################################
