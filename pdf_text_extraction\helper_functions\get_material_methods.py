"""
This module provides functionality to extract and process material_methods sections from academic papers.

The main function get_material_methods() takes a list of text content from a material_methods section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed material_methods text and page number.

Author: <PERSON>hav
Date: 13-02-25
"""


import re


def get_materials(content_list, material_methods_location):
    """
    Extract the material_methods content from the material_methods section of a paper.
    Args:
        material_methods_list (list): List of strings from the material_methods section.

    Returns:
        str: A string containing the material_methods content.
    """
    section_content = {}
    full_text = " "
    # print(f"{content_list =}")
    if content_list:
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text += item

        section_content["material_methods"] = full_text.strip()
        section_content["page_number"] = material_methods_location

        return section_content
    else:
        return full_text
