import re
import html
import unicodedata
import pandas as pd



# Mapping for subscripts
SUBSCRIPT_MAP = str.maketrans("0123456789+-=()", "₀₁₂₃₄₅₆₇₈₉₊₋₌₍₎")

# Mapping special symbols to ASCII
SYMBOL_REPLACEMENTS = {
    "≥": ">",
    "≤": "<",
    "±": "+/-",
    "–": "-",
    "—": "-",
}

# Mapping Greek letters (lower + upper) to names
GREEK_MAP = {
    'α': 'alpha', 'β': 'beta', 'γ': 'gamma', 'δ': 'delta', 'ε': 'epsilon',
    'ζ': 'zeta', 'η': 'eta', 'θ': 'theta', 'ι': 'iota', 'κ': 'kappa',
    'λ': 'lambda', 'μ': 'mu', 'ν': 'nu', 'ξ': 'xi', 'ο': 'omicron',
    'π': 'pi', 'ρ': 'rho', 'σ': 'sigma', 'τ': 'tau', 'υ': 'upsilon',
    'φ': 'phi', 'χ': 'chi', 'ψ': 'psi', 'ω': 'omega',

    'Α': 'Alpha', 'Β': 'Beta', 'Γ': 'Gamma', 'Δ': 'Delta', 'Ε': 'Epsilon',
    'Ζ': 'Zeta', 'Η': 'Eta', 'Θ': 'Theta', 'Ι': 'Iota', 'Κ': 'Kappa',
    'Λ': 'Lambda', 'Μ': 'Mu', 'Ν': 'Nu', 'Ξ': 'Xi', 'Ο': 'Omicron',
    'Π': 'Pi', 'Ρ': 'Rho', 'Σ': 'Sigma', 'Τ': 'Tau', 'Υ': 'Upsilon',
    'Φ': 'Phi', 'Χ': 'Chi', 'Ψ': 'Psi', 'Ω': 'Omega'
}

def replace_subscripts(text):
    """Replace HTML <sub>...</sub> with Unicode subscripts."""
    def subscript_replacer(match):
        return match.group(1).translate(SUBSCRIPT_MAP)
    return re.sub(r"<sub>(.*?)</sub>", subscript_replacer, text, flags=re.IGNORECASE)

def replace_greek_letters(text):
    """Replace Greek letters with English names."""
    return ''.join(GREEK_MAP.get(ch, ch) for ch in text)

def clean_text_0(text):
    """Full cleaning with special entity and Greek handling."""
    if pd.isna(text):
        return ""

    text = str(text)
    text = text.replace("\\r\\n", " ").replace("\r\n", " ")
    # Decode HTML entities (&ge;, &lt;sub&gt;, etc.)
    text = html.unescape(text)

    # Replace subscripts with Unicode subscripts
    text = replace_subscripts(text)

    # Remove any remaining HTML tags
    text = re.sub(r"<.*?>", " ", text)

    # Normalize unicode
    text = unicodedata.normalize("NFKC", text)

    # Replace special symbols with preferred ASCII
    for symbol, replacement in SYMBOL_REPLACEMENTS.items():
        text = text.replace(symbol, replacement)

    # Replace Greek letters
    text = replace_greek_letters(text)

    # Replace \r\n, \n, \r with space
    text = re.sub(r"[\r\n]+", " ", text)

    # Remove multiple spaces
    text = re.sub(r"\s+", " ", text).strip()

    return text

def clean_text(text):
    """Full cleaning with special entity and Greek handling."""
    if pd.isna(text):
        return ""

    text = str(text)
    text = text.replace("\\r\\n", " ").replace("\r\n", " ")

    # Repeatedly decode HTML entities until fully resolved
    prev = None
    while prev != text:
        prev = text
        text = html.unescape(text)

    # Replace subscripts with Unicode subscripts
    text = replace_subscripts(text)

    # Remove any remaining HTML tags
    text = re.sub(r"<.*?>", " ", text)

    # Normalize unicode
    text = unicodedata.normalize("NFKC", text)

    # Replace special symbols with preferred ASCII
    for symbol, replacement in SYMBOL_REPLACEMENTS.items():
        text = text.replace(symbol, replacement)

    # Replace Greek letters
    text = replace_greek_letters(text)

    # Replace \r\n, \n, \r with space
    text = re.sub(r"[\r\n]+", " ", text)

    # Remove multiple spaces
    text = re.sub(r"\s+", " ", text).strip()

    return text


# def filter_terms_in_text(terms, text):
#     """
#     Keeps only terms that appear in the text (case-insensitive, regex match).
#     Args:
#         terms (list): List of terms (strings)
#         text (str): Cleaned text to search in
#     """
#     if not isinstance(terms, list):
#         return []

#     found_terms = []
#     for term in terms:
#         if not isinstance(term, str) or term.strip() == "":
#             continue
#         pattern = r"\b{}\b".format(re.escape(term))
#         if re.search(pattern, text, flags=re.IGNORECASE):
#             found_terms.append(term)

#     return found_terms




def load_abbreviation_map(abbrev_file):
    """
    Load abbreviation mapping once from Excel into list of tuples.
    """
    df_abbr = pd.read_excel(abbrev_file, dtype=str)
    mapping = [
        (row['short_form'].strip(), row['long_form'].strip())
        for _, row in df_abbr.iterrows()
        if pd.notna(row['short_form']) and pd.notna(row['long_form'])
    ]
    return mapping


def replace_abbreviations(text, abbr_map):
    """
    Replace abbreviations in a text using a preloaded abbreviation map.
    """
    if pd.isna(text):
        return ""

    for abb, full in abbr_map:
        # Escape regex special chars in abbreviation
        escaped = re.escape(abb)
        # Use word boundaries but allow dots/hyphens inside abbreviation
        pattern = rf"(?<![A-Za-z0-9]){escaped}(?![A-Za-z0-9])"
        text = re.sub(pattern, full, text, flags=re.IGNORECASE)
    return text


def clean_title_abs_text(df, abbr_full_form_excel):
    """
    Cleans 'title' and 'abstract', replaces abbreviations,
    and keeps metadata about term list lengths.
    """
    df = df.copy()

    # # Record length before filtering
    # df["terms_list_len_before"] = df["terms_list"].apply(
    #     lambda x: len(x) if isinstance(x, list) else 0
    # )

    # Clean text first
    df["title"] = df["title"].apply(clean_text)
    df["abstract"] = df["abstract"].apply(clean_text)

    # Load abbreviation map once
    abbr_map = load_abbreviation_map(abbr_full_form_excel)

    # Apply abbreviation replacement
    df["title"] = df["title"].apply(lambda x: replace_abbreviations(x, abbr_map))
    df["abstract"] = df["abstract"].apply(lambda x: replace_abbreviations(x, abbr_map))
    # print("text cleaned with abbreviations replaced....")
    return df



    # print(df['abstract'][0])
    # Filter terms for each row
    # df["terms_list"] = df.apply(
    #     lambda row: filter_terms_in_text(row["terms_list"], row["title"]),
    #     axis=1
    # )

    # Record length after filtering
    # df["terms_list_len_after"] = df["terms_list"].apply(len)

    # Drop rows with no terms left
    # df = df[df["terms_list_len_after"] > 0].reset_index(drop=True)

    # return df




if __name__ == "__main__":

    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_2025\utility_data\text_abbreviations.xlsx"
    final_df = pd.read_excel(r"dataset_with_title_abstract_982560_5.xlsx")


    filtered_df = clean_title_abs_text(final_df, abbr_full_form_excel)

    print(filtered_df)
    filtered_df.to_excel("clustering_dataset_with_text_cleaned.xlsx", index=False)