from main_pdf_content import *


import pandas as pd
import json
import requests
import fitz
import re
from tqdm import tqdm



def process_pdfs_with_text_0(df: pd.DataFrame, title_list, research) -> pd.DataFrame:
    """
    Process PDFs from dataframe using get_pdf_text_main.
    Expands dictionary output into new dataframe columns.

    Args:
        df (pd.DataFrame): Input dataframe with columns ['TAN_NAME', 'pdf_path']
        title_list (list): Titles list needed by get_pdf_text_main
        research (str): Research parameter for get_pdf_text_main

    Returns:
        pd.DataFrame: Original df with new columns for each pdf_content key + metadata
    """
    results = []

    for _, row in tqdm(df.iterrows()):
        pdf_file = row["pdf_path"]
        tan_name = row["TAN_NAME"]

        try:
            pdf_content, total_pages, article_size = get_pdf_text_main(
                pdf_file, tan_name, title_list, None, research
            )

            # Flatten dictionary into row
            row_result = row.to_dict()
            row_result.update(pdf_content)   # add dictionary keys as columns
            row_result["total_pages"] = total_pages
            row_result["article_size"] = article_size

            results.append(row_result)

        except Exception as e:
            print(f"Error processing {pdf_file}: {e}")
            row_result = row.to_dict()
            row_result["error"] = str(e)
            results.append(row_result)

    # Build dataframe with all expanded keys
    return pd.DataFrame(results)




def process_pdfs_with_text_1(df, title_list, article_size, research):
    """
    Reads pdf_path from dataframe, processes via get_pdf_text_main,
    and flattens pdf_content into dataframe columns.
    """
    all_rows = []

    for _, row in tqdm(df.iterrows()):
        pdf_file = row["pdf_path"]
        tan_name = row.get("tan_name", None)

        # Call your main function
        pdf_content, total_pages, article_size = get_pdf_text_main(
            pdf_file, tan_name, title_list, article_size, research
        )

        row_data = {"pdf_path": pdf_file, "tan_name": tan_name, "total_pages": total_pages}

        # Handle normal key-value pairs
        for key, value in pdf_content.items():
            if key == "figure_title":
                # special handling for list of dicts
                if isinstance(value, list):
                    figure_titles = []
                    for item in value:
                        if isinstance(item, dict) and "figure_title" in item:
                            figure_titles.append(item["figure_title"])
                    # Join multiple titles into one string OR keep as list
                    row_data["figure_title"] = " || ".join(figure_titles)
                else:
                    row_data["figure_title"] = str(value)
            else:
                row_data[key] = value

        all_rows.append(row_data)

    return pd.DataFrame(all_rows)




EXCEL_CELL_LIMIT = 32000  # (Excel's max =32767)

def sanitize_for_excel(text: str) -> str:
    """
    Ensure text is safe for Excel:
    - Remove problematic unicode
    - Truncate to Excel's max cell size
    """
    if not isinstance(text, str):
        text = str(text)
    # Remove/ignore invalid characters
    safe_text = text.encode("utf-8", errors="ignore").decode("utf-8", errors="ignore")
    # Truncate to Excel's safe length
    if len(safe_text) > EXCEL_CELL_LIMIT:
        safe_text = safe_text[:EXCEL_CELL_LIMIT] + " ...[TRUNCATED]"
    return safe_text


def extract_values(obj):
    """
    Recursively extract all string values from nested dict/list,
    ignoring keys named 'page_number'.
    """
    values = []
    if isinstance(obj, dict):
        for k, v in obj.items():
            if k == "page_number":
                continue
            values.extend(extract_values(v))
    elif isinstance(obj, list):
        for item in obj:
            values.extend(extract_values(item))
    else:
        values.append(str(obj))
    return values


def process_pdfs_with_text(df, title_list, article_size, research):
    """
    Reads pdf_path from dataframe, processes via get_pdf_text_main,
    and flattens pdf_content into dataframe columns.
    Only values are extracted (as strings). Nested dicts/lists are flattened.
    Text is sanitized and truncated for Excel.
    """
    all_rows = []

    for _, row in tqdm(df.iterrows(), total=len(df)):
        pdf_file = row["pdf_path"]
        tan_name = row.get("tan_name", None)

        try:
            # Call your main function
            pdf_content, total_pages, article_size = get_pdf_text_main(
                pdf_file, tan_name, title_list, article_size, research
            )

            row_data = {
                "pdf_path": pdf_file,
                "tan_name": tan_name,
                "total_pages": total_pages
            }

            for key, value in pdf_content.items():
                if key == "figure_title":
                    figure_titles = extract_values(value)
                    row_data["figure_title"] = sanitize_for_excel(". ".join(figure_titles))
                elif key in {"table_content", "not_required_content"}:
                    continue  # skip large or unwanted keys
                else:
                    flat_values = extract_values(value)
                    row_data[key] = sanitize_for_excel(". ".join(flat_values)) if flat_values else ""

            all_rows.append(row_data)

        except Exception as e:
            print(f"Error processing {pdf_file}: {e}")
            row_data = {"pdf_path": pdf_file, "tan_name": tan_name, "error": str(e)}
            all_rows.append(row_data)

    return pd.DataFrame(all_rows)


if __name__ == "__main__":  
    #sections of a document
    intro_list = [
        "INTRODUCTION",
        "I N T R O D U C T I O N",
        "I n t r o d u c t i o n",
        "Introduction",
        "Background",
        "Introduction and Background",
        "B A C K G R O U N D",

    ]
    abstract_list = [
        "ABSTRACT",
        "Abstract",
        "A B S T R A C T",
        "a b s t r a c t",
        "Summary of Findings",
        "Abst.",
        "Summary",
        "S U M M A R Y",
    ]
    keywords_list = [
        "KEYWORDS",
        "Keywords",
        "K E Y W O R D S",
        "Key Terms",
        "Topics",
    ]
    experimental_list = [
        "EXPERIMENTAL PROCEDURES",
        "Experimental section",
        "Experimental Sections",
        "Experimental Section",
        "EXPERIMENTAL SECTION",
        "Experimental Section",
        "Experimental section",
        "■ EXPERIMENTAL SECTION",
        "EXPERIMENTAL PART",
        "Experimental Part",
        "Experimental part",
        "EXPERIMENTAL BIOLOGICAL PART",
        "EXPERIMENTAL CHEMICAL PART",
        "EXPERIMENTAL",
        "Experimental",
        "Experiment",
    ]
    results_list = [
        "Results and discussions",
        "R E S U L T S  A N D  D I S C U S S I O N",
        "CALCULATIONS, RESULTS, AND DISCUSSION",
        "EXPERIMENT RESULTS AND DISCUSSION",
        "Experiment results and discussion",
        "Experiment Results and Discussion",
        "Experiment Results And Discussion",
        "Experimental Results And Discussion",
        "Experimental results and discussion",
        "Experimental Results and Discussion",
        "Experimental Results and discussion",
        "RESULTS AND DISCUSSION",
        "RESULTS AND DISCUSSIONS",
        "RESULT AND DISCUSSION",
        "Results and Discussion",
        "Result and discussion",
        "Result and Discussion",
        "Results and Discussions",
        "Result and Discussions",
        "Experimental results",
        "Experimental Results",
        "EXPERIMENTAL RESULTS",
        "Results and discussion",
        "Findings",
        "Outcomes",
        "RESULTS",
        "R E S U L T S",
        "Results",
        "Result",

    ]
    materials_methods_list = [
        "Materials and methods",
        "Materials and Method",
        "Material and Methods",
        "Material and Method",
        "Methodology",
        "Methods",
        # "2 | METHODS",
        "Materials",
    ]
    conclusion_list = [
        "Conclusions",
        # "• CONCLUSIONS:",
        "CONCLUSION OR SUMMARY AND OUTLOOK",
        "Conclusion or summary and outlook",
        "Conclusion or Summary and Outlook",
        "Conclusions and future prospects",
        "Conclusions and Future Directions",
        "Conclusions and research needs",
        "Conclusion and future research",
        "Conclusions and perspectives",
        "Conclusions and Perspectives",
        "Conclusions and Discussions",
        "Conclusion and Discussion",
        "Conclusions and Discussion",
        "Conclusions and discussion",
        "C O N C L U S I O N S",
        "C O N C L U S I O N",
        "SUMMARY AND OUTLOOK",
        "Summary and Outlook",
        "Summary And Outlook",
        "Summary and outlook",
        "Conclusion",
        "CONCLUSIONS",
        "CONCLUSION",
    ]
    acknowledgments_list = [
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E D G M E N T S",
        "Acknowledgments:",
        "Acknowledgments",
        "Acknowledgements",
        "Acknowledgement",
        "Acknowlegments",
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "ACKNOWLEGMENTS",
        "A C K N O W L E D G M E N T S",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E G M E N T S",
    ]
    references_list = [
        "REFERENCES",
        "References and notes",
        "References and Notes",
        "Reference List",
        "R E F E R E N C E S",
        "R e f e r e n c e s",
        "References",
        "B I B L I O G R A P H Y",
        "B i b l i o g r a p h y",
        "Bibliography",
        "REFERENCES",
        "References",
        "BIBLIOGRAPHY",
        "B I B L I O G R A P H Y",
        "Bibliography",
    ]

    not_required_list = [ "Discussions", "DISCUSSIONS", "DISCUSSION","Discussion", "CONFLICT  OF  INTEREST",]
    title_list = [
        intro_list,
        abstract_list,
        keywords_list,
        experimental_list,
        results_list,
        materials_methods_list,
        conclusion_list,
        acknowledgments_list,
        references_list,
        not_required_list
    ]



    ## Get the Document type Research or non-research
    research = True  ## False
    article_size = "Large"
    df = pd.read_excel(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\dataset_with_title_abstract_982560_updated.xlsx")
    # df = df.head(5)
    final_df = process_pdfs_with_text(df, title_list, article_size, research)

    final_df.to_csv(r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\dataset_with_title_abstract_982560_final_all.csv", index=False)