# ## section_weighted_extractor.py

# import re
# from typing import List, Dict
# import pandas as pd


# SECTION_WEIGHTS = {
#     'title': 1.0,
#     'abstract': 0.8,
#     'conclusion': 0.6,
#     'results': 0.4,
#     "experimental_procedures": 0.3,
#     "materials_methods":0.3
# }


# class BasicKeyphraseExtractor:
#     def __init__(self, section_weights: Dict[str, float] = None):
#         self.section_weights = section_weights or SECTION_WEIGHTS

#     def split_text_to_sentences(self, text: str) -> List[str]:
#         sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', str(text))
#         return [s.strip() for s in sentences if s.strip()]

#     def clean_text(self, text: str) -> str:
#         return re.sub(r"\s+", " ", str(text)).strip()

#     def filter_sentences_by_basic_quality(self, sentences: List[str]) -> List[str]:
#         filtered = []
#         for sent in sentences:
#             words = sent.split()
#             if len(words) < 8: # or len(words) > 100:
#                 continue
#             alnum_words = [w for w in words if any(c.isalnum() for c in w)]
#             if len(alnum_words) / len(words) < 0.3:
#                 continue
#             filtered.append(sent)
#         return filtered

#     def clean_reference_sentences(self, series: pd.Series) -> pd.Series:
#         ref_only_pattern = re.compile(r'^\s*\[\s*\d+(?:[\s,-]*\d+)*\s*\]\s*\.?\s*$')
#         ends_with_ref_pattern = re.compile(r'.*\[\s*\d+(?:[\s,-]*\d+)*\s*\]\s*\.?\s*$')

#         def filter_func(text):
#             if pd.isna(text):
#                 return text
#             sentences = [s.strip() for s in str(text).split("||")]
#             sentences = [
#                 s for s in sentences
#                 if s and not ref_only_pattern.match(s) and not ends_with_ref_pattern.match(s)
#             ]
#             return " || ".join(sentences) if sentences else None

#         return series.apply(filter_func)

#     def drop_number_heavy_sentences(self, series: pd.Series, length_threshold=200, num_ratio_threshold=0.3) -> pd.Series:
#         def tokenize(sentence):
#             return re.findall(r'\w+|\S', str(sentence))

#         def is_number_heavy(sentence):
#             if len(sentence) < 40:
#                 return False
#             tokens = tokenize(sentence)
#             if not tokens:
#                 return False
#             digit_count = sum(1 for t in tokens if t.isdigit())
#             ratio = digit_count / len(tokens)
#             return len(sentence) > length_threshold and ratio > num_ratio_threshold

#         def filter_func(text):
#             if pd.isna(text):
#                 return text
#             sentences = [s.strip() for s in str(text).split("||")]
#             filtered = [s for s in sentences if not is_number_heavy(s)]
#             return " || ".join(filtered) if filtered else None

#         return series.apply(filter_func)

#     def extract_relevant_sentences(self, doc_segments: Dict[str, str], keyphrases: List[str]) -> Dict[str, List[str]]:
#         selected_sentences = {}
#         kp_set = set(kp.lower() for kp in keyphrases)

#         for segment, text in doc_segments.items():
#             if segment not in self.section_weights:
#                 continue

#             sentences = self.split_text_to_sentences(text)
#             sentences = [self.clean_text(s) for s in sentences]
#             sentences = self.filter_sentences_by_basic_quality(sentences)

#             filtered = []
#             for sent in sentences:
#                 sent_lc = sent.lower()
#                 if any(kp in sent_lc for kp in kp_set):
#                     filtered.append(sent)

#             max_sentences = max(1, int(len(filtered) * self.section_weights[segment]))
#             if len(filtered) > max_sentences:
#                 filtered = filtered[:max_sentences]

#             # Format filtered sentences into a single string with '||' separator
#             combined = " || ".join(filtered) if filtered else None

#             # Convert to pandas Series temporarily for cleaning functions expecting Series
#             s = pd.Series([combined])
#             s = self.clean_reference_sentences(s)
#             s = self.drop_number_heavy_sentences(s)
#             cleaned_sentences_str = s.iloc[0] if not s.empty else None

#             if cleaned_sentences_str:
#                 # Split back into list of sentences for output
#                 cleaned_sentences = [s.strip() for s in cleaned_sentences_str.split("||") if s.strip()]
#             else:
#                 cleaned_sentences = []

#             selected_sentences[segment] = cleaned_sentences

#         return selected_sentences
    
#     def calculate_sentence_importance_score(self, sentence: str, keyphrases: List[str], section: str, position_in_section: int, total_sentences: int) -> float:
#         """Calculate importance score based on section weight, keyphrase matches, and position"""
#         base_weight = self.section_weights.get(section, 0.1)
        
#         # Keyphrase matching score
#         kp_set = set(kp.lower() for kp in keyphrases)
#         sent_lower = sentence.lower()
#         keyphrase_matches = sum(1 for kp in kp_set if kp in sent_lower)
#         keyphrase_score = min(keyphrase_matches / len(keyphrases), 1.0) if keyphrases else 0
        
#         # Position score (earlier sentences get higher scores)
#         position_score = 1.0 - (position_in_section / max(total_sentences, 1))
        
#         # Combined importance score
#         importance_score = base_weight * (0.6 * keyphrase_score + 0.4 * position_score)
        
#         return importance_score

#     def extract_relevant_sentences_1(self, doc_segments: Dict[str, str], 
#                                 keyphrases: List[str]) -> Dict[str, List[Dict]]:
#         """Extract sentences with importance scores for BERT training dataset"""
#         selected_sentences = {}
#         kp_set = set(kp.lower() for kp in keyphrases)

#         for segment, text in doc_segments.items():
#             if segment not in self.section_weights:
#                 continue

#             sentences = self.split_text_to_sentences(text)
#             sentences = [self.clean_text(s) for s in sentences]
#             sentences = self.filter_sentences_by_basic_quality(sentences)

#             # Score all sentences
#             sentence_data = []
#             for i, sent in enumerate(sentences):
#                 sent_lc = sent.lower()
#                 has_keyphrase = any(kp in sent_lc for kp in kp_set)
                
#                 importance_score = self.calculate_sentence_importance_score(
#                     sent, keyphrases, segment, i, len(sentences)
#                 )
                
#                 sentence_data.append({
#                     'text': sent,
#                     'has_keyphrase': has_keyphrase,
#                     'importance_score': importance_score,
#                     'section': segment,
#                     'position': i,
#                     'section_weight': self.section_weights[segment]
#                 })

#             # Sort by importance score and filter
#             sentence_data.sort(key=lambda x: x['importance_score'], reverse=True)
            
#             # Keep top sentences based on section weight
#             max_sentences = max(2, int(len(sentence_data) * self.section_weights[segment]))
#             selected_data = sentence_data[:max_sentences]

#             # Apply additional cleaning
#             filtered_texts = [item['text'] for item in selected_data]
#             combined = " || ".join(filtered_texts) if filtered_texts else None

#             if combined:
#                 s = pd.Series([combined])
#                 s = self.clean_reference_sentences(s)
#                 s = self.drop_number_heavy_sentences(s)
#                 cleaned_sentences_str = s.iloc[0] if not s.empty else None

#                 if cleaned_sentences_str:
#                     cleaned_sentences = [s.strip() for s in cleaned_sentences_str.split("||") if s.strip()]
#                     # Update sentence data with cleaned sentences
#                     final_data = []
#                     for sent in cleaned_sentences:
#                         # Find matching original data
#                         for item in selected_data:
#                             if item['text'] == sent:
#                                 final_data.append(item)
#                                 break
#                     selected_sentences[segment] = final_data
#                 else:
#                     selected_sentences[segment] = []
#             else:
#                 selected_sentences[segment] = []

#         return selected_sentences
######################################################
# ## section_weighted_extractor.py (ENHANCED FOR MORE NEGATIVES)
# import re
# from typing import List, Dict, Tuple
# import pandas as pd

# SECTION_WEIGHTS = {
#     'title': 1.0,
#     'abstract': 1.0,
#     'conclusion': 0.8,
#     'results': 0.6,
#     "experimental_procedures": 0.6,
#     "materials_methods": 0.6
# }

# # Separate weights for negative sentences (higher to get more negatives)
# NEGATIVE_WEIGHTS = {
#     'title': 0.0,        # Get more negatives from title
#     'abstract': 0.0,     # Get more negatives from abstract  
#     'conclusion': 0.1,   # 4x more negatives from conclusion
#     'results': 0.1,      # 4x more negatives from results
#     "experimental_procedures": 0.1,   # 4x more negatives
#     "materials_methods":0.1,          # 4x more negatives
# }

# class BasicKeyphraseExtractor:
#     def __init__(self, section_weights: Dict[str, float] = None, negative_weights: Dict[str, float] = None):
#         self.section_weights = section_weights or SECTION_WEIGHTS
#         self.negative_weights = negative_weights or NEGATIVE_WEIGHTS

#     def split_text_to_sentences(self, text: str) -> List[str]:
#         sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', str(text))
#         return [s.strip() for s in sentences if s.strip()]

#     def clean_text(self, text: str) -> str:
#         return re.sub(r"\s+", " ", str(text)).strip()

#     def assess_sentence_quality(self, sentence: str) -> bool:
#         """STRICTER quality criteria to generate more negatives"""
#         words = sentence.split()
        
#         # More strict word count threshold (was 4, now 6)
#         if len(words) < 6:
#             return False
        
#         # More strict word count upper limit
#         if len(words) > 50:  # Very long sentences are poor quality
#             return False
        
#         # More strict alphanumeric ratio check (was 0.3, now 0.4)
#         alnum_words = [w for w in words if any(c.isalnum() for c in w)]
#         if len(alnum_words) / len(words) < 0.4:
#             return False
            
#         # NEW: Check for too many numbers/special characters
#         digit_count = sum(1 for w in words if any(c.isdigit() for c in w))
#         if digit_count / len(words) > 0.4:  # More than 40% words contain numbers
#             return False
            
#         # NEW: Check for repeated patterns (poor quality indicator)
#         if len(set(words)) < len(words) * 0.7:  # Less than 70% unique words
#             return False
            
#         return True

#     def assess_borderline_quality(self, sentence: str) -> bool:
#         """NEW: Identify borderline sentences (not good, not terrible)"""
#         words = sentence.split()
        
#         # Borderline criteria
#         if 3 <= len(words) < 8:  # Short but not too short
#             return True
#         if len(words) > 100:  # Long sentences
#             return True
            
#         alnum_words = [w for w in words if any(c.isalnum() for c in w)]
#         alnum_ratio = len(alnum_words) / len(words) if words else 0
#         # if 0.3 <= alnum_ratio: # < 0.6:  # Moderate alphanumeric ratio
#         if alnum_ratio >= 0.3:
#             return True
            
#         return False

#     def clean_reference_sentences(self, series: pd.Series) -> pd.Series:
#         ref_only_pattern = re.compile(r'^\s*\[\s*\d+(?:[\s,-]*\d+)*\s*\]\s*\.?\s*$')
#         ends_with_ref_pattern = re.compile(r'.*\[\s*\d+(?:[\s,-]*\d+)*\s*\]\s*\.?\s*$')

#         def filter_func(text):
#             if pd.isna(text):
#                 return text
#             sentences = [s.strip() for s in str(text).split("||")]
#             sentences = [
#                 s for s in sentences
#                 if s and not ref_only_pattern.match(s) and not ends_with_ref_pattern.match(s)
#             ]
#             return " || ".join(sentences) if sentences else None

#         return series.apply(filter_func)

#     def drop_number_heavy_sentences(self, series: pd.Series, length_threshold=200, num_ratio_threshold=0.3) -> pd.Series:
#         def tokenize(sentence):
#             return re.findall(r'\w+|\S', str(sentence))

#         def is_number_heavy(sentence):
#             if len(sentence) < 40:
#                 return False
#             tokens = tokenize(sentence)
#             if not tokens:
#                 return False
#             digit_count = sum(1 for t in tokens if t.isdigit())
#             ratio = digit_count / len(tokens)
#             return len(sentence) > length_threshold and ratio > num_ratio_threshold

#         def filter_func(text):
#             if pd.isna(text):
#                 return text
#             sentences = [s.strip() for s in str(text).split("||")]
#             filtered = [s for s in sentences if not is_number_heavy(s)]
#             return " || ".join(filtered) if filtered else None

#         return series.apply(filter_func)

#     def extract_relevant_sentences_with_quality_separation(self, doc_segments: Dict[str, str], keyphrases: List[str]) -> Dict[str, Dict[str, List[str]]]:
#         """
#         ENHANCED METHOD: Generate more negative sentences using multiple strategies
#         """
#         results = {}
#         kp_set = set(kp.lower().replace('-', '').replace(' ', '') for kp in keyphrases)  # Normalized for matching

#         for segment, text in doc_segments.items():
#             if segment not in self.section_weights:
#                 continue

#             # Step 1: Split and clean sentences
#             if ""
#             # raw_sentences = self.split_text_to_sentences(text)
#             raw_sentences = text.split(" | ")
#             sentences = [self.clean_text(s) for s in raw_sentences]
            
#             # Step 2: Categorize ALL sentences
#             good_quality_with_keyphrases = []
#             poor_quality_with_keyphrases = []
#             borderline_quality_with_keyphrases = []  # NEW category
#             no_keyphrases_but_relevant = []  # NEW category
            
#             for sent in sentences:
#                 # Normalize sentence for keyphrase matching (ignore case, hyphens, spaces)
#                 sent_norm = sent.lower().replace('-', '').replace(' ', '')
#                 has_keyphrases = any(kp in sent_norm for kp in kp_set)
                
#                 if has_keyphrases:
#                     is_good_quality = self.assess_sentence_quality(sent)
#                     is_borderline = self.assess_borderline_quality(sent)
                    
#                     if is_good_quality:
#                         good_quality_with_keyphrases.append(sent)
#                     elif is_borderline:
#                         borderline_quality_with_keyphrases.append(sent)  # NEW: Borderline → Negative
#                     else:
#                         poor_quality_with_keyphrases.append(sent)
#                 else:
#                     # NEW: Add some non-keyphrase sentences as negatives (domain-relevant)
#                     if self._is_domain_relevant(sent):
#                         no_keyphrases_but_relevant.append(sent)

#             # Step 3: Combine different types of negative sentences
#             all_negative_sentences = (
#                 poor_quality_with_keyphrases + 
#                 borderline_quality_with_keyphrases +
#                 no_keyphrases_but_relevant[:5]  # Limit non-keyphrase negatives
#             )

#             print(f"\nDETAILED STATISTICS for '{segment}':")
#             print(f"   Total sentences:                    {len(sentences)}")
#             print(f"   Good quality + keyphrases:          {len(good_quality_with_keyphrases)}")
#             print(f"   Poor quality + keyphrases:          {len(poor_quality_with_keyphrases)}")
#             print(f"   Borderline quality + keyphrases:    {len(borderline_quality_with_keyphrases)}")
#             print(f"   Domain relevant (no keyphrases):    {len(no_keyphrases_but_relevant)}")
#             print(f"   TOTAL NEGATIVE CANDIDATES:          {len(all_negative_sentences)}")

#             # Step 4: Apply section weight limiting with HIGHER limits for negatives
#             max_positive = max(1, int(len(good_quality_with_keyphrases) * self.section_weights[segment]))
#             max_negative = max(2, int(len(all_negative_sentences) * self.negative_weights[segment]))  # Use higher negative weights
            
#             limited_positive = good_quality_with_keyphrases[:max_positive]
#             limited_negative = all_negative_sentences[:max_negative]

#             print(f"\nSECTION WEIGHT LIMITING:")
#             print(f"   Positive weight: {self.section_weights[segment]}, Negative weight: {self.negative_weights[segment]}")
#             print(f"   Positive after limiting:            {len(limited_positive)}")
#             print(f"   Negative after limiting:            {len(limited_negative)}")

#             # Step 5: Apply cleaning with RELAXED criteria for negatives
#             def clean_sentence_list(sent_list, list_type, is_negative=False):
#                 if sent_list:
#                     combined = " || ".join(sent_list)
#                     s = pd.Series([combined])
                    
#                     # Apply reference cleaning
#                     s = self.clean_reference_sentences(s)
                    
#                     # For negatives, be more lenient with number-heavy sentence removal
#                     if not is_negative:
#                         s = self.drop_number_heavy_sentences(s)
#                     else:
#                         s = self.drop_number_heavy_sentences(s, length_threshold=300, num_ratio_threshold=0.5)  # More lenient
                    
#                     cleaned_str = s.iloc[0] if not s.empty else None
#                     if cleaned_str:
#                         final_list = [s.strip() for s in cleaned_str.split("||") if s.strip()]
#                         print(f"   {list_type} after final cleaning:      {len(final_list)}")
#                         return final_list
#                     else:
#                         print(f"   {list_type} after final cleaning:      0 (all removed)")
#                         return []
#                 return []

#             print(f"\nFINAL CLEANING:")
#             final_positive = clean_sentence_list(limited_positive, "Positive", is_negative=False)
#             final_negative = clean_sentence_list(limited_negative, "Negative", is_negative=True)

#             print(f"\nFINAL RESULTS for '{segment}':")
#             print(f"   Final positive sentences:           {len(final_positive)}")
#             print(f"   Final negative sentences:           {len(final_negative)}")
#             print(f"   Negative/Positive ratio:            {len(final_negative)/max(len(final_positive),1):.2f}")

#             results[segment] = {
#                 'positive': final_positive,
#                 'negative': final_negative
#             }

#         # Overall summary
#         total_positive = sum(len(data['positive']) for data in results.values())
#         total_negative = sum(len(data['negative']) for data in results.values())
        
#         print(f"\nOVERALL SUMMARY:")
#         print(f"{'='*50}")
#         print(f"Total positive sentences: {total_positive}")
#         print(f"Total negative sentences: {total_negative}")
#         print(f"Overall Negative/Positive ratio: {total_negative/max(total_positive,1):.2f}")
#         print(f"{'='*50}")

#         return results

#     def _is_domain_relevant(self, sentence: str) -> bool:
#         """NEW: Check if sentence is domain-relevant (for non-keyphrase negatives)"""
#         domain_indicators = [
#             'synthesis', 'reaction', 'catalyst', 'temperature', 'pressure', 'analysis',
#             'experiment', 'method', 'result', 'conclusion', 'performance', 'efficiency',
#             'characterization', 'spectroscopy', 'microscopy', 'measurement', 'evaluation'
#         ]
        
#         sentence_lower = sentence.lower()
#         return any(indicator in sentence_lower for indicator in domain_indicators)

#     # Backward compatibility
#     def extract_relevant_sentences(self, doc_segments: Dict[str, str], keyphrases: List[str]) -> Dict[str, Dict[str, List[str]]]:
#         """Updated method - returns both positive and negative sentences"""
#         quality_results = self.extract_relevant_sentences_with_quality_separation(doc_segments, keyphrases)
        
#         legacy_results = {}
#         for segment, data in quality_results.items():
#             legacy_results[segment] = {
#                 'positive': data.get('positive', []),
#                 'negative': data.get('negative', [])
#             }
        
#         print(f"{legacy_results =}")
#         return legacy_results
#############################################################
## section_weighted_extractor.py 
import re
from typing import List, Dict, Tuple
import pandas as pd

SECTION_WEIGHTS = {
    'title': 1.0,
    'abstract': 1.0,
    'conclusion': 0.2,
    'results': 0.2,
    "experimental_procedures": 0.2,
    "materials_methods": 0.2
}

class BasicKeyphraseExtractor:
    def __init__(self, section_weights: Dict[str, float] = None):
        self.section_weights = section_weights or SECTION_WEIGHTS

    def split_text_to_sentences(self, text: str) -> List[str]:
        sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', str(text))
        return [s.strip() for s in sentences if s.strip()]

    def clean_text(self, text: str) -> str:
        return re.sub(r"\s+", " ", str(text)).strip()

    def assess_sentence_quality(self, sentence: str) -> bool:
        """
        FIXED: More reasonable quality criteria
        """
        words = sentence.split()
        
        # Relaxed word count threshold (was 8, now 4)
        if len(words) < 4:
            return False
        
        # Keep alphanumeric ratio check
        alnum_words = [w for w in words if any(c.isalnum() for c in w)]
        if len(alnum_words) / len(words) < 0.3:
            return False
            
        return True

    def clean_reference_sentences(self, series: pd.Series) -> pd.Series:
        ref_only_pattern = re.compile(r'^\s*\[\s*\d+(?:[\s,-]*\d+)*\s*\]\s*\.?\s*$')
        ends_with_ref_pattern = re.compile(r'.*\[\s*\d+(?:[\s,-]*\d+)*\s*\]\s*\.?\s*$')

        def filter_func(text):
            if pd.isna(text):
                return text
            sentences = [s.strip() for s in str(text).split("||")]
            sentences = [
                s for s in sentences
                if s and not ref_only_pattern.match(s) and not ends_with_ref_pattern.match(s)
            ]
            return " || ".join(sentences) if sentences else None

        return series.apply(filter_func)

    def drop_number_heavy_sentences(self, series: pd.Series, length_threshold=200, num_ratio_threshold=0.3) -> pd.Series:
        def tokenize(sentence):
            return re.findall(r'\w+|\S', str(sentence))

        def is_number_heavy(sentence):
            if len(sentence) < 40:
                return False
            tokens = tokenize(sentence)
            if not tokens:
                return False
            digit_count = sum(1 for t in tokens if t.isdigit())
            ratio = digit_count / len(tokens)
            return len(sentence) > length_threshold and ratio > num_ratio_threshold

        def filter_func(text):
            if pd.isna(text):
                return text
            sentences = [s.strip() for s in str(text).split("||")]
            filtered = [s for s in sentences if not is_number_heavy(s)]
            return " || ".join(filtered) if filtered else None

        return series.apply(filter_func)

    def extract_relevant_sentences_with_quality_separation(self, doc_segments: Dict[str, str], keyphrases: List[str]) -> Dict[str, Dict[str, List[str]]]:
        """
        ENHANCED METHOD with detailed debugging: Check keyphrases FIRST, then assess quality
        Returns positive (good quality + keyphrases) and negative (poor quality + keyphrases)
        """
        results = {}
        kp_set = set(kp.lower() for kp in keyphrases)

        # print(f"\n{'='*80}")
        # print(f"PROCESSING SUMMARY - Total keyphrases: {len(keyphrases)}")
        # print(f"Keyphrases: {keyphrases}")
        # print(f"{'='*80}")

        for segment, text in doc_segments.items():
            if segment not in self.section_weights:
                continue

            # print(f"\n PROCESSING SEGMENT: '{segment.upper()}'")
            # print(f"{'='*50}")
            
            # Step 1: Split and clean sentences
            raw_sentences = self.split_text_to_sentences(text)
            sentences = [self.clean_text(s) for s in raw_sentences]
            
            # print(f" STAGE 1 - Raw sentences: {len(raw_sentences)}")
            # print(f" STAGE 2 - After cleaning: {len(sentences)}")

            # Step 2: Find sentences with keyphrases AND assess quality for ALL sentences
            keyphrase_sentences = []
            good_quality_sentences = []
            poor_quality_sentences = []
            good_quality_with_keyphrases = []
            poor_quality_with_keyphrases = []
            
            for sent in sentences:
                sent_lc = sent.lower()
                has_keyphrases = any(kp in sent_lc for kp in kp_set)
                is_good_quality = self.assess_sentence_quality(sent)
                
                # Track quality regardless of keyphrases
                if is_good_quality:
                    good_quality_sentences.append(sent)
                else:
                    poor_quality_sentences.append(sent)
                
                # Track keyphrase sentences
                if has_keyphrases:
                    keyphrase_sentences.append(sent)
                    if is_good_quality:
                        good_quality_with_keyphrases.append(sent)
                        # print(f" POSITIVE: '{sent[:60]}...'")
                    else:
                        poor_quality_with_keyphrases.append(sent)
                        # print(f" NEGATIVE: '{sent[:60]}...'")

            # # Print comprehensive statistics
            # print(f"\n DETAILED STATISTICS for '{segment}':")
            # print(f"   Total sentences:                    {len(sentences)}")
            # print(f"   Good quality sentences:             {len(good_quality_sentences)}")
            # print(f"   Poor quality sentences:             {len(poor_quality_sentences)}")
            # print(f"   Sentences with keyphrases:          {len(keyphrase_sentences)}")
            # print(f"   Good quality + keyphrases:          {len(good_quality_with_keyphrases)}")
            # print(f"   Poor quality + keyphrases:          {len(poor_quality_with_keyphrases)}")
            
            # # Calculate percentages
            # if len(sentences) > 0:
            #     quality_pct = (len(good_quality_sentences) / len(sentences)) * 100
            #     keyphrase_pct = (len(keyphrase_sentences) / len(sentences)) * 100
            #     pos_pct = (len(good_quality_with_keyphrases) / len(sentences)) * 100
            #     neg_pct = (len(poor_quality_with_keyphrases) / len(sentences)) * 100
                
                # print(f"\n PERCENTAGES for '{segment}':")
                # print(f"   Quality sentences:                  {quality_pct:.1f}%")
                # print(f"   Sentences with keyphrases:          {keyphrase_pct:.1f}%")
                # print(f"   Positive (quality + keyphrases):    {pos_pct:.1f}%")
                # print(f"   Negative (poor + keyphrases):       {neg_pct:.1f}%")

            # Step 3: Apply section weight limiting
            max_positive = max(1, int(len(good_quality_with_keyphrases) * self.section_weights[segment]))
            max_negative = max(1, int(len(poor_quality_with_keyphrases) * self.section_weights[segment]))
            
            limited_positive = good_quality_with_keyphrases[:max_positive]
            limited_negative = poor_quality_with_keyphrases[:max_negative]

            # print(f"\n  SECTION WEIGHT LIMITING (weight: {self.section_weights[segment]}):")
            # print(f"   Positive before limiting:           {len(good_quality_with_keyphrases)}")
            # print(f"   Positive after limiting:            {len(limited_positive)}")
            # print(f"   Negative before limiting:           {len(poor_quality_with_keyphrases)}")
            # print(f"   Negative after limiting:            {len(limited_negative)}")

            # Step 4: Apply additional cleaning (references and number-heavy)
            def clean_sentence_list(sent_list, list_type):
                if sent_list:
                    combined = " || ".join(sent_list)
                    s = pd.Series([combined])
                    
                    # Track reference cleaning
                    s_before_ref = s.copy()
                    s = self.clean_reference_sentences(s)
                    
                    # Track number cleaning
                    s = self.drop_number_heavy_sentences(s)
                    
                    cleaned_str = s.iloc[0] if not s.empty else None
                    if cleaned_str:
                        final_list = [s.strip() for s in cleaned_str.split("||") if s.strip()]
                        # print(f"   {list_type} after final cleaning:      {len(final_list)}")
                        return final_list
                    else:
                        # print(f"   {list_type} after final cleaning:      0 (all removed)")
                        return []
                return []

            # print(f"\n FINAL CLEANING:")
            final_positive = clean_sentence_list(limited_positive, "Positive")
            final_negative = clean_sentence_list(limited_negative, "Negative")

            # # Final summary for this segment
            # print(f"\n FINAL RESULTS for '{segment}':")
            # print(f"   Final positive sentences:           {len(final_positive)}")
            # print(f"   Final negative sentences:           {len(final_negative)}")
            # print(f"   Total output sentences:             {len(final_positive) + len(final_negative)}")

            results[segment] = {
                'positive': final_positive,
                'negative': final_negative
            }

        # Overall summary
        total_positive = sum(len(data['positive']) for data in results.values())
        total_negative = sum(len(data['negative']) for data in results.values())
        
        # print(f"\n OVERALL SUMMARY:")
        # print(f"{'='*50}")
        # print(f"Total positive sentences across all segments: {total_positive}")
        # print(f"Total negative sentences across all segments: {total_negative}")
        # print(f"Total output sentences: {total_positive + total_negative}")
        # print(f"{'='*50}")

        return results

    # backward compatibility
    def extract_relevant_sentences(self, doc_segments: Dict[str, str], keyphrases: List[str]) -> Dict[str, List[str]]:
        """Legacy method - returns only positive sentences"""
        quality_results = self.extract_relevant_sentences_with_quality_separation(doc_segments, keyphrases)
        
        legacy_results = {}
        for segment, data in quality_results.items():
            # legacy_results[segment] = data.get('positive', [])
            legacy_results[segment] = {
                'positive': data.get('positive', []),
                'negative': data.get('negative', [])
            }
        # print(f"legacy_results: {legacy_results}")
        return legacy_results

############################################################



    

if __name__ == "__main__":
    doc_segments = {
        'title': "Oxygen-Bridged Ga-O-PtPd Triple Sites Boost Methanol-Assisted Rechargeable Zn-Air Batteries Through Suppressing COads Generation.",

        'abstract': "Constructing high-efficiency platinum (Pt)-based catalysts for methanol oxidation reaction (MOR) by suppressing the intermediate COads generation is strongly desired and remains a grand challenge. Herein, the concept of holding O-bridged triple sites is documented to strengthen 'non-CO' pathway selectivity by forming HCOO- species during MOR. The obtained Ga-O-PtPd triple sites via grafting the single-atomic Ga sites on PtPd nanosheets achieves a high c.Density of 3.05 mAcm-2 of MOR, which is 5.65 times higher than commercial Pt/C (0.54 mAcm-2), as well as remarkably stability and COads poison resistance. The CO diffuse reflectance IR Fourier transform spectroscopy (CO-DRIFTS) results reveal that Ga-O-PtPd triple sites present a weak CO binding ability, reducing the generation of COads intermediate. In addition, the Ga-O-PtPd-based Zn-methanol-air batteries present an excellent activity and stability compared with commercial catalysts.",

        'results': """2. Results and Discussion
        In this work, a general synthesis method was developed to con-
        struct the desired oxygen-bridged Ga-O-PtPd triple-site catalysts.
        Platinum (Pt(acac) 2 ), palladium (Pd(acac) 2 ), Gallium (Ga(acac) 3 ),
        and hexadecarbonyl tungsten (W(CO) 6 ) are used as metal pre-
        cursors are dissolved in olylamine (OAm), and then the mix-
        ture was treated at high temperature to obtain black reactant.
        Figure 1 a shows the representative scanning transmission elec-
        tron microscopy (STEM) image of Ga-PtPd catalysts. It is clearly
        seen that the obtained catalysts are typical 2D nanosheet struc-
        tures with high flexibility with a diameter of 300 nm, which can
        be further demonstrated by a transmission electron microscope
        (TEM) image (Figure 1b ; Figure S1 , Supporting Information).
        Figure 1c shows the morphology of individual nanosheets with
        clear lattice spacing, indicating a perfect crystal structure. The
        atomic force microscope image (Figure 1d ) shows the Ga-PtPd
        catalysts own an average thickness of ≈ 2 nm. The EDS-mapping
        results (Figure 1e ) show that Pt, Pd, and Ga are uniformly dis-
        tributed along the Ga-PtPd catalysts. The composition ratio of Pt:
        Pd: Ga is ≈ 45.03: 49.24: 5.73, determined by inductively coupled
        plasma optical emission spectrometry (ICP-OES). Owing to the
        disparity in atomic radii between Pt, Pd, and Ga, distinguishing
        the prevalence of all elements in the Ga-PtPd based on luminance
        distribution was easily achievable. The presence of strain is usu-
        ally detfected when mixing two various atoms with diﬀerent radii.
        Figure 1f quantitatively presents the distribution of strains along
        the Ga-PtPd by geometrical phase analyses (GPA) techniques,
        which showed large fluctuations in the strain distribution in the
        Ga-PtPd due to lattice mismatches.
        An in-depth study on the structural feature of Ga-PtPd cata-
        lysts is revealed by scanning transmission electron microscopy
        (STEM) As shown in Figures 1g,h , the AC-HAADF-STEM im-
        age exhibits a clear intermetallic alloy structure when viewed
        along the zone axis of [ − 1, − 1, − 1]. The HCP structure of Pt NSs
        can be directly determined by characterizing the nanostructure
        along zone zxes, where the Z-contrast images closely resemble
        the structural projections of an HCP Pt-based nanosheets and its
        FFT patterns can be well indexed bt the hexagonal close pack-
        ing (HCP) cell along its [ − 1, − 1, − 1] directions. It is worth not-
        ing that the synthesized Pt-based nanosheet have unusual HCP
        domains, rather than the conventional FCC structure, which
        can improve the catalytic performance of MOR to some extent.
        Figure 1i shows an obvious atom luminance diﬀerence for Pt, Pd,
        and Ga elements. Meanwhile, the Ga-PtPd catalysts display lattice
        distances of ≈ 0.51 nm, which was ascribed to the super-lattice
        of the Ga-Pd-Pt arrangement. When viewed along the zone axis
        of [0,-1, 1], the Ga-PtPd catalysts present a typical face-centered
        cubic phase structure of Pt, as illustrated in Figures 1j and lk.
        The lattice spacing in Figure 1l is ≈ 0.23 nm, which is ascribed to
        the (111) crystal plane of Pt. As a comparison, the same method
        was adopted to synthesize the Pt-Pd, Pt-Ga, and Pd-Ga dual sites.
        The morphology of these catalysts is exhibited in Figure S2 (Sup-
        porting Information). It can be seen that Pt-Pd duals sites can
        keep 2D nanosheet structure well. Control experiments with-
        out W(CO) 6 resulted in irregular PtPd-Ga aggregates lacking the
        nanosheet morphology (Figure S3 , Supporting Information), un-
        derscoring CO’s indispensability. The Pt-Pd, Pt-Ga, and Pd-Ga
        dual sites are presented as nanoparticles and irregular nanorib-
        bons. In addition, the element content of metals in Pt-Pd, Pt-Ga,
        and Pd-Ga are 42.11/57.89, 97.24/2.76, and 96.05/3.95, respec-
        tively, indicating the successful synthesis of dual sites.
        To reveal the metallic dispersion, X-ray diﬀraction (XRD) was
        carried out to investigate the structure and phase of the Ga-PtPd
        catalysts (Figure S4 , Supporting Information). It is clearly seen
        that the Ga-PtPd and Pt-Pd catalysts all show a similar single fcc
        structure of Pt, of which the typical diﬀraction signals are located
        at the 40.5 ° , 46.6 ° , 68.3 ° , and 82.1 ° , attributing to {200}, {220}
        and {311}, respectively. [ 7 ]  There are no obvious diﬀraction peaks
        of Ga and its oxides over Ga-PtPd catalysts, indicating Ga species
        is in a monodispersed state (marked as Ga-O-PtPd trible sites).
        The X-ray photoelectron spectroscopy (XPS) was used to
        uncover the metallic state of Pt, Pd, and Ga in Ga-O-PtPd.
        Figure 2 a discloses that Ga-O-PtPd and PtPd all exhibit the dom-
        inant metallic state (Pt 0 ) of Pt. Compared to the Pt 4f 7/2 peak
        of PtPd (71.20 eV), the binding energy of Ga-O-PtPd (70.92 eV)
        Adv. Energy Mater. 2025 , 15 , 2500421
        © 2025 Wiley-VCH GmbH
        2500421 (2 of 8)
        www.advancedsciencenews.com
        www.advenergymat.de
        Figure 1. a) STEM image, b) TEM image, c) HRTEM image, d) AFM image and corresponding height profile, e) EDS elemental mappings, f) the
        corresponding GPA images for axial strain ( 𝜖 xx ). g) AC-HAADF-STEM image, h) corresponding FFT patterns, and i) the equalized rainbow color mode
        of Ga-PtPd catalysts. j) AC-HAADF-STEM image, k) corresponding FFT patterns, and l) the equalized rainbow color mode of Ga-PtPd catalysts.
        shifts negatively, demonstrating that more electrons might trans-
        fer from GaO x to Pt in Ga-O-PtPd. The state of Pd sites in Ga-O-
        PtPd presents a similar trend with Pt (Figure 2b ), in which the
        dominant Pd state is Pd 0  in Ga-O-PtPd (335.53 eV) and PtPd
        (335.60 eV). Figure 3 c exhibits that the Ga 2p signal of the Ga-
        O-PtPd is centered at 1117.90 eV, ascribing to the response sig-
        nals of Ga 3 + . [ 12 ]  The above results demonstrate that the O-bridged
        triple site can promote the electron optimization of Pt-Pd. Fur-
        thermore, surface valence band photoemission spectra (Figure
        S5 , Supporting Information) show that an obvious downshift of
        the d-band center of Pt for Ga-O-PtPd is ≈ 2.787 eV, lower than
        these of PtPd ( − 2.715 eV), indicating the weaker adsorption for
        intermediates such as CO ads in MOR on Ga-O-PtPd. The plot
        of the projected d-density of states (PDOS) reveals that the elec-
        tronic redistribution of Pt in the Ga-O-PtPd interface induces a di-
        rected electron movement (Figure S6 , Supporting Information).
        Additionally, the d-band center of Pt in Ga-O-PtPd upshifted from
        1.89 eV (PtPd) to 1.95 eV, keeping trace with surface valence band
        photoemission spectra results.
        Furthermore, X-ray absorption spectroscopy (XAS) was per-
        formed to reveal the metallic state of Pt in Ga-O-PtPd. Figure 2d
        exhibits the X-ray absorption near-edge structure (XANES) spec-
        tra of Pt 3-edge of Ga-O-PtPd, PtO 2, and Pt foil. The curve of
        Ga-O-PtPd brings into correspondence with Pt foil, revealing
        that Pt exists mainly in a metallic state for Ga-O-PtPd. Mean-
        while, the white line intensity of Pt L 3 edge spectra for Ga-O-
        PtPd is closer to that for Pt foil, which indicates the electron
        deficiency of Pt sites. The Fourier transform extended X-ray ab-
        sorption fine structure (FT EXAFS, Figure 2e ) of Ga-O-PtPd at
        Pt L 3-edge shows a main peak at 2.2 and 2.8 Å, which can be
        assigned to Pt-Rh and Pt-Pt path. The coordination number of
        Pt in Ga-O-PtPd is ≈ 8 (Figure 2f ; Table S1 , Supporting Informa-
        tion), with the corresponding bond lengths of 1.92, 2.74, and 2.73
        Å, which stand for Pt-O, Pt-Pt and Pt-Pd scattering respectively.
        Wavelet-transform (WT) plots were further performed to reveal
        the coordination configurations of Pt in the Ga-O-PtPd. As shown
        in Figure 2g–i , it is clearly seen that the intensity maximum
        can be observed at ≈ 2.4 Å for Ga-O-PtPd, in consistency with
        Pt foil, indicating a metallic Pt species in the Ga-O-PtPd triple
        sites.
        The catalytic performances of Ga-O-PtPd, PtPd, and commer-
        cial Pt/C for MOR were tested at the same conditions. The
        Adv. Energy Mater. 2025 , 15 , 2500421
        © 2025 Wiley-VCH GmbH
        2500421 (3 of 8)
        www.advancedsciencenews.com
        www.advenergymat.de
        Figure 2. a) Pt 4f XPS spectra, b) Pd 3d XPS spectra, and c) Ga 2p XPS spectra of Ga-O-PtPd triple sites. d) Experimental XANES spectra at the Pt L 3
        edge. e) FT k 3-weighted (k) function of the EXAFS spectra. f) FT of the k 3-weighted EXAFS spectrum and fitting in R space of Ga-O-PtPd triple sites.
        g) WT for the FT k 3-weighted (k) function of Pt in Ga-O-PtPd triple sites, PtO 2 and Pt foil.
        electrochemical surface areas (ECSAs) are evaluated by cyclic
        voltammetry (CV) curves in an acid solution (Figure 3a ). In com-
        parison with commercial Pt/C, the Ga-O-PtPd, PtPd shows a
        lower ECSAs values due to the larger particle sizes. Figure 3b
        shows the oxidation curves of ethanol in the 0.5 m H 2 SO 4 +
        2 m methanol solution. It is seen that the Ga-O-PtPd exhibits the
        highest activity than that of PtPd and commercial Pt/C. Figure 3c
        shows the specific activity and mass activity values, which are cal-
        culated by the ECSA values and Pt loading weight. In general,
        the specific activity of Ga-O-PtPd is 3.05 mAcm − 2 , which is 1.76
        and 5.65 times higher than PtPd (1.73 mAcm − 2 ) and commercial
        Pt/C (0.54 mAcm − 2 ), respectively. The mass activity of Ga-O-PtPd
        is 1.16 A mg − 1 , which is 1.37 times higher than that of commer-
        cial Pt/C. The above results infer that Ga-O-PtPd triple sites is
        beneficial for excellent MOR performance. In addition, the Ga-
        O-PtPd triple sites also possess the highest catalytic performance
        toward MOR in an alkaline solution as presented in Figure S7
        (Supporting Information).
        The methanol oxidation kinetics of Ga-O-PtPd and PtPd were
        investigated by adjusting scanning rates. As shown in Figure 3d ,
        the CVs were measured at diﬀerent scan rates from 10 to
        100 mV s − 1 , in which the square root of the scan rate (v 1/2 )
        is a linear relationship with the current density (J). The Ga-O-
        PtPd shows the largest slope value (0.0361) than PtPd (0.0281)
        and commercial Pt/C (0.025), demonstrating the great enhance-
        ment of MOR kinetics on Ga-O-PtPd. In addition, stability is
        another performance index for accessing MOR. Chronoamper-
        ometric measurements exhibit that Ga-O-PtPd exhibits excel-
        lent current density all the time (Figure 3e ) compared with
        PtPd and commercial Pt/C. Figure 3f exhibits the MOR perfor-
        mance retention after diﬀerent CV tests. The activity of Ga-O-
        PtPd can keep its initial performance of 72.06%, higher than
        Adv. Energy Mater. 2025 , 15 , 2500421
        © 2025 Wiley-VCH GmbH
        2500421 (4 of 8)
        www.advancedsciencenews.com
        www.advenergymat.de
        Figure 3. Cyclic–voltammogram curves of Ga-O-PtPd, PtPd, Pt/C a) 0.5 m H 2 SO 4 , b) 0.5 m H 2 SO 4 + 2 m CH 3 OH. c) The comparison of catalytic
        performances. d) The plots of forward peak current J (mA μ g − 1 Pt ) versus the square root of the scan rate (v 1/2 ) for MOR. e) I-T curves (0.4 V vs SCE),
        f) the normalized current after various CV cycles, g) Cyclic-voltammogram curves, and h) the comparison of catalytic performances of Ga-O-PtPd with
        diﬀerent Ga content. i) CO stripping curves for Ga-O-PtPd, PtPd, Pt/C.
        PtPd and commercial Pt/C after 1000 CV cycles. The above re-
        sults demonstrate that the O-bridged triple sites can boost MOR
        stability. In this work, the Ga-O-PtPd triple sites with diﬀerent
        Ga content are tested at the same conditions. As illustrated in
        Figure 3g,h and Figure S8 (Supporting Information), the Ga-
        modified PtPd all shows enhanced MOR performance than pure
        PtPd, and the Ga content is 0.05 wt % is the optimum dosage for
        MOR both in acid and alkaline solution. The above results in-
        dicate that Ga-O-PtPd triple sites are in favor of CO oxidation/
        removal.
        It is accepted that the CO poisoning for Pt active sites is in-
        evitable during the alcohol electro-oxidation reactions. Thus, the
        ability to resist CO poisoning is the key index to measure the
        MOR catalytic performance for catalysts. The CO stripping test
        results (Figure 3i ) show that adsorbed CO species on catalyst sur-
        faces are completely removed during the first CV test. [ 13 ]  Com-
        pared with commercial Pt/C (0.596 V) and PtPd (0.698 V), Ga-
        O-PtPd can oxidize the adsorbed CO intermediates at a lower
        potential (0.572 V). The reason is that the oxyphilic Ga element
        can promote the OH adsorption, and accelerate the CO ads oxi-
        dation/removal through Langmuir–Hinshelwood (L–H) mech-
        anism. And the same promoting eﬀect of oxyphilic metals can
        also be concluded in the previous reports (Table S2 , Supporting
        Information). To explore CO adsorption behavior, CO diﬀuse re-
        flectance infrared Fourier transform spectroscopy (CO-DRIFTS)
        was performed. [ 2d ]  Figure 4 a,c,e show that only CO gas peaks at
        ≈ 2175 and 2120 cm − 1  were detected on Ga-O-PtPd and PtPd cata-
        lysts. While, the linear CO adsorption peak (CO L ) of 2075cm − 1  ex-
        ists on Pt/C, which cannot be completely removed after N 2 purg-
        ing (Figure 4b,d,f ), suggesting the Ga-O-PtPd and PtPd have a
        strong anti-ability of poisonous CO intermediate than Pt/C. In
        addition, during the CO ads adsorption process (Figure S9 , Sup-
        porting Information), the PtPd dual site can approach its satura-
        tion point of CO within 4 min, while Ga-O-PtPd triple sites need
        6 min. On the contrary, the most of CO molecules can desorb
        from the Ga-O-PtPd triple sites within 2 min. While the PtPd
        Adv. Energy Mater. 2025 , 15 , 2500421
        © 2025 Wiley-VCH GmbH
        2500421 (5 of 8)
        www.advancedsciencenews.com
        www.advenergymat.de
        Figure 4. CO–DRIFTS spectra of a,b) Ga-O-PtPd, c,d) PtPd, and e,f) Pt/C.
        dual site takes 4 min to remove most of the CO molecules. Thus,
        it is concluded that compared with PtPd dual sites, the Ga-O-
        PtPd triple sites presents a weaker CO adsorption rate and a rapid
        desorption rate, suggesting a weak combining capacity with CO
        molecules.
        In traditional ZABs, the discharge process includes negative
        Zn plate oxidation and positive O 2 reduction reaction, and the
        opposite charging process includes negative Zn 2 +  reduction and
        positive OER reduction. A new methanol-assisted Zn-air batter-
        ies (ZMABs) system was proposed by replacing OER with MOR
        catalysized by Ga-O-PtPd, as displayed in Figure 5 a . [ 14 ]  In this sys-
        tem, the discharging process remains unchanged, and O 2 is still
        reduced to produce OH − at the air electrode. Replacing OER by
        MOR during the charging process, the oxidation reaction occurs
        from CH 3 OH to HCOOH. [ 2e ]  Besides, ZMABs were also assem-
        bled using the commercial Pt/C + IrO 2 (with a mass ratio of 1:1) as
        a reference catalyst. Figures 5b–d show that the Ga-O-PtPd-based
        ZMABs delivered a high open circuit voltage (OCV) of 1.34 V,
        a large specific capacity of 898.69 mAh g Zn
        − 1 , and a high peak
        power density of 172.16 mW cm − 2 , all of which far surpassed
        the index of PtPd-based ZMABs (1.31 V, 772.06 mAh g Zn
        − 1
        and 144.65 mW cm − 2 ) and Pt/C + IrO 2-based ZMABs (1.32 V,
        729.47 mAh g Zn
        − 1 and 132.82 mW cm − 2 ). The discharging curves
        with diﬀerent current densities (from 5 to 100 mAcm − 2 ) ex-
        hibit that the Ga-O-PtPd-based ZMABs exhibited a higher dis-
        charging curves than the Pt/C + IrO 2-based ZMABs (Figure 5e ).
        Herein, the corresponding ORR performance is further evalu-
        ated in Figure S10 (Supporting Information). The half-wave po-
        tential of Ga-O-PtPd is ≈ 0.93 V versus RHE, which is higher than
        these of PtPd (0.87 V) and commercial Pt/C (0.89 V), indicating
        the Ga species can greatly enhance the O 2 reduction during the
        discharging process. In addition, the Ga-O-PtPd-based ZMABs
        present outstanding cycling stability, which could operate con-
        tinuously for 215 h without obvious performance loss at 5 mA
        cm − 2 , higher than that of the Pt/C + IrO 2-based ZMABs. Over-
        all, the Ga-O-PtPd-based ZMABs own excellent electrochemical
        performance at room temperature. Furthermore, the promoting
        eﬀect during the charging process of Ga-O-PtPd can be clearly
        observed in Figure S11 (Supporting Information). The benefits
        of this substitution are obvious. After adding methanol, the ad-
        ditional voltage required is reduced at the same current density.
        The methanol reaction process involves the production of CO and
        carbonyl groups, and the adsorption capacity at the active site is
        relatively strong. With the increase of voltage, these species begin
        to oxidize. The synergistic eﬀect of multiple metal sites enhanced
        its anti-toxicity, which was also consistent with our previous ex-
        periments and characterization. Thus, a methanol-assisted strat-
        egy can efficiently improve the catalytic performance (activity and
        stability) of rechargeable Zn-Air Batteries over Ga-O-PtPd-triple
        sites.
        Adv. Energy Mater. 2025 , 15 , 2500421
        © 2025 Wiley-VCH GmbH
        2500421 (6 of 8)
        www.advancedsciencenews.com
        www.advenergymat.de
        Figure 5. a) Schematic diagram of the ZMABs. b) OCV of ZMABs. c) Discharging capacity plots at a constant current density in 5 mA cm − 2 . d) Charge–
        discharge polarization curves and the corresponding power density curves. e) Discharging plateaus at diﬀerent current densities. f) Galvanostatic
        discharge/charge cycling curves for 215 h at current densities of 5 mA cm − 2 .""",
        
        'conclusion': """3. Conclusion
        In this work, the O-bridged triple sites catalysts (Ga-O-PtPd)
        were designed by decorating the single-atomic GaO x on the
        PtPd nanosheets, which achieves a high current density of 3.05
        mAcm − 2  of MOR, which is 5.65 times higher than commercial
        Pt/C (0.54 mAcm − 2 ), as well as remarkably stability and CO ads
        poison resistance. The CO diﬀuse reflectance infrared Fourier
        transform spectroscopy (CO-DRIFTS) results reveal that Ga-O-
        PtPd triple sites present a weak CO binding ability, reducing
        the generation of CO ads intermediate. In addition, the non-CO
        pathway making the Ga-O-PtPd-based Zn-methanol-air batteries
        present excellent activity and stability. """,
    }

    keyphrases = ['Binding energy', 'Cyclic voltammetry', 'Density of states', 'Electric capacitance-potential relationship', 'Electric current-potential relationship', 'Nanosheets', 'Open circuit voltage', 'Photoemission spectra', 'Surface area', 'Valence band', 'X-ray diffraction', 'Zn-air batteries', 'Open circuit potential', 'Photoelectron spectra', 'Zinc-air secondary batteries']

    extractor = BasicKeyphraseExtractor()
    # cleaned_sentences = extractor.extract_relevant_sentences(doc_segments, keyphrases)

    # for section, sentences in cleaned_sentences.items():
    #     print(f"\nSection: {section}")
    #     for i, s in enumerate(sentences, 1):
    #         print(f"{i}. {s}")

    cleaned_sentences = extractor.extract_relevant_sentences(doc_segments, keyphrases)

