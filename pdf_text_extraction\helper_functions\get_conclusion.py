"""
This module provides functionality to extract and process conclusion sections from academic papers.

The main function get_conclusion() takes a list of text content from a conclusion section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed conclusion text and page number.
Author: <PERSON>
Date: 06-12-24
"""


import re


def get_conclusion(content_list, conclusion_location):
    """
    Extract the conclusion content from the conclusion section of a paper.
    Args:
        conclusion_list (list): List of strings from the conclusion section.

    Returns:
        str: A string containing the conclusion content.
    """
    section_content = {}
    full_text = " "
    # print(f"{content_list =}")
    if content_list:
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text += item

        section_content["conclusion"] = full_text.strip()
        section_content["page_number"] = conclusion_location

        return section_content
    else:
        return full_text
