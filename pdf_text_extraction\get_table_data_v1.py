"""
PDF Table Data Extraction Module

This module provides functionality to extract tables and their captions from PDF documents
using coordinate-based detection and spaCy layout analysis. It handles the complete workflow
from PDF processing to structured text output.

Key Features:
    - Extracts tables using precise coordinate mapping
    - Matches table captions with their corresponding tables
    - Saves extracted data in a structured text format

Dependencies:
    - PyMuPDF (fitz)
    - spaCy
    - spacy_layout

Created by: Anand Jadhav
Date: 13-02-2025

"""

## Required Imports
import os
import re
import time
import fitz
import spacy
from spacy_layout import spaCyLayout #type:ignore

#############################################################
nlp = spacy.load("en_core_web_sm")

layout = spaCyLayout(nlp)




def extract_tables_with_coordinates(pdf_file, table_coordinates):
    """
    Extract table text from the PDF using given coordinates.

    Args:
        pdf_file (str): Path to the PDF file.
        table_coordinates (list): List of dictionaries containing x, y, width, height, page_no, and table_title.

    Returns:
        list: A list of dictionaries containing the page number, table title, and extracted table content.
    """

    try:
        # Open the PDF document
        pdf_document = fitz.open(pdf_file)

        # Extract table content based on coordinates
        table_content = []
        for coord in table_coordinates:
            # Adjust for zero-based indexing
            page_number = coord["page_no"] - 1
            rect = fitz.Rect(
                coord["x"],
                coord["y"],
                coord["x"] + coord["width"],
                coord["y"] + coord["height"]
            )
            # Extract text within the rectangle
            table_text = pdf_document[page_number].get_text("text", clip=rect).strip()

            # Append the result with table title
            table_content.append({
                # Use 1-based page numbering for output
                "page_num": coord["page_no"],
                "table_title": coord["table_title"],
                "table_data": table_text
            })

        pdf_document.close()

        return table_content
    except Exception as error:
        print(f"Error in extract_tables_with_coordinates: {error}")
        return []


def get_caption_and_table_coordinates(doc):
    """
    Get coordinates of the section labeled 'caption' if it appears 1 or 2 locations before a 'table' label,
    and map it with the corresponding 'table' coordinates.

    Args:
        doc: spaCy document object processed with spaCyLayout.

    Returns:
        list: A list of dictionaries with 'caption' and 'table' coordinates, page number, and text.
    """
    mapped_coordinates = []
    try:
        for page_idx, page in enumerate(doc._.pages):

            for idx, section in enumerate(page[1]):

                # Check for 'table' label
                if section.label_ == "table":

                    # Look for a 'caption' label in the preceding 1 or 2 positions
                    # Check 1 or 2 steps back
                    for offset in range(1, 3):

                        # Ensure index does not go out of bounds
                        if idx - offset >= 0:
                            preceding_section = page[1][idx - offset]

                            if preceding_section.label_ == "caption":

                                # Extract and store caption and table coordinates
                                mapped_coordinates.append({
                                    "page_no": section._.layout.page_no,

                                    "table_title": preceding_section.text,

                                    "table": {
                                        "x": section._.layout.x,
                                        "y": section._.layout.y,
                                        "width": section._.layout.width,
                                        "height": section._.layout.height,
                                        "text": section.text,
                                    }
                                })
                                # Stop checking further offsets for this table
                                break
        return mapped_coordinates

    except Exception as error:
        print(f"Error in get_caption_and_table_coordinates: {error}")
        return mapped_coordinates



def get_table_text_from_coordinates(pdf_file):
    """
    Extract table content from a PDF file along with their titles and page numbers.

    This function processes a PDF file to identify tables and their captions, extracts
    the table content using coordinates, and formats the output with table titles
    and data concatenated together.

    Args:
        pdf_file (str): Path to the PDF file to process.

    Returns:
        list: A list of dictionaries containing:
            - page_num (int): The page number where the table appears
            - table_data (str): Concatenated string of table title and content
                               in format: "{title} |*Table Title*| {table_content}"
    """
    def parse_mapped_coordinates(mapped_coordinates):
        """
        Convert mapped coordinates into a standardized list of dictionaries containing table information.

        Args:
            mapped_coordinates (list): List of dictionaries containing raw table mapping data with
                keys for table_title, page_no, and table coordinates.

        Returns:
            list: List of dictionaries with standardized table information containing:
                - x (float): X-coordinate of table
                - y (float): Y-coordinate of table
                - width (float): Width of table
                - height (float): Height of table
                - page_no (int): Page number where table appears
                - table_title (str): Title/caption of the table
        """

        parsed_data = []
        for item in mapped_coordinates:

            # Safely extract fields with default values if missing
            table_title = item.get("table_title", "NO TITLE")
            page_no = item.get("page_no", 1)
            table_coords = item.get("table", None)

            if table_coords:
                parsed_data.append({
                    "x": table_coords.get("x", 0),
                    "y": table_coords.get("y", 0),
                    "width": table_coords.get("width", 0),
                    "height": table_coords.get("height", 0),
                    "page_no": page_no,
                    "table_title": table_title
                })
            else:
                # Log or handle entries with missing "table" key
                print(f"Skipping entry with missing 'table': {item}")

        return parsed_data

    # Process the PDF file to create a spaCy Doc object
    doc = layout(pdf_file)

    # Get mapped coordinates (already includes table titles and coordinates)
    mapped_coordinates = get_caption_and_table_coordinates(doc)

    # Parse the mapped coordinates
    parsed_data = parse_mapped_coordinates(mapped_coordinates)

    # Extract tables from the PDF based on parsed coordinates
    tables = extract_tables_with_coordinates(pdf_file, parsed_data)

    # Format the output as per requirement
    table_content = []
    for table in tables:

        # Ensure we get non-empty table data
        table_data = table.get("table_data", "").strip()

        # Add "NO TITLE" if title is missing
        table_title = table.get("table_title", "NO TITLE")

        if table_data:  # Only include if table_data is not empty
            concatenated_content = f"{table_title} |*Table Title*| {table_data}"
            content = {
                "page_num": table["page_num"],
                "table_data": concatenated_content
            }
            table_content.append(content)

    return table_content



def save_dicts_to_text_file(dict_list, file_name):
    """
    Saves a list of dictionaries into a text file,
    with each dictionary written separately.

    Args:
    dict_list (list): List of dictionaries to write to the file.
    file_name (str): Name of the text file to save the data.
    """
    try:
        with open(file_name, "w", encoding="utf-8") as file:

            for i, item in enumerate(dict_list):

                file.write(f"--------------- TABLE {i + 1} -------------------\n")
                for key, value in item.items():
                    file.write(f"{key}: {value}\n")
                file.write("\n")

        print(f"Data successfully saved to {file_name}")

    except Exception as e:
        print(f"An error occurred: {e}")




if __name__ == "__main__":
    start =  time.time()
    # pdf_file = "06000703B.article.002.pdf"

    pdf_file_list = [
                    #  "06000703B.article.002.pdf",
                    #  "05344933T.article.004.pdf",
                    #  "36751975Q.article.001.pdf",
                     "04573738J.article.004.pdf"]

    for pdf_file in pdf_file_list:
        tan_name = pdf_file.split(".")[0]
        file_name = f"table_data_{tan_name}.txt"
        table_content =get_table_text_from_coordinates(pdf_file)
        save_dicts_to_text_file(table_content, file_name)
        print(f"{len(table_content)=}")

    end = time.time()
    print(f"Time taken: {round((end - start),2)} seconds...")
####################################################################