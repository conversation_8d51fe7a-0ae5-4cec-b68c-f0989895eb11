import json
import requests

def get_title_abstract_from_url(shipment_name, tan_number):
    # print(f"{shipment_name} >>> {tan_number}")
    url = "http://***************:5014/get_title_abstarct"
    payload = json.dumps({
    "shipment_name": shipment_name,
    "tan_number": tan_number
    })
    headers = {
    'Content-Type': 'application/json'
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    if response.status_code == 200:
        return response.text  ##json.loads(response.text)
    else:
        return None


if __name__ == "__main__":
    shipment_name = "982232"
    tan_name = "06721220Z"	

    response = get_title_abstract_from_url(shipment_name, tan_name)   
    print(response)