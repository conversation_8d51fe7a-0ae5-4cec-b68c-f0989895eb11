{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Table Data:\n", " TABLE 1. Demographics and Baseline Characteristics\n", "No. of patients\n", "(median [IQR])\n", "Group 0 (n = 8)\n", "Group 1 (n = 11)\n", "Group 2 (n = 34)\n", "Follow-up time in months\n", "(median [IQR])\n", "(median [IQR])\n", "53 (33 women, 20 men)\n", "37.9 [30.3;47.5]\n", "47.5 [30.3;50.4]\n", "51.8 [46.9;56.6]\n", "77 [49;126]\n", "28.0 [9.8;43.5]\n", "216\n", "266\n", "106 eyes\n", "observation period\n", "= no injections = 15.1%\n", "= unilateral injections = 20.8%\n", "= bilateral injections = 64.2%\n", "Abbreviations: IQR = interquartile range OD = oculus dexter; OS = oculus sinister.\n", "predictive value for the occurrence of CNV in the fellow\n", "some more extra extra extra words as a line to be kept outside of table data\n", "\n", "Notes:\n", " Patient Characteristics\n", "Age in years at baseline\n", "Injections per patient\n", "Total number of injections OD\n", "OS\n", "Treatment group at the end of the\n"]}], "source": ["import re\n", "\n", "\n", "def extract_table_and_notes(data):\n", "    \"\"\"\n", "    Extract table data and exclude non-table lines dynamically.\n", "\n", "    Args:\n", "        data (str): Input data as a string containing table and non-table content.\n", "\n", "    Returns:\n", "        dict: Dictionary with \"table_data\" and \"notes\" (lines outside the table).\n", "    \"\"\"\n", "    lines = data.split(\"\\n\")\n", "    table_lines = []\n", "    notes = []\n", "    inside_table = False\n", "\n", "    # Define patterns for table-like lines and non-table-like lines\n", "    table_line_pattern = re.compile(r\"[\\d\\w\\(\\)\\[\\];,:=]+\")  # Matches table-style lines\n", "    non_table_sentence_pattern = re.compile(\n", "        r\"^[A-Z][\\w\\s,;:()]+[.?!]?$\"\n", "    )  # Matches non-table sentence-like lines\n", "\n", "    for line in lines:\n", "        line = line.strip()\n", "\n", "        # Start detecting table data based on keywords like \"TABLE\" or similar\n", "        if not inside_table and re.match(r\"^TABLE\\s*\\d*.*\", line, re.IGNORECASE):\n", "            inside_table = True\n", "            table_lines.append(line)\n", "            continue\n", "\n", "        # If inside table, decide whether to include or exclude the line\n", "        if inside_table:\n", "            if table_line_pattern.match(line) and not non_table_sentence_pattern.match(line):\n", "                # Line looks like table data\n", "                table_lines.append(line)\n", "            else:\n", "                # Line looks like a note, stop collecting table data\n", "                notes.append(line)\n", "\n", "    return {\n", "        \"table_data\": \"\\n\".join(table_lines).strip(),\n", "        \"notes\": \"\\n\".join(notes).strip(),\n", "    }\n", "\n", "\n", "# Example Data\n", "data = \"\"\"TABLE 1. Demographics and Baseline Characteristics\n", "Patient Characteristics\n", "No. of patients\n", "Age in years at baseline\n", "(median [IQR])\n", "Group 0 (n = 8)\n", "Group 1 (n = 11)\n", "Group 2 (n = 34)\n", "Follow-up time in months\n", "(median [IQR])\n", "Injections per patient\n", "(median [IQR])\n", "Total number of injections OD\n", "OS\n", "53 (33 women, 20 men)\n", "37.9 [30.3;47.5]\n", "47.5 [30.3;50.4]\n", "51.8 [46.9;56.6]\n", "77 [49;126]\n", "28.0 [9.8;43.5]\n", "216\n", "266\n", "106 eyes\n", "Treatment group at the end of the\n", "observation period\n", "= no injections = 15.1%\n", "= unilateral injections = 20.8%\n", "= bilateral injections = 64.2%\n", "Abbreviations: IQR = interquartile range OD = oculus dexter; OS = oculus sinister.\n", "\n", "predictive value for the occurrence of CNV in the fellow\n", "some more extra extra extra words as a line to be kept outside of table data\"\"\"\n", "\n", "# Extract table data and notes\n", "result = extract_table_and_notes(data)\n", "\n", "print(\"Table Data:\\n\", result[\"table_data\"])\n", "print(\"\\nNotes:\\n\", result[\"notes\"])\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Table Data:\n", " TABLE 1. Demographics and Baseline Characteristics\n", "Patient Characteristics\n", "No. of patients\n", "Age in years at baseline\n", "(median [IQR])\n", "Group 0 (n = 8)\n", "Group 1 (n = 11)\n", "Group 2 (n = 34)\n", "Follow-up time in months\n", "(median [IQR])\n", "Injections per patient\n", "(median [IQR])\n", "Total number of injections OD\n", "OS\n", "53 (33 women, 20 men)\n", "37.9 [30.3;47.5]\n", "47.5 [30.3;50.4]\n", "51.8 [46.9;56.6]\n", "77 [49;126]\n", "28.0 [9.8;43.5]\n", "216\n", "266\n", "106 eyes\n", "Treatment group at the end of the\n", "observation period\n", "= no injections = 15.1%\n", "= unilateral injections = 20.8%\n", "= bilateral injections = 64.2%\n", "Abbreviations: IQR = interquartile range OD = oculus dexter; OS = oculus sinister.\n", "predictive value for the occurrence of CNV in the fellow\n"]}], "source": ["import re\n", "\n", "def extract_table_data(data):\n", "    \"\"\"\n", "    Extracts table data dynamically and determines when to stop based on patterns.\n", "\n", "    Args:\n", "        data (str): Input text containing table and non-table content.\n", "\n", "    Returns:\n", "        str: Extracted table data as a string.\n", "    \"\"\"\n", "    lines = data.split(\"\\n\")\n", "    table_data = []\n", "    inside_table = False\n", "\n", "    # Define patterns for table and non-table content\n", "    table_start_pattern = re.compile(r\"^TABLE\\s*\\d*.*\", re.IGNORECASE)  # Detects table header\n", "    table_line_pattern = re.compile(r\"^[A-Za-z0-9\\(\\)\\[\\];,:%=\\.\\-\\s]+$\")  # Detects structured table lines\n", "    stop_line_pattern = re.compile(r\"^[A-Z][\\w\\s,;:()]+[.?!]$\")  # Detects full sentences (descriptive)\n", "\n", "    for line in lines:\n", "        line = line.strip()\n", "\n", "        # Start collecting table data if we detect a table header\n", "        if not inside_table and table_start_pattern.match(line):\n", "            inside_table = True\n", "            table_data.append(line)\n", "            continue\n", "\n", "        # If inside the table, decide whether to keep the line or stop\n", "        if inside_table:\n", "            if table_line_pattern.match(line):\n", "                # Line looks like table content, add it to the table\n", "                table_data.append(line)\n", "            else:\n", "                # Check for stopping conditions\n", "                if stop_line_pattern.match(line) or line == \"\":\n", "                    # Line is descriptive or empty, stop collecting\n", "                    break\n", "                else:\n", "                    # Line may still be part of the table, keep collecting\n", "                    table_data.append(line)\n", "\n", "    return \"\\n\".join(table_data).strip()\n", "\n", "\n", "# Example Data\n", "data = \"\"\"TABLE 1. Demographics and Baseline Characteristics\n", "Patient Characteristics\n", "No. of patients\n", "Age in years at baseline\n", "(median [IQR])\n", "Group 0 (n = 8)\n", "Group 1 (n = 11)\n", "Group 2 (n = 34)\n", "Follow-up time in months\n", "(median [IQR])\n", "Injections per patient\n", "(median [IQR])\n", "Total number of injections OD\n", "OS\n", "53 (33 women, 20 men)\n", "37.9 [30.3;47.5]\n", "47.5 [30.3;50.4]\n", "51.8 [46.9;56.6]\n", "77 [49;126]\n", "28.0 [9.8;43.5]\n", "216\n", "266\n", "106 eyes\n", "Treatment group at the end of the\n", "observation period\n", "= no injections = 15.1%\n", "= unilateral injections = 20.8%\n", "= bilateral injections = 64.2%\n", "Abbreviations: IQR = interquartile range OD = oculus dexter; OS = oculus sinister.\n", "predictive value for the occurrence of CNV in the fellow\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "# Extract table data\n", "table_data = extract_table_data(data)\n", "\n", "print(\"Table Data:\\n\", table_data)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module layoutparser has no attribute Detectron2LayoutModel", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 61\u001b[0m\n\u001b[0;32m     59\u001b[0m pdf_path \u001b[38;5;241m=\u001b[39m  \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124m***********\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mbio-act-curation\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mMAC-Projects\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mIntegrated-Indexing\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mshipments\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124m982110\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124m06000703B\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124m06000703B.article.002.pdf\u001b[39m\u001b[38;5;124m\"\u001b[39m  \n\u001b[0;32m     60\u001b[0m output_csv_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moutput_table_data.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m \n\u001b[1;32m---> 61\u001b[0m \u001b[43mextract_pdf_table\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpdf_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moutput_csv_path\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[1], line 11\u001b[0m, in \u001b[0;36mextract_pdf_table\u001b[1;34m(pdf_path, output_csv_path)\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mextract_pdf_table\u001b[39m(pdf_path, output_csv_path):\n\u001b[0;32m     10\u001b[0m     \u001b[38;5;66;03m# Load the pre-trained table detection model\u001b[39;00m\n\u001b[1;32m---> 11\u001b[0m     model \u001b[38;5;241m=\u001b[39m \u001b[43mlp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mDetectron2LayoutModel\u001b[49m(\n\u001b[0;32m     12\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlp://PubLayNet/table\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     13\u001b[0m         extra_config\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMODEL.ROI_HEADS.SCORE_THRESH_TEST\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m0.85\u001b[39m},\n\u001b[0;32m     14\u001b[0m         label_map\u001b[38;5;241m=\u001b[39m{\u001b[38;5;241m0\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTable\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[0;32m     15\u001b[0m     )\n\u001b[0;32m     17\u001b[0m     \u001b[38;5;66;03m# Open the PDF\u001b[39;00m\n\u001b[0;32m     18\u001b[0m     pdf_document \u001b[38;5;241m=\u001b[39m fitz\u001b[38;5;241m.\u001b[39mopen(pdf_path)\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages\\layoutparser\\file_utils.py:226\u001b[0m, in \u001b[0;36m_LazyModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m    224\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(module, name)\n\u001b[0;32m    225\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 226\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodule \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m has no attribute \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    228\u001b[0m \u001b[38;5;28msetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, name, value)\n\u001b[0;32m    229\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m value\n", "\u001b[1;31mAttributeError\u001b[0m: module layoutparser has no attribute Detectron2LayoutModel"]}], "source": ["import layoutparser as lp\n", "import fitz  # PyMuPDF\n", "import pytesseract\n", "import cv2\n", "import pandas as pd\n", "from io import BytesIO\n", "import numpy as np\n", "\n", "def extract_pdf_table(pdf_path, output_csv_path):\n", "    # Load the pre-trained table detection model\n", "    model = lp.Detectron2LayoutModel(\n", "        'lp://PubLayNet/table',\n", "        extra_config={\"MODEL.ROI_HEADS.SCORE_THRESH_TEST\": 0.85},\n", "        label_map={0: \"Table\"}\n", "    )\n", "\n", "    # Open the PDF\n", "    pdf_document = fitz.open(pdf_path)\n", "\n", "    table_data = []  # To store extracted table data\n", "\n", "    for page_num in range(len(pdf_document)):\n", "        page = pdf_document[page_num]\n", "        pix = page.get_pixmap()  # Convert PDF page to image\n", "        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)\n", "\n", "        # Convert to OpenCV format\n", "        img_cv = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)\n", "\n", "        # Detect tables using the model\n", "        layout = model.detect(img_cv)\n", "        tables = [block for block in layout if block.type == \"Table\"]\n", "\n", "        for table in tables:\n", "            # Crop table region\n", "            x1, y1, x2, y2 = map(int, table.coordinates)\n", "            cropped_table = img_cv[y1:y2, x1:x2]\n", "\n", "            # Convert cropped image to grayscale for OCR\n", "            cropped_gray = cv2.cvtColor(cropped_table, cv2.COLOR_BGR2GRAY)\n", "\n", "            # Perform OCR on the cropped table\n", "            table_text = pytesseract.image_to_string(cropped_gray, config=\"--psm 6\")\n", "\n", "            # Clean and structure table data\n", "            table_lines = table_text.strip().split(\"\\n\")\n", "            structured_table = [line.split(\"\\t\") for line in table_lines if line.strip()]\n", "            table_data.extend(structured_table)\n", "\n", "    # Save the table data to CSV\n", "    if table_data:\n", "        df = pd.DataFrame(table_data)\n", "        df.to_csv(output_csv_path, index=False, header=False)\n", "        print(f\"Table data extracted and saved to {output_csv_path}\")\n", "    else:\n", "        print(\"No table data found in the PDF.\")\n", "\n", "\n", "pdf_path =  r\"\\\\***********\\bio-act-curation\\MAC-Projects\\Integrated-Indexing\\shipments\\982110\\06000703B\\06000703B.article.002.pdf\"  \n", "output_csv_path = \"output_table_data.csv\" \n", "extract_pdf_table(pdf_path, output_csv_path)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting layoutparserNote: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.3.1 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "  Downloading layoutparser-0.3.4-py3-none-any.whl.metadata (7.7 kB)\n", "Requirement already satisfied: PyMuPDF in c:\\users\\<USER>\\appdata\\roaming\\python\\python39\\site-packages (1.24.13)\n", "Collecting opencv-python-headless\n", "  Downloading opencv_python_headless-*********-cp37-abi3-win_amd64.whl.metadata (20 kB)\n", "Collecting pytesseract\n", "  Downloading pytesseract-0.3.13-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser) (1.24.3)\n", "Collecting opencv-python (from layoutparser)\n", "  Downloading opencv_python-*********-cp37-abi3-win_amd64.whl.metadata (20 kB)\n", "Requirement already satisfied: scipy in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser) (1.13.1)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser) (2.2.2)\n", "Requirement already satisfied: pillow in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser) (10.3.0)\n", "Requirement already satisfied: pyyaml>=5.1 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser) (6.0)\n", "Collecting iopath (from <PERSON><PERSON><PERSON>)\n", "  Downloading iopath-0.1.10.tar.gz (42 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: pdfplumber in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser) (0.11.4)\n", "Collecting pdf2image (from layoutparser)\n", "  Using cached pdf2image-1.17.0-py3-none-any.whl.metadata (6.2 kB)\n", "Requirement already satisfied: packaging>=21.3 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pytesseract) (23.0)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from iopath->layoutparser) (4.67.1)\n", "Requirement already satisfied: typing_extensions in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from iopath->layoutparser) (4.5.0)\n", "Collecting portalocker (from iopath->layoutparser)\n", "  Downloading portalocker-3.1.1-py3-none-any.whl.metadata (8.6 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pandas->layoutparser) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pandas->layoutparser) (2023.3)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pandas->layoutparser) (2023.3)\n", "Requirement already satisfied: pdfminer.six==20231228 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfplumber->layoutparser) (20231228)\n", "Requirement already satisfied: pypdfium2>=4.18.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfplumber->layoutparser) (4.30.0)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber->layoutparser) (3.1.0)\n", "Requirement already satisfied: cryptography>=36.0.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber->layoutparser) (43.0.3)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from python-dateutil>=2.8.2->pandas->layoutparser) (1.16.0)\n", "Requirement already satisfied: pywin32>=226 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from portalocker->iopath->layoutparser) (305.1)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from tqdm->iopath->layoutparser) (0.4.6)\n", "Requirement already satisfied: cffi>=1.12 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber->layoutparser) (1.15.1)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber->layoutparser) (2.21)\n", "Downloading layoutparser-0.3.4-py3-none-any.whl (19.2 MB)\n", "   ---------------------------------------- 0.0/19.2 MB ? eta -:--:--\n", "   --------- ------------------------------ 4.7/19.2 MB 19.0 MB/s eta 0:00:01\n", "   --------------------- ------------------ 10.2/19.2 MB 22.7 MB/s eta 0:00:01\n", "   ------------------------------- -------- 15.2/19.2 MB 22.7 MB/s eta 0:00:01\n", "   ---------------------------------------  19.1/19.2 MB 23.2 MB/s eta 0:00:01\n", "   ---------------------------------------  19.1/19.2 MB 23.2 MB/s eta 0:00:01\n", "   ---------------------------------------  19.1/19.2 MB 23.2 MB/s eta 0:00:01\n", "   ---------------------------------------  19.1/19.2 MB 23.2 MB/s eta 0:00:01\n", "   ---------------------------------------  19.1/19.2 MB 23.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 19.2/19.2 MB 10.3 MB/s eta 0:00:00\n", "Downloading opencv_python_headless-*********-cp37-abi3-win_amd64.whl (39.4 MB)\n", "   ---------------------------------------- 0.0/39.4 MB ? eta -:--:--\n", "   --- ------------------------------------ 3.9/39.4 MB 23.5 MB/s eta 0:00:02\n", "   --------- ------------------------------ 9.2/39.4 MB 22.0 MB/s eta 0:00:02\n", "   -------------- ------------------------- 14.7/39.4 MB 22.0 MB/s eta 0:00:02\n", "   -------------------- ------------------- 19.9/39.4 MB 22.9 MB/s eta 0:00:01\n", "   ------------------------- -------------- 24.9/39.4 MB 22.9 MB/s eta 0:00:01\n", "   ------------------------------ --------- 30.1/39.4 MB 23.0 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 35.4/39.4 MB 23.2 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.4 MB 23.4 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.4 MB 23.4 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.4 MB 23.4 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.4 MB 23.4 MB/s eta 0:00:01\n", "   ---------------------------------------- 39.4/39.4 MB 15.7 MB/s eta 0:00:00\n", "Downloading pytesseract-0.3.13-py3-none-any.whl (14 kB)\n", "Downloading opencv_python-*********-cp37-abi3-win_amd64.whl (39.5 MB)\n", "   ---------------------------------------- 0.0/39.5 MB ? eta -:--:--\n", "   ---- ----------------------------------- 4.7/39.5 MB 22.0 MB/s eta 0:00:02\n", "   --------- ------------------------------ 9.4/39.5 MB 23.5 MB/s eta 0:00:02\n", "   --------------- ------------------------ 14.9/39.5 MB 23.5 MB/s eta 0:00:02\n", "   -------------------- ------------------- 19.9/39.5 MB 23.7 MB/s eta 0:00:01\n", "   ------------------------- -------------- 24.9/39.5 MB 23.9 MB/s eta 0:00:01\n", "   ------------------------------ --------- 29.6/39.5 MB 23.2 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 35.1/39.5 MB 23.7 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.5 MB 23.6 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.5 MB 23.6 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.5 MB 23.6 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.5 MB 23.6 MB/s eta 0:00:01\n", "   ---------------------------------------  39.3/39.5 MB 23.6 MB/s eta 0:00:01\n", "   ---------------------------------------- 39.5/39.5 MB 15.5 MB/s eta 0:00:00\n", "Using cached pdf2image-1.17.0-py3-none-any.whl (11 kB)\n", "Downloading portalocker-3.1.1-py3-none-any.whl (19 kB)\n", "Building wheels for collected packages: iopath\n", "  Building wheel for iopath (setup.py): started\n", "  Building wheel for iopath (setup.py): finished with status 'done'\n", "  Created wheel for iopath: filename=iopath-0.1.10-py3-none-any.whl size=31539 sha256=009c3bfb36a33487983777decf055d54c6ecf13d2b19b0e7a6b16bdc7f9ceda7\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\c1\\13\\6d\\441d8f2af76ee6d2a3e67eebb1d0c556fefcee0a8b32266a8e\n", "Successfully built iopath\n", "Installing collected packages: pytesseract, portalocker, pdf2image, opencv-python-headless, opencv-python, iopath, layoutparser\n", "Successfully installed iopath-0.1.10 layoutparser-0.3.4 opencv-python-********* opencv-python-headless-********* pdf2image-1.17.0 portalocker-3.1.1 pytesseract-0.3.13\n"]}], "source": ["%pip install layoutparser PyMuPDF opencv-python-headless pytesseract\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: layoutparser[detectron2] in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (0.3.4)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (1.24.3)\n", "Requirement already satisfied: opencv-python in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (*********)\n", "Requirement already satisfied: scipy in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (1.13.1)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (2.2.2)\n", "Requirement already satisfied: pillow in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (10.3.0)\n", "Requirement already satisfied: pyyaml>=5.1 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (6.0)\n", "Requirement already satisfied: iopath in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (0.1.10)\n", "Requirement already satisfied: pdfplumber in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (0.11.4)\n", "Requirement already satisfied: pdf2image in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from layoutparser[detectron2]) (1.17.0)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from iopath->layoutparser[detectron2]) (4.67.1)\n", "Requirement already satisfied: typing-extensions in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from iopath->layoutparser[detectron2]) (4.5.0)\n", "Requirement already satisfied: portalocker in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from iopath->layoutparser[detectron2]) (3.1.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pandas->layoutparser[detectron2]) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pandas->layoutparser[detectron2]) (2023.3)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pandas->layoutparser[detectron2]) (2023.3)\n", "Requirement already satisfied: pdfminer.six==20231228 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfplumber->layoutparser[detectron2]) (20231228)\n", "Requirement already satisfied: pypdfium2>=4.18.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfplumber->layoutparser[detectron2]) (4.30.0)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber->layoutparser[detectron2]) (3.1.0)\n", "Requirement already satisfied: cryptography>=36.0.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber->layoutparser[detectron2]) (43.0.3)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from python-dateutil>=2.8.2->pandas->layoutparser[detectron2]) (1.16.0)\n", "Requirement already satisfied: pywin32>=226 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from portalocker->iopath->layoutparser[detectron2]) (305.1)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from tqdm->iopath->layoutparser[detectron2]) (0.4.6)\n", "Requirement already satisfied: cffi>=1.12 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber->layoutparser[detectron2]) (1.15.1)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber->layoutparser[detectron2]) (2.21)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: layoutparser 0.3.4 does not provide the extra 'detectron2'\n", "\n", "[notice] A new release of pip is available: 24.3.1 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["%pip install layoutparser[detectron2]\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["^C\n", "Note: you may need to restart the kernel to use updated packages.\n", "Note: you may need to restart the kernel to use updated packages.\n", "Collecting git+https://github.com/facebookresearch/detectron2.git\n", "  Cloning https://github.com/facebookresearch/detectron2.git to c:\\users\\<USER>\\appdata\\local\\temp\\pip-req-build-otwlrg1u\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  Running command git clone --filter=blob:none --quiet https://github.com/facebookresearch/detectron2.git 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-req-build-otwlrg1u'\n", "  error: RPC failed; curl 18 HTTP/2 stream 5 was not closed cleanly before end of the underlying stream\n", "  error: 4136 bytes of body are still expected\n", "  fetch-pack: unexpected disconnect while reading sideband packet\n", "  fatal: early EOF\n", "  fatal: fetch-pack: invalid index-pack output\n", "  error: subprocess-exited-with-error\n", "  \n", "  × git clone --filter=blob:none --quiet https://github.com/facebookresearch/detectron2.git 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-req-build-otwlrg1u' did not run successfully.\n", "  │ exit code: 128\n", "  ╰─> See above for output.\n", "  \n", "  note: This error originates from a subprocess, and is likely not a problem with pip.\n", "\n", "[notice] A new release of pip is available: 24.3.1 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n", "error: subprocess-exited-with-error\n", "\n", "× git clone --filter=blob:none --quiet https://github.com/facebookresearch/detectron2.git 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-req-build-otwlrg1u' did not run successfully.\n", "│ exit code: 128\n", "╰─> See above for output.\n", "\n", "note: This error originates from a subprocess, and is likely not a problem with pip.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "spacy-transformers 1.2.3 requires spacy<4.0.0,>=3.5.0, but you have spacy 3.2.6 which is incompatible.\n", "spacy-transformers 1.2.3 requires transformers<4.29.0,>=3.4.0, but you have transformers 4.48.0 which is incompatible.\n", "\n", "[notice] A new release of pip is available: 24.3.1 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://download.pytorch.org/whl/cu113\n", "Requirement already satisfied: torch in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (2.0.0)\n", "Collecting torchvision\n", "  Downloading https://download.pytorch.org/whl/cu113/torchvision-0.13.1%2Bcu113-cp39-cp39-win_amd64.whl (4.7 MB)\n", "     ---------------------------------------- 0.0/4.7 MB ? eta -:--:--\n", "     --------------------------------- ------ 3.9/4.7 MB 19.5 MB/s eta 0:00:01\n", "     ---------------------------------------  4.7/4.7 MB 19.0 MB/s eta 0:00:01\n", "     ---------------------------------------  4.7/4.7 MB 19.0 MB/s eta 0:00:01\n", "     ---------------------------------------  4.7/4.7 MB 19.0 MB/s eta 0:00:01\n", "     ---------------------------------------  4.7/4.7 MB 19.0 MB/s eta 0:00:01\n", "     ---------------------------------------  4.7/4.7 MB 19.0 MB/s eta 0:00:01\n", "     ---------------------------------------- 4.7/4.7 MB 3.5 MB/s eta 0:00:00\n", "Collecting <PERSON><PERSON><PERSON>\n", "  Downloading https://download.pytorch.org/whl/cu113/torchaudio-0.12.1%2Bcu113-cp39-cp39-win_amd64.whl (1.2 MB)\n", "     ---------------------------------------- 0.0/1.2 MB ? eta -:--:--\n", "     ---------------------------------- ----- 1.0/1.2 MB 24.6 MB/s eta 0:00:01\n", "     ---------------------------------- ----- 1.0/1.2 MB 24.6 MB/s eta 0:00:01\n", "     ---------------------------------- ----- 1.0/1.2 MB 24.6 MB/s eta 0:00:01\n", "     ---------------------------------- ----- 1.0/1.2 MB 24.6 MB/s eta 0:00:01\n", "     ---------------------------------- ----- 1.0/1.2 MB 24.6 MB/s eta 0:00:01\n", "     ---------------------------------- ----- 1.0/1.2 MB 24.6 MB/s eta 0:00:01\n", "     ---------------------------------------- 1.2/1.2 MB 633.0 kB/s eta 0:00:00\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torch) (3.12.0)\n", "Requirement already satisfied: typing-extensions in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torch) (4.5.0)\n", "Requirement already satisfied: sympy in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torch) (1.11.1)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torch) (3.1)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torch) (3.1.2)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torchvision) (1.24.3)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torchvision) (2.32.3)\n", "Collecting torch\n", "  Downloading https://download.pytorch.org/whl/cu113/torch-1.12.1%2Bcu113-cp39-cp39-win_amd64.whl (2143.3 MB)\n", "     ---------------------------------------- 0.0/2.1 GB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.1 GB 21.5 MB/s eta 0:01:40\n", "     ---------------------------------------- 0.0/2.1 GB 23.2 MB/s eta 0:01:32\n", "     ---------------------------------------- 0.0/2.1 GB 24.0 MB/s eta 0:01:29\n", "     ---------------------------------------- 0.0/2.1 GB 23.9 MB/s eta 0:01:29\n", "     ---------------------------------------- 0.0/2.1 GB 23.4 MB/s eta 0:01:31\n", "      --------------------------------------- 0.0/2.1 GB 23.5 MB/s eta 0:01:30\n", "      --------------------------------------- 0.0/2.1 GB 23.6 MB/s eta 0:01:30\n", "      --------------------------------------- 0.0/2.1 GB 23.8 MB/s eta 0:01:29\n", "      --------------------------------------- 0.0/2.1 GB 23.6 MB/s eta 0:01:29\n", "      --------------------------------------- 0.1/2.1 GB 23.8 MB/s eta 0:01:28\n", "     - -------------------------------------- 0.1/2.1 GB 23.4 MB/s eta 0:01:30\n", "     - -------------------------------------- 0.1/2.1 GB 23.6 MB/s eta 0:01:29\n", "     - -------------------------------------- 0.1/2.1 GB 23.4 MB/s eta 0:01:29\n", "     - -------------------------------------- 0.1/2.1 GB 23.4 MB/s eta 0:01:29\n", "     - -------------------------------------- 0.1/2.1 GB 23.6 MB/s eta 0:01:28\n", "     - -------------------------------------- 0.1/2.1 GB 23.4 MB/s eta 0:01:29\n", "     - -------------------------------------- 0.1/2.1 GB 23.5 MB/s eta 0:01:28\n", "     - -------------------------------------- 0.1/2.1 GB 22.1 MB/s eta 0:01:34\n", "     - -------------------------------------- 0.1/2.1 GB 22.2 MB/s eta 0:01:33\n", "     - -------------------------------------- 0.1/2.1 GB 22.5 MB/s eta 0:01:32\n", "     - -------------------------------------- 0.1/2.1 GB 22.5 MB/s eta 0:01:31\n", "     - -------------------------------------- 0.1/2.1 GB 22.6 MB/s eta 0:01:31\n", "     -- ------------------------------------- 0.1/2.1 GB 22.2 MB/s eta 0:01:32\n", "     -- ------------------------------------- 0.1/2.1 GB 22.2 MB/s eta 0:01:32\n", "     -- ------------------------------------- 0.1/2.1 GB 22.2 MB/s eta 0:01:32\n", "     -- ------------------------------------- 0.1/2.1 GB 22.3 MB/s eta 0:01:31\n", "     -- ------------------------------------- 0.1/2.1 GB 22.2 MB/s eta 0:01:31\n", "     -- ------------------------------------- 0.1/2.1 GB 22.3 MB/s eta 0:01:30\n", "     -- ------------------------------------- 0.1/2.1 GB 22.3 MB/s eta 0:01:30\n", "     -- ------------------------------------- 0.1/2.1 GB 22.5 MB/s eta 0:01:29\n", "     -- ------------------------------------- 0.2/2.1 GB 22.5 MB/s eta 0:01:29\n", "     -- ------------------------------------- 0.2/2.1 GB 22.5 MB/s eta 0:01:29\n", "     -- ------------------------------------- 0.2/2.1 GB 22.4 MB/s eta 0:01:29\n", "     --- ------------------------------------ 0.2/2.1 GB 22.4 MB/s eta 0:01:29\n", "     --- ------------------------------------ 0.2/2.1 GB 22.5 MB/s eta 0:01:28\n", "     --- ------------------------------------ 0.2/2.1 GB 22.6 MB/s eta 0:01:28\n", "     --- ------------------------------------ 0.2/2.1 GB 22.6 MB/s eta 0:01:27\n", "     --- ------------------------------------ 0.2/2.1 GB 22.6 MB/s eta 0:01:27\n", "     --- ------------------------------------ 0.2/2.1 GB 22.7 MB/s eta 0:01:27\n", "     --- ------------------------------------ 0.2/2.1 GB 22.7 MB/s eta 0:01:26\n", "     --- ------------------------------------ 0.2/2.1 GB 22.7 MB/s eta 0:01:26\n", "     --- ------------------------------------ 0.2/2.1 GB 22.8 MB/s eta 0:01:25\n", "     --- ------------------------------------ 0.2/2.1 GB 22.7 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.8 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.8 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.8 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.8 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.7 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.2 MB/s eta 0:01:26\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.8 MB/s eta 0:01:24\n", "     ---- ----------------------------------- 0.2/2.1 GB 22.4 MB/s eta 0:01:25\n", "     ---- ----------------------------------- 0.3/2.1 GB 22.7 MB/s eta 0:01:24\n", "     ---- ----------------------------------- 0.3/2.1 GB 22.7 MB/s eta 0:01:24\n", "     ---- ----------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:24\n", "     ---- ----------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:23\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:23\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:23\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:23\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:23\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:22\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:22\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:22\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:22\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:22\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:22\n", "     ----- ---------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:22\n", "     ------ --------------------------------- 0.3/2.1 GB 22.5 MB/s eta 0:01:21\n", "     ------ --------------------------------- 0.3/2.1 GB 22.4 MB/s eta 0:01:21\n", "     ------ --------------------------------- 0.3/2.1 GB 22.3 MB/s eta 0:01:22\n", "     ------ --------------------------------- 0.3/2.1 GB 22.3 MB/s eta 0:01:22\n", "     ------ --------------------------------- 0.3/2.1 GB 22.2 MB/s eta 0:01:21\n", "     ------ --------------------------------- 0.3/2.1 GB 22.6 MB/s eta 0:01:20\n", "     ------ --------------------------------- 0.4/2.1 GB 22.6 MB/s eta 0:01:20\n", "     ------ --------------------------------- 0.4/2.1 GB 22.5 MB/s eta 0:01:20\n", "     ------ --------------------------------- 0.4/2.1 GB 22.5 MB/s eta 0:01:20\n", "     ------ --------------------------------- 0.4/2.1 GB 22.6 MB/s eta 0:01:19\n", "     ------ --------------------------------- 0.4/2.1 GB 22.7 MB/s eta 0:01:19\n", "     ------- -------------------------------- 0.4/2.1 GB 22.7 MB/s eta 0:01:18\n", "     ------- -------------------------------- 0.4/2.1 GB 22.7 MB/s eta 0:01:18\n", "     ------- -------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:18\n", "     ------- -------------------------------- 0.4/2.1 GB 22.7 MB/s eta 0:01:17\n", "     ------- -------------------------------- 0.4/2.1 GB 22.7 MB/s eta 0:01:17\n", "     ------- -------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:17\n", "     ------- -------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:17\n", "     ------- -------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:16\n", "     ------- -------------------------------- 0.4/2.1 GB 22.9 MB/s eta 0:01:16\n", "     ------- -------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:16\n", "     ------- -------------------------------- 0.4/2.1 GB 22.7 MB/s eta 0:01:16\n", "     -------- ------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:16\n", "     -------- ------------------------------- 0.4/2.1 GB 22.8 MB/s eta 0:01:15\n", "     -------- ------------------------------- 0.4/2.1 GB 22.9 MB/s eta 0:01:15\n", "     -------- ------------------------------- 0.4/2.1 GB 22.9 MB/s eta 0:01:14\n", "     -------- ------------------------------- 0.5/2.1 GB 22.9 MB/s eta 0:01:14\n", "     -------- ------------------------------- 0.5/2.1 GB 22.8 MB/s eta 0:01:14\n", "     -------- ------------------------------- 0.5/2.1 GB 22.8 MB/s eta 0:01:14\n", "     -------- ------------------------------- 0.5/2.1 GB 22.8 MB/s eta 0:01:14\n", "     -------- ------------------------------- 0.5/2.1 GB 22.7 MB/s eta 0:01:14\n", "     -------- ------------------------------- 0.5/2.1 GB 22.4 MB/s eta 0:01:15\n", "     -------- ------------------------------- 0.5/2.1 GB 22.6 MB/s eta 0:01:14\n", "     --------- ------------------------------ 0.5/2.1 GB 22.4 MB/s eta 0:01:15\n", "     --------- ------------------------------ 0.5/2.1 GB 22.7 MB/s eta 0:01:14\n", "     --------- ------------------------------ 0.5/2.1 GB 22.3 MB/s eta 0:01:15\n", "     --------- ------------------------------ 0.5/2.1 GB 22.4 MB/s eta 0:01:14\n", "     --------- ------------------------------ 0.5/2.1 GB 22.5 MB/s eta 0:01:13\n", "     --------- ------------------------------ 0.5/2.1 GB 22.9 MB/s eta 0:01:12\n", "     --------- ------------------------------ 0.5/2.1 GB 22.6 MB/s eta 0:01:13\n", "     --------- ------------------------------ 0.5/2.1 GB 23.1 MB/s eta 0:01:11\n", "     --------- ------------------------------ 0.5/2.1 GB 22.7 MB/s eta 0:01:12\n", "     --------- ------------------------------ 0.5/2.1 GB 22.7 MB/s eta 0:01:12\n", "     --------- ------------------------------ 0.5/2.1 GB 22.6 MB/s eta 0:01:12\n", "     --------- ------------------------------ 0.5/2.1 GB 22.6 MB/s eta 0:01:12\n", "     ---------- ----------------------------- 0.5/2.1 GB 22.6 MB/s eta 0:01:12\n", "     ---------- ----------------------------- 0.5/2.1 GB 22.7 MB/s eta 0:01:11\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:11\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:11\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:10\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:10\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.8 MB/s eta 0:01:09\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:10\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:09\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:09\n", "     ---------- ----------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:09\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.7 MB/s eta 0:01:09\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.8 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.8 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.8 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.8 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.8 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:08\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:07\n", "     ----------- ---------------------------- 0.6/2.1 GB 22.5 MB/s eta 0:01:07\n", "     ------------ --------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:07\n", "     ------------ --------------------------- 0.6/2.1 GB 22.6 MB/s eta 0:01:07\n", "     ------------ --------------------------- 0.7/2.1 GB 22.6 MB/s eta 0:01:06\n", "     ------------ --------------------------- 0.7/2.1 GB 22.6 MB/s eta 0:01:06\n", "     ------------ --------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:06\n", "     ------------ --------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:06\n", "     ------------ --------------------------- 0.7/2.1 GB 22.6 MB/s eta 0:01:06\n", "     ------------ --------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:06\n", "     ------------ --------------------------- 0.7/2.1 GB 23.0 MB/s eta 0:01:04\n", "     ------------ --------------------------- 0.7/2.1 GB 22.6 MB/s eta 0:01:05\n", "     ------------ --------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:05\n", "     ------------- -------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:05\n", "     ------------- -------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:05\n", "     ------------- -------------------------- 0.7/2.1 GB 22.6 MB/s eta 0:01:04\n", "     ------------- -------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:04\n", "     ------------- -------------------------- 0.7/2.1 GB 22.5 MB/s eta 0:01:04\n", "     ------------- -------------------------- 0.7/2.1 GB 23.1 MB/s eta 0:01:02\n", "     ------------- -------------------------- 0.7/2.1 GB 22.9 MB/s eta 0:01:02\n", "     ------------- -------------------------- 0.7/2.1 GB 23.0 MB/s eta 0:01:02\n", "     ------------- -------------------------- 0.7/2.1 GB 22.8 MB/s eta 0:01:02\n", "     ------------- -------------------------- 0.7/2.1 GB 22.7 MB/s eta 0:01:02\n", "     ------------- -------------------------- 0.7/2.1 GB 23.1 MB/s eta 0:01:01\n", "     -------------- ------------------------- 0.8/2.1 GB 23.2 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 23.2 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 23.2 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 23.2 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 22.8 MB/s eta 0:01:01\n", "     -------------- ------------------------- 0.8/2.1 GB 22.7 MB/s eta 0:01:01\n", "     -------------- ------------------------- 0.8/2.1 GB 22.9 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 22.8 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 22.2 MB/s eta 0:01:02\n", "     -------------- ------------------------- 0.8/2.1 GB 22.6 MB/s eta 0:01:00\n", "     -------------- ------------------------- 0.8/2.1 GB 22.3 MB/s eta 0:01:01\n", "     -------------- ------------------------- 0.8/2.1 GB 22.6 MB/s eta 0:01:00\n", "     --------------- ------------------------ 0.8/2.1 GB 22.6 MB/s eta 0:01:00\n", "     --------------- ------------------------ 0.8/2.1 GB 22.6 MB/s eta 0:00:59\n", "     --------------- ------------------------ 0.8/2.1 GB 22.6 MB/s eta 0:00:59\n", "     --------------- ------------------------ 0.8/2.1 GB 22.6 MB/s eta 0:00:59\n", "     --------------- ------------------------ 0.8/2.1 GB 22.6 MB/s eta 0:00:59\n", "     --------------- ------------------------ 0.8/2.1 GB 22.6 MB/s eta 0:00:59\n", "     --------------- ------------------------ 0.8/2.1 GB 22.7 MB/s eta 0:00:58\n", "     --------------- ------------------------ 0.8/2.1 GB 22.7 MB/s eta 0:00:58\n", "     --------------- ------------------------ 0.8/2.1 GB 22.7 MB/s eta 0:00:58\n", "     --------------- ------------------------ 0.8/2.1 GB 22.5 MB/s eta 0:00:58\n", "     --------------- ------------------------ 0.8/2.1 GB 22.4 MB/s eta 0:00:58\n", "     --------------- ------------------------ 0.9/2.1 GB 22.4 MB/s eta 0:00:58\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.4 MB/s eta 0:00:58\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.0 MB/s eta 0:00:59\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.0 MB/s eta 0:00:59\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.1 MB/s eta 0:00:58\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:57\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:57\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.9 MB/s eta 0:00:55\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.8 MB/s eta 0:00:56\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:56\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:56\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.4 MB/s eta 0:00:56\n", "     ---------------- ----------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:56\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.5 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.4 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.4 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:54\n", "     ----------------- ---------------------- 0.9/2.1 GB 22.3 MB/s eta 0:00:54\n", "     ----------------- ---------------------- 0.9/2.1 GB 21.6 MB/s eta 0:00:56\n", "     ----------------- ---------------------- 1.0/2.1 GB 22.0 MB/s eta 0:00:55\n", "     ----------------- ---------------------- 1.0/2.1 GB 22.0 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 22.2 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 22.0 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 22.0 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 22.0 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.6 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.5 MB/s eta 0:00:55\n", "     ------------------ --------------------- 1.0/2.1 GB 21.3 MB/s eta 0:00:55\n", "     ------------------ --------------------- 1.0/2.1 GB 21.4 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.4 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.3 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.3 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.2 MB/s eta 0:00:54\n", "     ------------------ --------------------- 1.0/2.1 GB 21.2 MB/s eta 0:00:54\n", "     ------------------- -------------------- 1.0/2.1 GB 21.3 MB/s eta 0:00:53\n", "     ------------------- -------------------- 1.0/2.1 GB 22.0 MB/s eta 0:00:51\n", "     ------------------- -------------------- 1.0/2.1 GB 21.6 MB/s eta 0:00:52\n", "     ------------------- -------------------- 1.0/2.1 GB 21.6 MB/s eta 0:00:52\n", "     ------------------- -------------------- 1.0/2.1 GB 21.5 MB/s eta 0:00:52\n", "     ------------------- -------------------- 1.0/2.1 GB 22.1 MB/s eta 0:00:50\n", "     ------------------- -------------------- 1.0/2.1 GB 21.8 MB/s eta 0:00:51\n", "     ------------------- -------------------- 1.0/2.1 GB 21.3 MB/s eta 0:00:52\n", "     ------------------- -------------------- 1.1/2.1 GB 21.7 MB/s eta 0:00:51\n", "     ------------------- -------------------- 1.1/2.1 GB 21.4 MB/s eta 0:00:51\n", "     ------------------- -------------------- 1.1/2.1 GB 21.4 MB/s eta 0:00:51\n", "     ------------------- -------------------- 1.1/2.1 GB 21.4 MB/s eta 0:00:51\n", "     ------------------- -------------------- 1.1/2.1 GB 20.9 MB/s eta 0:00:52\n", "     ------------------- -------------------- 1.1/2.1 GB 20.8 MB/s eta 0:00:52\n", "     -------------------- ------------------- 1.1/2.1 GB 20.6 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 20.3 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 20.6 MB/s eta 0:00:52\n", "     -------------------- ------------------- 1.1/2.1 GB 20.6 MB/s eta 0:00:52\n", "     -------------------- ------------------- 1.1/2.1 GB 20.3 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 20.2 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 20.0 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 20.8 MB/s eta 0:00:51\n", "     -------------------- ------------------- 1.1/2.1 GB 20.5 MB/s eta 0:00:52\n", "     -------------------- ------------------- 1.1/2.1 GB 20.0 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 19.9 MB/s eta 0:00:53\n", "     -------------------- ------------------- 1.1/2.1 GB 19.5 MB/s eta 0:00:54\n", "     -------------------- ------------------- 1.1/2.1 GB 19.5 MB/s eta 0:00:54\n", "     -------------------- ------------------- 1.1/2.1 GB 19.2 MB/s eta 0:00:55\n", "     -------------------- ------------------- 1.1/2.1 GB 18.9 MB/s eta 0:00:55\n", "     -------------------- ------------------- 1.1/2.1 GB 18.7 MB/s eta 0:00:56\n", "     -------------------- ------------------- 1.1/2.1 GB 18.7 MB/s eta 0:00:56\n", "     -------------------- ------------------- 1.1/2.1 GB 18.4 MB/s eta 0:00:56\n", "     -------------------- ------------------- 1.1/2.1 GB 18.4 MB/s eta 0:00:56\n", "     -------------------- ------------------- 1.1/2.1 GB 18.0 MB/s eta 0:00:58\n", "     -------------------- ------------------- 1.1/2.1 GB 17.8 MB/s eta 0:00:58\n", "     -------------------- ------------------- 1.1/2.1 GB 17.8 MB/s eta 0:00:58\n", "     -------------------- ------------------- 1.1/2.1 GB 18.1 MB/s eta 0:00:57\n", "     --------------------- ------------------ 1.1/2.1 GB 17.8 MB/s eta 0:00:58\n", "     --------------------- ------------------ 1.1/2.1 GB 17.8 MB/s eta 0:00:58\n", "     --------------------- ------------------ 1.1/2.1 GB 17.6 MB/s eta 0:00:58\n", "     --------------------- ------------------ 1.1/2.1 GB 17.3 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.1/2.1 GB 17.2 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.1/2.1 GB 17.1 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.1/2.1 GB 17.2 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.1/2.1 GB 17.0 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.1/2.1 GB 16.8 MB/s eta 0:01:00\n", "     --------------------- ------------------ 1.1/2.1 GB 17.0 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.2/2.1 GB 16.9 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.2/2.1 GB 16.6 MB/s eta 0:01:00\n", "     --------------------- ------------------ 1.2/2.1 GB 16.6 MB/s eta 0:01:00\n", "     --------------------- ------------------ 1.2/2.1 GB 16.5 MB/s eta 0:01:00\n", "     --------------------- ------------------ 1.2/2.1 GB 16.4 MB/s eta 0:01:00\n", "     --------------------- ------------------ 1.2/2.1 GB 16.5 MB/s eta 0:00:59\n", "     --------------------- ------------------ 1.2/2.1 GB 16.4 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.4 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.4 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.4 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.5 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.5 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.5 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.5 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.5 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 16.5 MB/s eta 0:00:58\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.9 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.8 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.8 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.6 MB/s eta 0:01:00\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.6 MB/s eta 0:01:00\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.6 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.6 MB/s eta 0:00:59\n", "     ---------------------- ----------------- 1.2/2.1 GB 15.6 MB/s eta 0:00:59\n", "     ----------------------- ---------------- 1.2/2.1 GB 15.7 MB/s eta 0:00:58\n", "     ----------------------- ---------------- 1.2/2.1 GB 15.9 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.2/2.1 GB 15.8 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.7 MB/s eta 0:00:56\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.7 MB/s eta 0:00:56\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.7 MB/s eta 0:00:56\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.4 MB/s eta 0:00:57\n", "     ----------------------- ---------------- 1.3/2.1 GB 15.4 MB/s eta 0:00:57\n", "     ------------------------ --------------- 1.3/2.1 GB 15.4 MB/s eta 0:00:56\n", "     ------------------------ --------------- 1.3/2.1 GB 15.3 MB/s eta 0:00:56\n", "     ------------------------ --------------- 1.3/2.1 GB 15.5 MB/s eta 0:00:55\n", "     ------------------------ --------------- 1.3/2.1 GB 15.4 MB/s eta 0:00:55\n", "     ------------------------ --------------- 1.3/2.1 GB 15.5 MB/s eta 0:00:55\n", "     ------------------------ --------------- 1.3/2.1 GB 15.6 MB/s eta 0:00:54\n", "     ------------------------ --------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:53\n", "     ------------------------ --------------- 1.3/2.1 GB 15.7 MB/s eta 0:00:53\n", "     ------------------------ --------------- 1.3/2.1 GB 15.9 MB/s eta 0:00:52\n", "     ------------------------ --------------- 1.3/2.1 GB 15.9 MB/s eta 0:00:52\n", "     ------------------------ --------------- 1.3/2.1 GB 15.9 MB/s eta 0:00:51\n", "     ------------------------ --------------- 1.3/2.1 GB 16.1 MB/s eta 0:00:51\n", "     ------------------------ --------------- 1.3/2.1 GB 16.1 MB/s eta 0:00:51\n", "     ------------------------- -------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:51\n", "     ------------------------- -------------- 1.3/2.1 GB 15.9 MB/s eta 0:00:51\n", "     ------------------------- -------------- 1.3/2.1 GB 15.8 MB/s eta 0:00:51\n", "     ------------------------- -------------- 1.4/2.1 GB 15.9 MB/s eta 0:00:50\n", "     ------------------------- -------------- 1.4/2.1 GB 15.9 MB/s eta 0:00:50\n", "     ------------------------- -------------- 1.4/2.1 GB 15.9 MB/s eta 0:00:50\n", "     ------------------------- -------------- 1.4/2.1 GB 16.6 MB/s eta 0:00:47\n", "     ------------------------- -------------- 1.4/2.1 GB 16.6 MB/s eta 0:00:47\n", "     ------------------------- -------------- 1.4/2.1 GB 17.1 MB/s eta 0:00:46\n", "     ------------------------- -------------- 1.4/2.1 GB 17.3 MB/s eta 0:00:45\n", "     ------------------------- -------------- 1.4/2.1 GB 17.3 MB/s eta 0:00:45\n", "     ------------------------- -------------- 1.4/2.1 GB 17.1 MB/s eta 0:00:45\n", "     ------------------------- -------------- 1.4/2.1 GB 17.4 MB/s eta 0:00:44\n", "     ------------------------- -------------- 1.4/2.1 GB 17.4 MB/s eta 0:00:44\n", "     -------------------------- ------------- 1.4/2.1 GB 17.9 MB/s eta 0:00:42\n", "     -------------------------- ------------- 1.4/2.1 GB 17.9 MB/s eta 0:00:42\n", "     -------------------------- ------------- 1.4/2.1 GB 17.9 MB/s eta 0:00:42\n", "     -------------------------- ------------- 1.4/2.1 GB 18.1 MB/s eta 0:00:41\n", "     -------------------------- ------------- 1.4/2.1 GB 18.1 MB/s eta 0:00:41\n", "     -------------------------- ------------- 1.4/2.1 GB 18.3 MB/s eta 0:00:40\n", "     -------------------------- ------------- 1.4/2.1 GB 18.3 MB/s eta 0:00:40\n", "     -------------------------- ------------- 1.4/2.1 GB 18.1 MB/s eta 0:00:40\n", "     -------------------------- ------------- 1.4/2.1 GB 18.8 MB/s eta 0:00:39\n", "     -------------------------- ------------- 1.4/2.1 GB 18.8 MB/s eta 0:00:39\n", "     -------------------------- ------------- 1.4/2.1 GB 18.3 MB/s eta 0:00:40\n", "     -------------------------- ------------- 1.4/2.1 GB 18.4 MB/s eta 0:00:39\n", "     -------------------------- ------------- 1.4/2.1 GB 18.4 MB/s eta 0:00:39\n", "     -------------------------- ------------- 1.4/2.1 GB 18.0 MB/s eta 0:00:39\n", "     --------------------------- ------------ 1.4/2.1 GB 18.1 MB/s eta 0:00:39\n", "     --------------------------- ------------ 1.5/2.1 GB 18.4 MB/s eta 0:00:38\n", "     --------------------------- ------------ 1.5/2.1 GB 19.9 MB/s eta 0:00:35\n", "     --------------------------- ------------ 1.5/2.1 GB 19.6 MB/s eta 0:00:35\n", "     --------------------------- ------------ 1.5/2.1 GB 19.3 MB/s eta 0:00:36\n", "     --------------------------- ------------ 1.5/2.1 GB 19.9 MB/s eta 0:00:34\n", "     --------------------------- ------------ 1.5/2.1 GB 19.5 MB/s eta 0:00:35\n", "     --------------------------- ------------ 1.5/2.1 GB 19.3 MB/s eta 0:00:35\n", "     --------------------------- ------------ 1.5/2.1 GB 19.4 MB/s eta 0:00:34\n", "     --------------------------- ------------ 1.5/2.1 GB 19.4 MB/s eta 0:00:34\n", "     --------------------------- ------------ 1.5/2.1 GB 19.1 MB/s eta 0:00:35\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.5 MB/s eta 0:00:33\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.6 MB/s eta 0:00:33\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.6 MB/s eta 0:00:33\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.8 MB/s eta 0:00:32\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.6 MB/s eta 0:00:32\n", "     ---------------------------- ----------- 1.5/2.1 GB 20.1 MB/s eta 0:00:31\n", "     ---------------------------- ----------- 1.5/2.1 GB 20.1 MB/s eta 0:00:31\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.4 MB/s eta 0:00:32\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.4 MB/s eta 0:00:32\n", "     ---------------------------- ----------- 1.5/2.1 GB 18.9 MB/s eta 0:00:33\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.7 MB/s eta 0:00:31\n", "     ---------------------------- ----------- 1.5/2.1 GB 19.7 MB/s eta 0:00:31\n", "     ---------------------------- ----------- 1.6/2.1 GB 19.8 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.7 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.7 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.7 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.7 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.4 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.4 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.3 MB/s eta 0:00:30\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.4 MB/s eta 0:00:29\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.9 MB/s eta 0:00:28\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.8 MB/s eta 0:00:28\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.6 MB/s eta 0:00:29\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.2 MB/s eta 0:00:29\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.0 MB/s eta 0:00:29\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.0 MB/s eta 0:00:29\n", "     ----------------------------- ---------- 1.6/2.1 GB 19.1 MB/s eta 0:00:29\n", "     ------------------------------ --------- 1.6/2.1 GB 19.6 MB/s eta 0:00:27\n", "     ------------------------------ --------- 1.6/2.1 GB 19.6 MB/s eta 0:00:27\n", "     ------------------------------ --------- 1.6/2.1 GB 19.2 MB/s eta 0:00:28\n", "     ------------------------------ --------- 1.6/2.1 GB 19.6 MB/s eta 0:00:27\n", "     ------------------------------ --------- 1.6/2.1 GB 19.7 MB/s eta 0:00:26\n", "     ------------------------------ --------- 1.6/2.1 GB 19.7 MB/s eta 0:00:26\n", "     ------------------------------ --------- 1.6/2.1 GB 20.3 MB/s eta 0:00:25\n", "     ------------------------------ --------- 1.6/2.1 GB 20.0 MB/s eta 0:00:26\n", "     ------------------------------ --------- 1.6/2.1 GB 19.7 MB/s eta 0:00:26\n", "     ------------------------------ --------- 1.7/2.1 GB 20.0 MB/s eta 0:00:25\n", "     ------------------------------ --------- 1.7/2.1 GB 19.9 MB/s eta 0:00:25\n", "     ------------------------------ --------- 1.7/2.1 GB 20.1 MB/s eta 0:00:24\n", "     ------------------------------- -------- 1.7/2.1 GB 20.0 MB/s eta 0:00:24\n", "     ------------------------------- -------- 1.7/2.1 GB 20.4 MB/s eta 0:00:24\n", "     ------------------------------- -------- 1.7/2.1 GB 20.3 MB/s eta 0:00:24\n", "     ------------------------------- -------- 1.7/2.1 GB 20.1 MB/s eta 0:00:24\n", "     ------------------------------- -------- 1.7/2.1 GB 20.4 MB/s eta 0:00:23\n", "     ------------------------------- -------- 1.7/2.1 GB 20.2 MB/s eta 0:00:23\n", "     ------------------------------- -------- 1.7/2.1 GB 20.9 MB/s eta 0:00:22\n", "     ------------------------------- -------- 1.7/2.1 GB 20.6 MB/s eta 0:00:22\n", "     ------------------------------- -------- 1.7/2.1 GB 20.4 MB/s eta 0:00:22\n", "     ------------------------------- -------- 1.7/2.1 GB 20.8 MB/s eta 0:00:22\n", "     ------------------------------- -------- 1.7/2.1 GB 20.6 MB/s eta 0:00:22\n", "     ------------------------------- -------- 1.7/2.1 GB 20.5 MB/s eta 0:00:21\n", "     -------------------------------- ------- 1.7/2.1 GB 20.2 MB/s eta 0:00:22\n", "     -------------------------------- ------- 1.7/2.1 GB 20.1 MB/s eta 0:00:21\n", "     -------------------------------- ------- 1.7/2.1 GB 20.6 MB/s eta 0:00:21\n", "     -------------------------------- ------- 1.7/2.1 GB 20.5 MB/s eta 0:00:21\n", "     -------------------------------- ------- 1.7/2.1 GB 20.4 MB/s eta 0:00:21\n", "     -------------------------------- ------- 1.7/2.1 GB 20.5 MB/s eta 0:00:20\n", "     -------------------------------- ------- 1.7/2.1 GB 20.5 MB/s eta 0:00:20\n", "     -------------------------------- ------- 1.7/2.1 GB 20.4 MB/s eta 0:00:20\n", "     -------------------------------- ------- 1.8/2.1 GB 20.7 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.5 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.4 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.4 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.4 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.4 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.4 MB/s eta 0:00:19\n", "     -------------------------------- ------- 1.8/2.1 GB 20.4 MB/s eta 0:00:19\n", "     --------------------------------- ------ 1.8/2.1 GB 18.9 MB/s eta 0:00:20\n", "     --------------------------------- ------ 1.8/2.1 GB 18.9 MB/s eta 0:00:20\n", "     --------------------------------- ------ 1.8/2.1 GB 19.0 MB/s eta 0:00:20\n", "     --------------------------------- ------ 1.8/2.1 GB 18.9 MB/s eta 0:00:19\n", "     --------------------------------- ------ 1.8/2.1 GB 19.5 MB/s eta 0:00:19\n", "     --------------------------------- ------ 1.8/2.1 GB 19.7 MB/s eta 0:00:18\n", "     --------------------------------- ------ 1.8/2.1 GB 19.5 MB/s eta 0:00:18\n", "     --------------------------------- ------ 1.8/2.1 GB 19.4 MB/s eta 0:00:18\n", "     --------------------------------- ------ 1.8/2.1 GB 19.4 MB/s eta 0:00:18\n", "     --------------------------------- ------ 1.8/2.1 GB 19.3 MB/s eta 0:00:18\n", "     --------------------------------- ------ 1.8/2.1 GB 19.3 MB/s eta 0:00:17\n", "     --------------------------------- ------ 1.8/2.1 GB 19.3 MB/s eta 0:00:17\n", "     ---------------------------------- ----- 1.8/2.1 GB 19.8 MB/s eta 0:00:17\n", "     ---------------------------------- ----- 1.8/2.1 GB 19.6 MB/s eta 0:00:16\n", "     ---------------------------------- ----- 1.8/2.1 GB 19.7 MB/s eta 0:00:16\n", "     ---------------------------------- ----- 1.8/2.1 GB 19.5 MB/s eta 0:00:16\n", "     ---------------------------------- ----- 1.8/2.1 GB 19.6 MB/s eta 0:00:16\n", "     ---------------------------------- ----- 1.8/2.1 GB 19.5 MB/s eta 0:00:16\n", "     ---------------------------------- ----- 1.9/2.1 GB 19.4 MB/s eta 0:00:15\n", "     ---------------------------------- ----- 1.9/2.1 GB 19.8 MB/s eta 0:00:15\n", "     ---------------------------------- ----- 1.9/2.1 GB 20.4 MB/s eta 0:00:14\n", "     ---------------------------------- ----- 1.9/2.1 GB 20.5 MB/s eta 0:00:14\n", "     ---------------------------------- ----- 1.9/2.1 GB 20.5 MB/s eta 0:00:14\n", "     ---------------------------------- ----- 1.9/2.1 GB 19.9 MB/s eta 0:00:14\n", "     ---------------------------------- ----- 1.9/2.1 GB 19.6 MB/s eta 0:00:14\n", "     ---------------------------------- ----- 1.9/2.1 GB 19.6 MB/s eta 0:00:14\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.8 MB/s eta 0:00:14\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.6 MB/s eta 0:00:14\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.4 MB/s eta 0:00:14\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.4 MB/s eta 0:00:14\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.4 MB/s eta 0:00:13\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.3 MB/s eta 0:00:13\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.3 MB/s eta 0:00:13\n", "     ----------------------------------- ---- 1.9/2.1 GB 18.7 MB/s eta 0:00:13\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.1 MB/s eta 0:00:13\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.1 MB/s eta 0:00:13\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.1 MB/s eta 0:00:12\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.1 MB/s eta 0:00:12\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.0 MB/s eta 0:00:12\n", "     ----------------------------------- ---- 1.9/2.1 GB 19.0 MB/s eta 0:00:12\n", "     ------------------------------------ --- 1.9/2.1 GB 18.8 MB/s eta 0:00:12\n", "     ------------------------------------ --- 1.9/2.1 GB 18.7 MB/s eta 0:00:12\n", "     ------------------------------------ --- 1.9/2.1 GB 18.9 MB/s eta 0:00:11\n", "     ------------------------------------ --- 1.9/2.1 GB 18.8 MB/s eta 0:00:11\n", "     ------------------------------------ --- 1.9/2.1 GB 18.8 MB/s eta 0:00:11\n", "     ------------------------------------ --- 1.9/2.1 GB 18.8 MB/s eta 0:00:11\n", "     ------------------------------------ --- 1.9/2.1 GB 18.8 MB/s eta 0:00:11\n", "     ------------------------------------ --- 2.0/2.1 GB 18.3 MB/s eta 0:00:11\n", "     ------------------------------------ --- 2.0/2.1 GB 18.3 MB/s eta 0:00:11\n", "     ------------------------------------ --- 2.0/2.1 GB 18.3 MB/s eta 0:00:10\n", "     ------------------------------------ --- 2.0/2.1 GB 18.2 MB/s eta 0:00:10\n", "     ------------------------------------ --- 2.0/2.1 GB 18.2 MB/s eta 0:00:10\n", "     ------------------------------------ --- 2.0/2.1 GB 18.2 MB/s eta 0:00:10\n", "     ------------------------------------ --- 2.0/2.1 GB 18.1 MB/s eta 0:00:10\n", "     ------------------------------------ --- 2.0/2.1 GB 18.1 MB/s eta 0:00:10\n", "     ------------------------------------- -- 2.0/2.1 GB 18.1 MB/s eta 0:00:09\n", "     ------------------------------------- -- 2.0/2.1 GB 18.1 MB/s eta 0:00:09\n", "     ------------------------------------- -- 2.0/2.1 GB 18.1 MB/s eta 0:00:09\n", "     ------------------------------------- -- 2.0/2.1 GB 18.2 MB/s eta 0:00:09\n", "     ------------------------------------- -- 2.0/2.1 GB 18.0 MB/s eta 0:00:09\n", "     ------------------------------------- -- 2.0/2.1 GB 18.0 MB/s eta 0:00:08\n", "     ------------------------------------- -- 2.0/2.1 GB 18.0 MB/s eta 0:00:08\n", "     ------------------------------------- -- 2.0/2.1 GB 18.0 MB/s eta 0:00:08\n", "     ------------------------------------- -- 2.0/2.1 GB 18.1 MB/s eta 0:00:07\n", "     ------------------------------------- -- 2.0/2.1 GB 19.5 MB/s eta 0:00:07\n", "     ------------------------------------- -- 2.0/2.1 GB 19.4 MB/s eta 0:00:06\n", "     ------------------------------------- -- 2.0/2.1 GB 19.4 MB/s eta 0:00:06\n", "     -------------------------------------- - 2.0/2.1 GB 19.3 MB/s eta 0:00:06\n", "     -------------------------------------- - 2.0/2.1 GB 19.3 MB/s eta 0:00:06\n", "     -------------------------------------- - 2.1/2.1 GB 19.5 MB/s eta 0:00:05\n", "     -------------------------------------- - 2.1/2.1 GB 19.4 MB/s eta 0:00:05\n", "     -------------------------------------- - 2.1/2.1 GB 19.4 MB/s eta 0:00:05\n", "     -------------------------------------- - 2.1/2.1 GB 19.4 MB/s eta 0:00:05\n", "     -------------------------------------- - 2.1/2.1 GB 19.5 MB/s eta 0:00:04\n", "     -------------------------------------- - 2.1/2.1 GB 19.6 MB/s eta 0:00:04\n", "     -------------------------------------- - 2.1/2.1 GB 19.5 MB/s eta 0:00:04\n", "     -------------------------------------- - 2.1/2.1 GB 19.6 MB/s eta 0:00:04\n", "     ---------------------------------------  2.1/2.1 GB 19.6 MB/s eta 0:00:03\n", "     ---------------------------------------  2.1/2.1 GB 19.7 MB/s eta 0:00:03\n", "     ---------------------------------------  2.1/2.1 GB 19.7 MB/s eta 0:00:03\n", "     ---------------------------------------  2.1/2.1 GB 19.6 MB/s eta 0:00:03\n", "     ---------------------------------------  2.1/2.1 GB 19.5 MB/s eta 0:00:02\n", "     ---------------------------------------  2.1/2.1 GB 19.6 MB/s eta 0:00:02\n", "     ---------------------------------------  2.1/2.1 GB 19.6 MB/s eta 0:00:02\n", "     ---------------------------------------  2.1/2.1 GB 19.6 MB/s eta 0:00:02\n", "     ---------------------------------------  2.1/2.1 GB 19.6 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.7 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------  2.1/2.1 GB 20.4 MB/s eta 0:00:01\n", "     ---------------------------------------- 2.1/2.1 GB ? eta 0:00:00\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from torchvision) (10.3.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from requests->torchvision) (3.1.0)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from requests->torchvision) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from requests->torchvision) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from requests->torchvision) (2022.12.7)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from jinja2->torch) (2.1.1)\n", "Requirement already satisfied: mpmath>=0.19 in c:\\users\\<USER>\\.conda\\envs\\myenvnlp\\lib\\site-packages (from sympy->torch) (1.3.0)\n", "Installing collected packages: torch, torchvision, torchaudio\n", "  Attempting uninstall: torch\n", "    Found existing installation: torch 2.0.0\n", "    Uninstalling torch-2.0.0:\n", "      Successfully uninstalled torch-2.0.0\n", "Successfully installed torch-1.12.1+cu113 torchaudio-0.12.1+cu113 torchvision-0.13.1+cu113\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mFailed to interrupt the Kernel. \n", "\u001b[1;31mUnable to start Kernel 'myenvnlp (Python 3.9.16)' due to a timeout waiting for the ports to get used. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# For PyTorch 1.10 and CUDA 11.3\n", "%pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu113\n", "%pip install git+https://github.com/facebookresearch/detectron2.git\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python --version"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'detectron2'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mlp\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mdetectron2\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01men<PERSON>e\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m DefaultPredictor\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(lp\u001b[38;5;241m.\u001b[39mDetectron2LayoutModel)\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'detectron2'"]}], "source": ["import layoutparser as lp\n", "from detectron2.engine import DefaultPredictor\n", "\n", "print(lp.Detectron2LayoutModel)  # Should not raise an AttributeError\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pip in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (23.1.2)"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "ERROR: Exception:\n", "Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\cli\\base_command.py\", line 169, in exc_logging_wrapper\n", "    status = run_func(*args)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\cli\\req_command.py\", line 248, in wrapper\n", "    return func(self, options, args)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\commands\\install.py\", line 449, in run\n", "    installed = install_given_reqs(\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\req\\__init__.py\", line 72, in install_given_reqs\n", "    requirement.install(\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\req\\req_install.py\", line 800, in install\n", "    install_wheel(\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\operations\\install\\wheel.py\", line 731, in install_wheel\n", "    _install_wheel(\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\operations\\install\\wheel.py\", line 648, in _install_wheel\n", "    generated_console_scripts = maker.make_multiple(scripts_to_generate)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_vendor\\distlib\\scripts.py\", line 436, in make_multiple\n", "    filenames.extend(self.make(specification, options))\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_internal\\operations\\install\\wheel.py\", line 429, in make\n", "    return super().make(specification, options)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_vendor\\distlib\\scripts.py\", line 425, in make\n", "    self._make_script(entry, filenames, options=options)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_vendor\\distlib\\scripts.py\", line 325, in _make_script\n", "    self._write_script(scriptnames, shebang, script, filenames, ext)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_vendor\\distlib\\scripts.py\", line 249, in _write_script\n", "    launcher = self._get_launcher('t')\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\pip\\_vendor\\distlib\\scripts.py\", line 404, in _get_launcher\n", "    raise ValueError(msg)\n", "ValueError: Unable to find resource t64.exe in package pip._vendor.distlib\n", "\n", "[notice] A new release of pip is available: 23.1.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Collecting pip\n", "  Using cached pip-25.0.1-py3-none-any.whl (1.8 MB)\n", "Installing collected packages: pip\n", "  Attempting uninstall: pip\n", "    Found existing installation: pip 23.1.2\n", "    Uninstalling pip-23.1.2:\n", "      Successfully uninstalled pip-23.1.2\n", "  Rolling back uninstall of pip\n", "  Moving to c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\scripts\\pip.exe\n", "   from C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-uninstall-54n07v9c\\pip.exe\n", "  Moving to c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\scripts\\pip3.10.exe\n", "   from C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-uninstall-54n07v9c\\pip3.10.exe\n", "  Moving to c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\scripts\\pip3.exe\n", "   from C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-uninstall-54n07v9c\\pip3.exe\n", "  Moving to c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages\\pip-23.1.2.dist-info\\\n", "   from C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\~ip-23.1.2.dist-info\n", "  Moving to c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages\\pip\\\n", "   from C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\~ip\n"]}], "source": ["!python.exe -m pip install --upgrade pip"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hi\n"]}], "source": ["print(\"Hi\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python 3.10.15\n"]}], "source": ["!python --version"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: spacy in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (3.4.4)Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "\n", "[notice] A new release of pip is available: 23.1.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.10 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.0.12)\n", "Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.0.4)\n", "Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.0.9)\n", "Requirement already satisfied: cymem<2.1.0,>=2.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.0.7)\n", "Requirement already satisfied: preshed<3.1.0,>=3.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.0.8)\n", "Requirement already satisfied: thinc<8.2.0,>=8.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (8.1.9)\n", "Requirement already satisfied: wasabi<1.1.0,>=0.9.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.10.1)\n", "Requirement already satisfied: srsly<3.0.0,>=2.4.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.4.6)\n", "Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.0.8)\n", "Requirement already satisfied: typer<0.8.0,>=0.3.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.7.0)\n", "Requirement already satisfied: pathy>=0.3.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.10.1)\n", "Requirement already satisfied: smart-open<7.0.0,>=5.2.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (6.4.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (4.67.1)\n", "Requirement already satisfied: numpy>=1.15.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (2.2.2)\n", "Requirement already satisfied: requests<3.0.0,>=2.13.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (2.32.3)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,<1.11.0,>=1.7.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.10.7)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (3.1.5)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (67.7.2)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (24.2)\n", "Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.3.0)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<1.11.0,>=1.7.4->spacy) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2025.1.31)\n", "Requirement already satisfied: blis<0.8.0,>=0.7.8 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from thinc<8.2.0,>=8.1.0->spacy) (0.7.9)\n", "Requirement already satisfied: confection<1.0.0,>=0.0.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from thinc<8.2.0,>=8.1.0->spacy) (0.0.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from tqdm<5.0.0,>=4.38.0->spacy) (0.4.6)\n", "Requirement already satisfied: click<9.0.0,>=7.1.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from typer<0.8.0,>=0.3.0->spacy) (8.1.8)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from jinja2->spacy) (3.0.2)\n", "Collecting spacy-layoutNote: you may need to restart the kernel to use updated packages.\n", "\n", "  Using cached spacy_layout-0.0.11-py2.py3-none-any.whl (10 kB)\n", "Collecting spacy>=3.7.5 (from spacy-layout)\n", "  Using cached spacy-3.8.4-cp310-cp310-win_amd64.whl (12.2 MB)\n", "Collecting docling>=2.5.2 (from spacy-layout)\n", "  Using cached docling-2.21.0-py3-none-any.whl (132 kB)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy-layout) (2.2.3)\n", "Requirement already satisfied: srsly in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy-layout) (2.4.6)\n", "Collecting beautifulsoup4<4.13.0,>=4.12.3 (from docling>=2.5.2->spacy-layout)\n", "  Using cached beautifulsoup4-4.12.3-py3-none-any.whl (147 kB)\n", "Requirement already satisfied: certifi>=2024.7.4 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (2025.1.31)\n", "Collecting deepsearch-glm<2.0.0,>=1.0.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached deepsearch_glm-1.0.0-cp310-cp310-win_amd64.whl (7.9 MB)\n", "Collecting docling-core[chunking]<3.0.0,>=2.18.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached docling_core-2.18.0-py3-none-any.whl (95 kB)\n", "Collecting docling-ibm-models<4.0.0,>=3.3.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached docling_ibm_models-3.3.1-py3-none-any.whl (76 kB)\n", "Collecting docling-parse<4.0.0,>=3.3.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached docling_parse-3.3.0-cp310-cp310-win_amd64.whl (23.2 MB)\n", "Requirement already satisfied: easyocr<2.0,>=1.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from docling>=2.5.2->spacy-layout) (1.7.1)\n", "Requirement already satisfied: filetype<2.0.0,>=1.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from docling>=2.5.2->spacy-layout) (1.2.0)\n", "Requirement already satisfied: huggingface_hub<1,>=0.23 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (0.28.1)\n", "Collecting lxml<6.0.0,>=4.0.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached lxml-5.3.1-cp310-cp310-win_amd64.whl (3.8 MB)\n", "Collecting marko<3.0.0,>=2.1.2 (from docling>=2.5.2->spacy-layout)\n", "  Using cached marko-2.1.2-py3-none-any.whl (42 kB)\n", "Collecting openpyxl<4.0.0,>=3.1.5 (from docling>=2.5.2->spacy-layout)\n", "  Using cached openpyxl-3.1.5-py2.py3-none-any.whl (250 kB)\n", "Requirement already satisfied: pillow<11.0.0,>=10.0.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (10.4.0)\n", "Collecting pydantic<3.0.0,>=2.0.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached pydantic-2.10.6-py3-none-any.whl (431 kB)\n", "Collecting pydantic-settings<3.0.0,>=2.3.0 (from docling>=2.5.2->spacy-layout)\n", "  Using cached pydantic_settings-2.7.1-py3-none-any.whl (29 kB)\n", "Requirement already satisfied: pypdfium2<5.0.0,>=4.30.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (4.30.1)\n", "Collecting python-docx<2.0.0,>=1.1.2 (from docling>=2.5.2->spacy-layout)\n", "  Using cached python_docx-1.1.2-py3-none-any.whl (244 kB)\n", "Collecting python-pptx<2.0.0,>=1.0.2 (from docling>=2.5.2->spacy-layout)\n", "  Using cached python_pptx-1.0.2-py3-none-any.whl (472 kB)\n", "Requirement already satisfied: requests<3.0.0,>=2.32.2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (2.32.3)\n", "Requirement already satisfied: rtree<2.0.0,>=1.3.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (1.3.0)\n", "Requirement already satisfied: scipy<2.0.0,>=1.6.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (1.15.1)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.65.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling>=2.5.2->spacy-layout) (4.67.1)\n", "Collecting typer<0.13.0,>=0.12.5 (from docling>=2.5.2->spacy-layout)\n", "  Using cached typer-0.12.5-py3-none-any.whl (47 kB)\n", "Requirement already satisfied: numpy>=1.22.4 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pandas->spacy-layout) (2.2.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pandas->spacy-layout) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pandas->spacy-layout) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from pandas->spacy-layout) (2023.3)\n", "Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.11 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (3.0.12)\n", "Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (1.0.4)\n", "Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (1.0.9)\n", "Requirement already satisfied: cymem<2.1.0,>=2.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (2.0.7)\n", "Requirement already satisfied: preshed<3.1.0,>=3.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (3.0.8)\n", "Collecting thinc<8.4.0,>=8.3.4 (from spacy>=3.7.5->spacy-layout)\n", "  Using cached thinc-8.3.4-cp310-cp310-win_amd64.whl (1.5 MB)\n", "Requirement already satisfied: wasabi<1.2.0,>=0.9.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (0.10.1)\n", "Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (2.0.8)\n", "Collecting weasel<0.5.0,>=0.1.0 (from spacy>=3.7.5->spacy-layout)\n", "  Using cached weasel-0.4.1-py3-none-any.whl (50 kB)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy>=3.7.5->spacy-layout) (3.1.5)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (67.7.2)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy>=3.7.5->spacy-layout) (24.2)\n", "Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy>=3.7.5->spacy-layout) (3.3.0)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from beautifulsoup4<4.13.0,>=4.12.3->docling>=2.5.2->spacy-layout) (2.6)\n", "Requirement already satisfied: pywin32<308,>=307 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from deepsearch-glm<2.0.0,>=1.0.0->docling>=2.5.2->spacy-layout) (307)\n", "Collecting jsonref<2.0.0,>=1.1.0 (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached jsonref-1.1.0-py3-none-any.whl (9.4 kB)\n", "Collecting jsonschema<5.0.0,>=4.16.0 (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached jsonschema-4.23.0-py3-none-any.whl (88 kB)\n", "Collecting latex2mathml<4.0.0,>=3.77.0 (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached latex2mathml-3.77.0-py3-none-any.whl (73 kB)\n", "Requirement already satisfied: pyyaml<7.0.0,>=5.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (6.0.2)\n", "Requirement already satisfied: tabulate<0.10.0,>=0.9.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (0.9.0)\n", "Requirement already satisfied: typing-extensions<5.0.0,>=4.12.2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (4.12.2)\n", "Collecting semchunk<3.0.0,>=2.2.0 (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached semchunk-2.2.2-py3-none-any.whl (10 kB)\n", "Requirement already satisfied: transformers<5.0.0,>=4.34.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (4.48.2)\n", "Collecting jsonlines<4.0.0,>=3.1.0 (from docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout)\n", "  Using cached jsonlines-3.1.0-py3-none-any.whl (8.6 kB)\n", "Requirement already satisfied: opencv-python-headless<5.0.0.0,>=******** in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (********)\n", "Requirement already satisfied: safetensors[torch]<1,>=0.4.3 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (0.5.2)\n", "Requirement already satisfied: torch<3.0.0,>=2.2.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (2.3.0)\n", "Requirement already satisfied: torchvision<1,>=0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (0.18.0)\n", "Collecting scikit-image (from easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout)\n", "  Using cached scikit_image-0.25.1-cp310-cp310-win_amd64.whl (12.8 MB)\n", "Requirement already satisfied: python-bidi in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout) (0.4.2)\n", "Requirement already satisfied: S<PERSON><PERSON>y in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout) (2.0.1)\n", "Requirement already satisfied: pyclipper in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout) (1.3.0.post4)\n", "Requirement already satisfied: ninja in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout) (********)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from huggingface_hub<1,>=0.23->docling>=2.5.2->spacy-layout) (3.17.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from huggingface_hub<1,>=0.23->docling>=2.5.2->spacy-layout) (2024.9.0)\n", "Collecting et-xmlfile (from openpyxl<4.0.0,>=3.1.5->docling>=2.5.2->spacy-layout)\n", "  Using cached et_xmlfile-2.0.0-py3-none-any.whl (18 kB)\n", "Collecting annotated-types>=0.6.0 (from pydantic<3.0.0,>=2.0.0->docling>=2.5.2->spacy-layout)\n", "  Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Collecting pydantic-core==2.27.2 (from pydantic<3.0.0,>=2.0.0->docling>=2.5.2->spacy-layout)\n", "  Using cached pydantic_core-2.27.2-cp310-cp310-win_amd64.whl (2.0 MB)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pydantic-settings<3.0.0,>=2.3.0->docling>=2.5.2->spacy-layout) (1.0.1)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from python-dateutil>=2.8.2->pandas->spacy-layout) (1.17.0)\n", "Requirement already satisfied: XlsxWriter>=0.5.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from python-pptx<2.0.0,>=1.0.2->docling>=2.5.2->spacy-layout) (3.1.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.32.2->docling>=2.5.2->spacy-layout) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.32.2->docling>=2.5.2->spacy-layout) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.32.2->docling>=2.5.2->spacy-layout) (2.3.0)\n", "Collecting blis<1.3.0,>=1.2.0 (from thinc<8.4.0,>=8.3.4->spacy>=3.7.5->spacy-layout)\n", "  Using cached blis-1.2.0-cp310-cp310-win_amd64.whl (6.2 MB)\n", "Requirement already satisfied: confection<1.0.0,>=0.0.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from thinc<8.4.0,>=8.3.4->spacy>=3.7.5->spacy-layout) (0.0.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from tqdm<5.0.0,>=4.65.0->docling>=2.5.2->spacy-layout) (0.4.6)\n", "Requirement already satisfied: click>=8.0.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from typer<0.13.0,>=0.12.5->docling>=2.5.2->spacy-layout) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from typer<0.13.0,>=0.12.5->docling>=2.5.2->spacy-layout) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from typer<0.13.0,>=0.12.5->docling>=2.5.2->spacy-layout) (13.3.5)\n", "Collecting cloudpathlib<1.0.0,>=0.7.0 (from weasel<0.5.0,>=0.1.0->spacy>=3.7.5->spacy-layout)\n", "  Using cached cloudpathlib-0.20.0-py3-none-any.whl (52 kB)\n", "Requirement already satisfied: smart-open<8.0.0,>=5.2.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from weasel<0.5.0,>=0.1.0->spacy>=3.7.5->spacy-layout) (6.4.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from jinja2->spacy>=3.7.5->spacy-layout) (3.0.2)\n", "INFO: pip is looking at multiple versions of confection to determine which version is compatible with other requirements. This could take a while.\n", "Collecting confection<1.0.0,>=0.0.1 (from thinc<8.4.0,>=8.3.4->spacy>=3.7.5->spacy-layout)\n", "  Using cached confection-0.1.5-py3-none-any.whl (35 kB)\n", "Requirement already satisfied: attrs>=19.2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from jsonlines<4.0.0,>=3.1.0->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (25.1.0)\n", "Collecting jsonschema-specifications>=2023.03.6 (from jsonschema<5.0.0,>=4.16.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached jsonschema_specifications-2024.10.1-py3-none-any.whl (18 kB)\n", "Collecting referencing>=0.28.4 (from jsonschema<5.0.0,>=4.16.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached referencing-0.36.2-py3-none-any.whl (26 kB)\n", "Requirement already satisfied: rpds-py>=0.7.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (0.22.3)\n", "Requirement already satisfied: markdown-it-py<3.0.0,>=2.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from rich>=10.11.0->typer<0.13.0,>=0.12.5->docling>=2.5.2->spacy-layout) (2.2.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from rich>=10.11.0->typer<0.13.0,>=0.12.5->docling>=2.5.2->spacy-layout) (2.15.1)\n", "Collecting mpire[dill] (from semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout)\n", "  Using cached mpire-2.10.2-py3-none-any.whl (272 kB)\n", "Requirement already satisfied: sympy in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from torch<3.0.0,>=2.2.2->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (1.13.3)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from torch<3.0.0,>=2.2.2->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (3.4.2)\n", "Requirement already satisfied: mkl<=2021.4.0,>=2021.1.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from torch<3.0.0,>=2.2.2->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (2021.4.0)\n", "Requirement already satisfied: regex!=2019.12.17 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from transformers<5.0.0,>=4.34.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (2024.11.6)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from transformers<5.0.0,>=4.34.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (0.21.0)\n", "Collecting imageio!=2.35.0,>=2.33 (from scikit-image->easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout)\n", "  Using cached imageio-2.37.0-py3-none-any.whl (315 kB)\n", "Requirement already satisfied: tifffile>=2022.8.12 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from scikit-image->easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout) (2025.1.10)\n", "Collecting lazy-loader>=0.4 (from scikit-image->easyocr<2.0,>=1.7->docling>=2.5.2->spacy-layout)\n", "  Using cached lazy_loader-0.4-py3-none-any.whl (12 kB)\n", "Requirement already satisfied: mdurl~=0.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from markdown-it-py<3.0.0,>=2.2.0->rich>=10.11.0->typer<0.13.0,>=0.12.5->docling>=2.5.2->spacy-layout) (0.1.2)\n", "Requirement already satisfied: intel-openmp==2021.* in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from mkl<=2021.4.0,>=2021.1.1->torch<3.0.0,>=2.2.2->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (2021.4.0)\n", "Requirement already satisfied: tbb==2021.* in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from mkl<=2021.4.0,>=2021.1.1->torch<3.0.0,>=2.2.2->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (2021.12.0)\n", "Requirement already satisfied: multiprocess in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from mpire[dill]->semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (0.70.16)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from sympy->torch<3.0.0,>=2.2.2->docling-ibm-models<4.0.0,>=3.3.0->docling>=2.5.2->spacy-layout) (1.3.0)\n", "Requirement already satisfied: dill>=0.3.8 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from multiprocess->mpire[dill]->semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.18.0->docling>=2.5.2->spacy-layout) (0.3.8)\n", "Installing collected packages: referencing, pydantic-core, marko, lxml, lazy-loader, latex2mathml, jsonref, jsonlines, imageio, et-xmlfile, deepsearch-glm, cloudpathlib, blis, beautifulsoup4, annotated-types, scikit-image, python-pptx, python-docx, pydantic, openpyxl, mpire, jsonschema-specifications, typer, pydantic-settings, jsonschema, confection, weasel, thinc, semchunk, docling-core, spacy, docling-parse, docling-ibm-models, docling, spacy-layout\n", "  Attempting uninstall: blis\n", "    Found existing installation: blis 0.7.9\n", "    Uninstalling blis-0.7.9:\n", "      Successfully uninstalled blis-0.7.9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "  WARNING: The script marko.exe is installed in 'c:\\Users\\<USER>\\.conda\\envs\\casenv\\Scripts' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\n", "  WARNING: The scripts l2m.exe and latex2mathml.exe are installed in 'c:\\Users\\<USER>\\.conda\\envs\\casenv\\Scripts' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\n", "  WARNING: The scripts imageio_download_bin.exe and imageio_remove_bin.exe are installed in 'c:\\Users\\<USER>\\.conda\\envs\\casenv\\Scripts' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\n", "ERROR: Could not install packages due to an OSError: [WinError 5] Access is denied: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python310\\\\site-packages\\\\~lis\\\\cy.cp310-win_amd64.pyd'\n", "Consider using the `--user` option or check the permissions.\n", "\n", "\n", "[notice] A new release of pip is available: 23.1.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\compat.py:36: UserWarning: 'has_mps' is deprecated, please use 'torch.backends.mps.is_built()'\n", "  hasattr(torch, \"has_mps\")\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\compat.py:37: UserWarning: 'has_mps' is deprecated, please use 'torch.backends.mps.is_built()'\n", "  and torch.has_mps  # type: ignore[attr-defined]\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\.conda\\envs\\casenv\\lib\\runpy.py\", line 187, in _run_module_as_main\n", "    mod_name, mod_spec, code = _get_module_details(mod_name, _Error)\n", "  File \"c:\\Users\\<USER>\\.conda\\envs\\casenv\\lib\\runpy.py\", line 146, in _get_module_details\n", "    return _get_module_details(pkg_main_name, error)\n", "  File \"c:\\Users\\<USER>\\.conda\\envs\\casenv\\lib\\runpy.py\", line 110, in _get_module_details\n", "    __import__(pkg_name)\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\__init__.py\", line 6, in <module>\n", "    from .errors import setup_default_warnings\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\errors.py\", line 2, in <module>\n", "    from .compat import Literal\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\compat.py\", line 38, in <module>\n", "    from thinc.api import Optimizer  # noqa: F401\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\api.py\", line 2, in <module>\n", "    from .initializers import normal_init, uniform_init, glorot_uniform_init, zero_init\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\initializers.py\", line 4, in <module>\n", "    from .backends import Ops\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\__init__.py\", line 8, in <module>\n", "    from .cupy_ops import CupyOps\n", "  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\cupy_ops.py\", line 4, in <module>\n", "    from .numpy_ops import NumpyOps\n", "  File \"thinc\\backends\\numpy_ops.pyx\", line 1, in init thinc.backends.numpy_ops\n", "ValueError: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject\n"]}], "source": ["%pip install spacy \n", "%pip install spacy-layout\n", "!python -m spacy download en_core_web_sm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: spacy in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (3.4.4)Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "\n", "[notice] A new release of pip is available: 23.1.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.10 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.0.12)\n", "Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.0.4)\n", "Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.0.9)\n", "Requirement already satisfied: cymem<2.1.0,>=2.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.0.7)\n", "Requirement already satisfied: preshed<3.1.0,>=3.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.0.8)\n", "Requirement already satisfied: thinc<8.2.0,>=8.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (8.1.9)\n", "Requirement already satisfied: wasabi<1.1.0,>=0.9.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.10.1)\n", "Requirement already satisfied: srsly<3.0.0,>=2.4.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.4.6)\n", "Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.0.8)\n", "Requirement already satisfied: typer<0.8.0,>=0.3.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.7.0)\n", "Requirement already satisfied: pathy>=0.3.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.10.1)\n", "Requirement already satisfied: smart-open<7.0.0,>=5.2.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (6.4.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (4.67.1)\n", "Requirement already satisfied: numpy>=1.15.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (2.2.2)\n", "Requirement already satisfied: requests<3.0.0,>=2.13.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (2.32.3)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,<1.11.0,>=1.7.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.10.7)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (3.1.5)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (67.7.2)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (24.2)\n", "Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.3.0)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<1.11.0,>=1.7.4->spacy) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2025.1.31)\n", "Requirement already satisfied: blis<0.8.0,>=0.7.8 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from thinc<8.2.0,>=8.1.0->spacy) (0.7.9)\n", "Requirement already satisfied: confection<1.0.0,>=0.0.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from thinc<8.2.0,>=8.1.0->spacy) (0.0.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from tqdm<5.0.0,>=4.38.0->spacy) (0.4.6)\n", "Requirement already satisfied: click<9.0.0,>=7.1.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from typer<0.8.0,>=0.3.0->spacy) (8.1.8)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from jinja2->spacy) (3.0.2)\n"]}], "source": ["# %pip install spacy "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: spacy in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (3.4.4)Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "    WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "\n", "[notice] A new release of pip is available: 23.1.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.10 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.0.12)\n", "Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.0.4)\n", "Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.0.9)\n", "Requirement already satisfied: cymem<2.1.0,>=2.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.0.7)\n", "Requirement already satisfied: preshed<3.1.0,>=3.0.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.0.8)\n", "Requirement already satisfied: thinc<8.2.0,>=8.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (8.1.9)\n", "Requirement already satisfied: wasabi<1.1.0,>=0.9.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.10.1)\n", "Requirement already satisfied: srsly<3.0.0,>=2.4.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.4.6)\n", "Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (2.0.8)\n", "Requirement already satisfied: typer<0.8.0,>=0.3.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.7.0)\n", "Requirement already satisfied: pathy>=0.3.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (0.10.1)\n", "Requirement already satisfied: smart-open<7.0.0,>=5.2.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (6.4.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (4.67.1)\n", "Requirement already satisfied: numpy>=1.15.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (2.2.2)\n", "Requirement already satisfied: requests<3.0.0,>=2.13.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (2.32.3)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,<1.11.0,>=1.7.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (1.10.7)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (3.1.5)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (67.7.2)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from spacy) (24.2)\n", "Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from spacy) (3.3.0)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<1.11.0,>=1.7.4->spacy) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2025.1.31)\n", "Collecting blis<0.8.0,>=0.7.8 (from thinc<8.2.0,>=8.1.0->spacy)\n", "  Downloading blis-0.7.11-cp310-cp310-win_amd64.whl (6.6 MB)\n", "                                              0.0/6.6 MB ? eta -:--:--\n", "                                              0.0/6.6 MB ? eta -:--:--\n", "                                              0.0/6.6 MB 165.2 kB/s eta 0:00:40\n", "                                              0.0/6.6 MB 165.2 kB/s eta 0:00:40\n", "                                              0.0/6.6 MB 196.9 kB/s eta 0:00:34\n", "                                              0.1/6.6 MB 252.2 kB/s eta 0:00:27\n", "     -                                        0.2/6.6 MB 696.3 kB/s eta 0:00:10\n", "     ----                                     0.7/6.6 MB 2.1 MB/s eta 0:00:03\n", "     ------                                   1.2/6.6 MB 3.2 MB/s eta 0:00:02\n", "     ---------                                1.6/6.6 MB 4.0 MB/s eta 0:00:02\n", "     ------------                             2.1/6.6 MB 4.6 MB/s eta 0:00:01\n", "     ---------------                          2.6/6.6 MB 5.1 MB/s eta 0:00:01\n", "     ------------------                       3.0/6.6 MB 5.4 MB/s eta 0:00:01\n", "     ---------------------                    3.5/6.6 MB 6.1 MB/s eta 0:00:01\n", "     --------------------------               4.4/6.6 MB 6.8 MB/s eta 0:00:01\n", "     ------------------------------           5.1/6.6 MB 7.6 MB/s eta 0:00:01\n", "     ------------------------------------     6.1/6.6 MB 8.3 MB/s eta 0:00:01\n", "     ---------------------------------------  6.6/6.6 MB 8.8 MB/s eta 0:00:01\n", "     ---------------------------------------  6.6/6.6 MB 8.8 MB/s eta 0:00:01\n", "     ---------------------------------------  6.6/6.6 MB 8.8 MB/s eta 0:00:01\n", "     ---------------------------------------  6.6/6.6 MB 8.8 MB/s eta 0:00:01\n", "     ---------------------------------------  6.6/6.6 MB 8.8 MB/s eta 0:00:01\n", "     ---------------------------------------- 6.6/6.6 MB 6.6 MB/s eta 0:00:00\n", "Requirement already satisfied: confection<1.0.0,>=0.0.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from thinc<8.2.0,>=8.1.0->spacy) (0.0.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from tqdm<5.0.0,>=4.38.0->spacy) (0.4.6)\n", "Requirement already satisfied: click<9.0.0,>=7.1.1 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from typer<0.8.0,>=0.3.0->spacy) (8.1.8)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\.conda\\envs\\casenv\\lib\\site-packages (from jinja2->spacy) (3.0.2)\n", "Installing collected packages: blis\n", "  Attempting uninstall: blis\n", "    Found existing installation: blis 1.2.0\n", "    Uninstalling blis-1.2.0:\n", "      Successfully uninstalled blis-1.2.0\n", "Successfully installed blis-0.7.11\n"]}], "source": ["%pip install spacy\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Package                      VersionNote: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -illow (c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages)\n", "\n", "[notice] A new release of pip is available: 23.1.2 -> 25.0.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "---------------------------- -----------\n", "absl-py                      1.4.0\n", "accelerate                   1.3.0\n", "aiofiles                     23.1.0\n", "aiohttp                      3.8.4\n", "aiosignal                    1.3.1\n", "altair                       4.2.2\n", "argcomplete                  1.10.3\n", "astor                        0.8.1\n", "asttokens                    3.0.0\n", "astunparse                   1.6.3\n", "async-timeout                4.0.2\n", "attrdict                     2.0.1\n", "attrs                        25.1.0\n", "Babel                        2.12.1\n", "bce-python-sdk               0.8.83\n", "beautifulsoup4               4.9.3\n", "blis                         0.7.11\n", "Brotli                       1.0.9\n", "bs4                          0.0.1\n", "cachetools                   5.3.0\n", "Camelot                      12.6.29\n", "camelot-py                   0.9.0\n", "catalogue                    2.0.8\n", "certifi                      2025.1.31\n", "chardet                      3.0.4\n", "charset-normalizer           3.4.1\n", "click                        8.1.8\n", "cloudpathlib                 0.20.0\n", "colorama                     0.4.6\n", "comm                         0.2.2\n", "compressed-rtf               1.0.6\n", "comtypes                     1.4.2\n", "confection                   0.0.4\n", "cssutils                     2.6.0\n", "cymem                        2.0.7\n", "Cython                       0.29.34\n", "datasets                     3.2.0\n", "debugpy                      1.8.12\n", "decorator                    5.1.1\n", "deepsearch-glm               1.0.0\n", "dill                         0.3.8\n", "distro                       1.8.0\n", "docopt                       0.6.2\n", "docx2txt                     0.8\n", "easyocr                      1.7.1\n", "ebcdic                       1.1.1\n", "Elixir                       0.7.1\n", "en-core-web-lg               3.4.1\n", "en-core-web-sm               3.4.1\n", "et_xmlfile                   2.0.0\n", "exceptiongroup               1.2.2\n", "executing                    2.1.0\n", "extract-msg                  0.28.7\n", "fastapi                      0.95.1\n", "feedfinder2                  0.0.4\n", "feedparser                   6.0.10\n", "ffmpy                        0.3.0\n", "filelock                     3.17.0\n", "filetype                     1.2.0\n", "fire                         0.5.0\n", "flask-babel                  3.1.0\n", "flatbuffers                  23.3.3\n", "frozenlist                   1.3.3\n", "fsspec                       2024.9.0\n", "gast                         0.4.0\n", "gevent                       22.10.2\n", "geventhttpclient             2.0.2\n", "ghostscript                  0.7\n", "google-auth                  2.17.3\n", "google-auth-oauthlib         1.0.0\n", "google-pasta                 0.2.0\n", "gradio                       3.27.0\n", "gradio_client                0.1.3\n", "grpcio                       1.53.0\n", "h11                          0.14.0\n", "html5lib                     1.1\n", "httpcore                     0.17.0\n", "httpx                        0.24.0\n", "huggingface-hub              0.28.1\n", "idna                         3.10\n", "imageio                      2.37.0\n", "IMAPClient                   2.1.0\n", "img2table                    0.0.24\n", "imgaug                       0.4.0\n", "importlib_metadata           8.6.1\n", "inflect                      6.0.4\n", "intel-openmp                 2021.4.0\n", "iopath                       0.1.10\n", "ipykernel                    6.29.5\n", "ipython                      8.31.0\n", "jax                          0.4.8\n", "jedi                         0.19.2\n", "jieba3k                      0.35.1\n", "Jinja2                       3.1.5\n", "joblib                       1.4.2\n", "jsonlines                    3.1.0\n", "jsonref                      1.1.0\n", "jupyter_client               8.6.3\n", "jupyter_core                 5.7.2\n", "keras                        2.12.0\n", "langcodes                    3.3.0\n", "latex2mathml                 3.77.0\n", "layoutparser                 0.0.0\n", "lazy_loader                  0.4\n", "libclang                     16.0.0\n", "linkify-it-py                2.0.0\n", "lmdb                         1.4.1\n", "locationtagger               0.0.1\n", "lxml                         5.3.1\n", "markdown-it-py               2.2.0\n", "marko                        2.1.2\n", "MarkupSafe                   3.0.2\n", "matplotlib-inline            0.1.7\n", "mdit-py-plugins              0.3.3\n", "mdurl                        0.1.2\n", "mkl                          2021.4.0\n", "ml-dtypes                    0.1.0\n", "mpmath                       1.3.0\n", "multidict                    6.0.4\n", "multiprocess                 0.70.16\n", "murmurhash                   1.0.9\n", "nest_asyncio                 1.6.0\n", "networkx                     3.4.2\n", "newspaper3k                  0.2.8\n", "ninja                        ********\n", "num2words                    0.5.12\n", "numpy                        2.2.2\n", "oauthlib                     3.2.2\n", "olefile                      0.46\n", "onnx                         1.13.1\n", "opencv-contrib-python        ********\n", "opencv-python                ********\n", "opencv-python-headless       ********\n", "opt-einsum                   3.3.0\n", "<PERSON><PERSON><PERSON>                       3.8.10\n", "packaging                    24.2\n", "paddle-bfloat                0.1.7\n", "paddleocr                    *******\n", "paddlepaddle                 2.4.2\n", "paddlepaddle-gpu             2.4.2\n", "pandas                       2.2.3\n", "parso                        0.8.4\n", "pathy                        0.10.1\n", "pbr                          5.11.1\n", "pdf2docx                     0.5.6\n", "pdf2image                    1.16.3\n", "pdfannot                     2019.6.5.1\n", "pdfannots                    0.4\n", "pdfminer                     20191125\n", "pdfplumber                   0.8.1\n", "pickleshare                  0.7.5\n", "pillow                       10.4.0\n", "pip                          23.1.2\n", "pipe                         2.0\n", "platformdirs                 4.3.6\n", "polars                       0.17.5\n", "poppler-utils                0.1.0\n", "portalocker                  2.7.0\n", "premailer                    3.10.0\n", "preshed                      3.0.8\n", "prompt_toolkit               3.0.50\n", "psutil                       6.1.1\n", "pure_eval                    0.2.3\n", "pyarrow                      19.0.0\n", "pyclipper                    1.3.0.post4\n", "pycountry                    22.3.5\n", "pycryptodome                 3.17\n", "pydantic                     1.10.7\n", "pydantic_core                2.27.2\n", "pydub                        0.25.1\n", "Pygments                     2.15.1\n", "PyMuPDF                      1.20.2\n", "pypdf                        3.9.0\n", "PyPDF2                       1.26.0\n", "pypdfium2                    4.30.1\n", "pytesseract                  0.3.10\n", "python-bidi                  0.4.2\n", "python-dateutil              2.9.0.post0\n", "python-docx                  0.8.11\n", "python-dotenv                1.0.1\n", "python-multipart             0.0.6\n", "python-pptx                  0.6.21\n", "python-<PERSON><PERSON><PERSON>             1.10\n", "pytz                         2025.1\n", "pytz-deprecation-shim        0.1.0.post0\n", "pywin32                      307\n", "PyYAML                       6.0.2\n", "pyzmq                        26.2.1\n", "quantulum3                   0.9.0\n", "rapidfuzz                    3.0.0\n", "rarfile                      4.0\n", "referencing                  0.36.2\n", "regex                        2024.11.6\n", "requests                     2.32.3\n", "requests-oauthlib            1.3.1\n", "rich                         13.3.5\n", "rpds-py                      0.22.3\n", "rpy2                         3.5.12\n", "rsa                          4.9\n", "Rtree                        1.3.0\n", "safetensors                  0.5.2\n", "scikit-learn                 1.6.1\n", "scipy                        1.15.1\n", "semantic-version             2.10.0\n", "setuptools                   67.7.2\n", "sgmllib3k                    1.0.0\n", "shapely                      2.0.1\n", "shellingham                  1.5.4\n", "six                          1.17.0\n", "smart-open                   6.4.0\n", "soupsieve                    2.6\n", "spacy                        3.4.4\n", "spacy-legacy                 3.0.12\n", "spacy-loggers                1.0.4\n", "SpeechRecognition            3.8.1\n", "SQLAlchemy                   0.7.10\n", "sqlalchemy-migrate           0.11.0\n", "sqlparse                     0.4.3\n", "srsly                        2.4.6\n", "stack_data                   0.6.3\n", "starlette                    0.26.1\n", "stemming                     1.0.1\n", "sympy                        1.13.3\n", "tabula-py                    2.7.0\n", "tabulate                     0.9.0\n", "tbb                          2021.12.0\n", "Tempita                      0.5.2\n", "tensorboard                  2.12.2\n", "tensorboard-data-server      0.7.0\n", "tensorboard-plugin-wit       1.8.1\n", "tensorflow                   2.12.0\n", "tensorflow-estimator         2.12.0\n", "tensorflow-io-gcs-filesystem 0.31.0\n", "termcolor                    2.2.0\n", "tesseract                    0.1.3\n", "textract                     1.6.5\n", "thinc                        8.1.9\n", "threadpoolctl                3.5.0\n", "tifffile                     2025.1.10\n", "tinysegmenter                0.3\n", "tokenizers                   0.21.0\n", "torch                        2.3.0\n", "torchvision                  0.18.0\n", "tornado                      6.4.2\n", "tqdm                         4.67.1\n", "traitlets                    5.14.3\n", "transformers                 4.48.2\n", "tritonclient                 2.32.0\n", "typer                        0.7.0\n", "typing_extensions            4.12.2\n", "tzdata                       2023.3\n", "tzlocal                      4.3\n", "uc-micro-py                  1.0.1\n", "urllib3                      2.3.0\n", "uvicorn                      0.21.1\n", "visualdl                     2.5.1\n", "Wand                         0.6.11\n", "wasabi                       0.10.1\n", "wcwidth                      0.2.13\n", "websockets                   11.0.1\n", "wget                         3.2\n", "wheel                        0.40.0\n", "wikipedia                    1.4.0\n", "x2paddle                     1.4.1\n", "xlrd                         1.2.0\n", "XlsxWriter                   3.1.0\n", "xlwt                         0.7.2\n", "xxhash                       3.5.0\n", "yarl                         1.8.2\n", "zipp                         3.21.0\n", "zope.event                   4.6\n"]}], "source": ["%pip list\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip uninstall pillow\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install pillow\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from PIL import Image\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[19], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mspacy\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;66;03m# Create a blank spaCy Pipeline for English\u001b[39;00m\n\u001b[0;32m      3\u001b[0m nlp \u001b[38;5;241m=\u001b[39m spacy\u001b[38;5;241m.\u001b[39mload(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124men_core_web_sm\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\__init__.py:6\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01msys\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;66;03m# set library-specific custom warning handling before doing anything else\u001b[39;00m\n\u001b[1;32m----> 6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m setup_default_warnings\n\u001b[0;32m      8\u001b[0m setup_default_warnings()  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;66;03m# These are imported as part of the API\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\errors.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mwarnings\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Literal\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mErrorsWithCodes\u001b[39;00m(\u001b[38;5;28mtype\u001b[39m):\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__getattribute__\u001b[39m(\u001b[38;5;28mself\u001b[39m, code):\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\compat.py:38\u001b[0m\n\u001b[0;32m     35\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[0;32m     36\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mcatalogue\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _importlib_metadata \u001b[38;5;28;01mas\u001b[39;00m importlib_metadata  \u001b[38;5;66;03m# type: ignore[no-redef]    # noqa: F401\u001b[39;00m\n\u001b[1;32m---> 38\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mthinc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Optimizer  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n\u001b[0;32m     40\u001b[0m pickle \u001b[38;5;241m=\u001b[39m pickle\n\u001b[0;32m     41\u001b[0m copy_reg \u001b[38;5;241m=\u001b[39m copy_reg\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\api.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Config, registry, ConfigValidationError\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minitializers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m normal_init, uniform_init, glorot_uniform_init, zero_init\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minitializers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m configure_normal_init\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mloss\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m CategoricalCrossentropy, L2Distance, CosineDistance\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\initializers.py:4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Callable, cast\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackends\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Ops\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m registry\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FloatsXd, Shape\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\__init__.py:8\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mthreading\u001b[39;00m\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Ops\n\u001b[1;32m----> 8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcupy_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m CupyOps\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnumpy_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m NumpyOps\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmps_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m MPSOps\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\cupy_ops.py:4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m registry\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Ops\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnumpy_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m NumpyOps\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _custom_kernels\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DeviceTypes\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\numpy_ops.pyx:1\u001b[0m, in \u001b[0;36minit thinc.backends.numpy_ops\u001b[1;34m()\u001b[0m\n", "\u001b[1;31mValueError\u001b[0m: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject"]}], "source": ["import spacy\n", "# Create a blank spaCy Pipeline for English\n", "nlp = spacy.load(\"en_core_web_sm\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\compat.py:36: UserWarning: 'has_mps' is deprecated, please use 'torch.backends.mps.is_built()'\n", "  hasattr(torch, \"has_mps\")\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\compat.py:37: UserWarning: 'has_mps' is deprecated, please use 'torch.backends.mps.is_built()'\n", "  and torch.has_mps  # type: ignore[attr-defined]\n"]}, {"ename": "ValueError", "evalue": "numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[8], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mspacy\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mspacy_layout\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m spaCyLayout\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mextract_tables_from_pdf\u001b[39m(pdf_file):\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\__init__.py:6\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01msys\u001b[39;00m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;66;03m# set library-specific custom warning handling before doing anything else\u001b[39;00m\n\u001b[1;32m----> 6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m setup_default_warnings\n\u001b[0;32m      8\u001b[0m setup_default_warnings()  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;66;03m# These are imported as part of the API\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\errors.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mwarnings\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Literal\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mErrorsWithCodes\u001b[39;00m(\u001b[38;5;28mtype\u001b[39m):\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__getattribute__\u001b[39m(\u001b[38;5;28mself\u001b[39m, code):\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\spacy\\compat.py:38\u001b[0m\n\u001b[0;32m     35\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[0;32m     36\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mcatalogue\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _importlib_metadata \u001b[38;5;28;01mas\u001b[39;00m importlib_metadata  \u001b[38;5;66;03m# type: ignore[no-redef]    # noqa: F401\u001b[39;00m\n\u001b[1;32m---> 38\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mthinc\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Optimizer  \u001b[38;5;66;03m# noqa: F401\u001b[39;00m\n\u001b[0;32m     40\u001b[0m pickle \u001b[38;5;241m=\u001b[39m pickle\n\u001b[0;32m     41\u001b[0m copy_reg \u001b[38;5;241m=\u001b[39m copy_reg\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\api.py:2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Config, registry, ConfigValidationError\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minitializers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m normal_init, uniform_init, glorot_uniform_init, zero_init\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minitializers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m configure_normal_init\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mloss\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m CategoricalCrossentropy, L2Distance, CosineDistance\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\initializers.py:4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Callable, cast\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackends\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Ops\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m registry\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FloatsXd, Shape\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\__init__.py:8\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mthreading\u001b[39;00m\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Ops\n\u001b[1;32m----> 8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcupy_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m CupyOps\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnumpy_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m NumpyOps\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmps_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m MPSOps\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\cupy_ops.py:4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m registry\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Ops\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnumpy_ops\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m NumpyOps\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _custom_kernels\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DeviceTypes\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python310\\site-packages\\thinc\\backends\\numpy_ops.pyx:1\u001b[0m, in \u001b[0;36minit thinc.backends.numpy_ops\u001b[1;34m()\u001b[0m\n", "\u001b[1;31mValueError\u001b[0m: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject"]}], "source": ["import spacy\n", "from spacy_layout import spaCyLayout\n", "\n", "def extract_tables_from_pdf(pdf_file):\n", "    \"\"\"\n", "    Extracts table data from the given PDF file using spaCyLayout.\n", "\n", "    Args:\n", "        pdf_file (str): Path to the PDF file.\n", "\n", "    Returns:\n", "        list: A list of extracted tables, where each table is represented as a list of rows (list of strings).\n", "    \"\"\"\n", "    # Load the spaCy pipeline\n", "    nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "    # Create an instance of spaCyLayout\n", "    layout = spaCyLayout(nlp)\n", "\n", "    # Process the PDF file to create a spaCy Doc object\n", "    doc = layout(pdf_file)\n", "\n", "    # Retrieve tables in the document\n", "    tables = doc._.tables\n", "\n", "    # Extract text for each table\n", "    extracted_tables = []\n", "    for i, table in enumerate(tables):\n", "        table_data = []\n", "        for row in table:\n", "            row_text = [cell.text for cell in row]  # Extract text from each cell in the row\n", "            table_data.append(row_text)\n", "        extracted_tables.append(table_data)\n", "    \n", "    return extracted_tables\n", "\n", "\n", "\n", "pdf_file = pdf_file = r\"\\\\***********\\bio-act-curation\\MAC-Projects\\Integrated-Indexing\\shipments\\982110\\06000703B\\06000703B.article.002.pdf\"\n", "\n", "# Extract tables from the PDF\n", "tables = extract_tables_from_pdf(pdf_file)\n", "\n", "# Print extracted tables\n", "for i, table in enumerate(tables):\n", "    print(f\"\\nTable {i + 1}:\")\n", "    for row in table:\n", "        print(\" | \".join(row))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## get pdf file path list"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "def get_pdf_files_with_subfolder_pattern(root_folder: str):\n", "    \"\"\"\n", "    Get PDF files from a folder and its subfolders.\n", "    For each subfolder, only return PDF files that contain\n", "    'subfoldername.article.' in their filename.\n", "    \n", "    Args:\n", "        root_folder (str): Root directory path\n", "    \n", "    Returns:\n", "        List[str]: List of matching PDF file paths\n", "    \"\"\"\n", "    matching_pdfs = []\n", "\n", "    for subdir, _, files in os.walk(root_folder):\n", "        print(subdir)\n", "        subfolder_name = Path(subdir).name   \n", "        pattern = f\"{subfolder_name}.article.\"\n", "\n", "        for file in files:\n", "            if file.lower().endswith(\".pdf\") and pattern in file:\n", "                matching_pdfs.append(os.path.join(subdir, file))\n", "\n", "    return matching_pdfs\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\\\***************\\cas_app_files\\UII\\INTEGRATED\\982560\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m pdf_list = \u001b[43mget_pdf_files_with_subfolder_pattern\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mr\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[33;43m***************\u001b[39;49m\u001b[33;43m\\\u001b[39;49m\u001b[33;43mcas_app_files\u001b[39;49m\u001b[33;43m\\\u001b[39;49m\u001b[33;43mUII\u001b[39;49m\u001b[33;43m\\\u001b[39;49m\u001b[33;43mINTEGRATED\u001b[39;49m\u001b[33;43m\\\u001b[39;49m\u001b[33;43m982560\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m pdf \u001b[38;5;129;01min\u001b[39;00m pdf_list[:\u001b[32m20\u001b[39m]:\n\u001b[32m      3\u001b[39m     \u001b[38;5;28mprint\u001b[39m(pdf)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 18\u001b[39m, in \u001b[36mget_pdf_files_with_subfolder_pattern\u001b[39m\u001b[34m(root_folder)\u001b[39m\n\u001b[32m      5\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[33;03mGet PDF files from a folder and its subfolders.\u001b[39;00m\n\u001b[32m      7\u001b[39m \u001b[33;03mFor each subfolder, only return PDF files that contain\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     14\u001b[39m \u001b[33;03m    List[str]: List of matching PDF file paths\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     16\u001b[39m matching_pdfs = []\n\u001b[32m---> \u001b[39m\u001b[32m18\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msubdir\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfiles\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mos\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwalk\u001b[49m\u001b[43m(\u001b[49m\u001b[43mroot_folder\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m     19\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mprint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msubdir\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     20\u001b[39m \u001b[43m    \u001b[49m\u001b[43msubfolder_name\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mPath\u001b[49m\u001b[43m(\u001b[49m\u001b[43msubdir\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mname\u001b[49m\u001b[43m   \u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen os>:434\u001b[39m, in \u001b[36mwalk\u001b[39m\u001b[34m(top, topdown, onerror, followlinks)\u001b[39m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["pdf_list = get_pdf_files_with_subfolder_pattern(r\"\\\\***************\\cas_app_files\\UII\\INTEGRATED\\982560\")\n", "for pdf in pdf_list[:20]:\n", "    print(pdf)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pdfenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}