"""
This module provides functionality to extract and process abstract content from academic papers.

The main function get_abstract() takes a list of content strings from the abstract section
and returns a dictionary containing the processed abstract text and its page location.

The module handles text formatting, cleanup of whitespace, and returns structured content
ready for further processing.
Author: <PERSON>hav
Date: 06-12-24
"""

import re


def get_abstract(content_list, abstract_location):
    """
    Extract the abstract content from the abstract section of a paper.
    Args:
        abstract_list (list): List of strings from the abstract section.

    Returns:
        str: A string containing the abstract content.
    """
    section_content = {}
    full_text = " "
    # print(f"{content_list =}")
    if content_list:
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text += item

        section_content["abstract"] = full_text.strip()
        section_content["page_number"] = abstract_location

        return section_content
    else:
        return full_text
