"""
This module contains functions for extracting and processing results content from research papers.
It provides utilities to clean and format text from results sections, handling various text
formatting issues like line breaks and spacing.
Author: <PERSON>hav
Date: 06-12-24
"""

import re


def get_results(content_list, results_location):
    """
    Extract the abstract content from the abstract section of a paper.
    Args:
        abstract_list (list): List of strings from the abstract section.

    Returns:
        str: A string containing the abstract content.
    """
    section_content = {}
    full_text = " "
    # print(f"{content_list =}")
    if content_list:
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text += item

        section_content["results"] = full_text.strip()
        section_content["page_number"] = results_location

        return section_content
    else:
        return full_text
