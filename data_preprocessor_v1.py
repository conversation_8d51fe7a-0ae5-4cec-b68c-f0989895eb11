
import re
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import inflect

class SegmentPreprocessor:
    """Text preprocessing for PDF segments"""
    
    def __init__(self):
        self.inflect_engine = inflect.engine()
        
        # Compile regex patterns for better performance
        self.patterns = {
            'whitespace': re.compile(r'\s+'),
            'special_chars': re.compile(r'[^\w\s\-\.\,\;\:\!\?\(\)\[\]]'),
            'multiple_dots': re.compile(r'\.{2,}'),
            'brackets': re.compile(r'\[.*?\]'),
            'parentheses': re.compile(r'\(.*?\)'),
            'numbers': re.compile(r'\d+'),
            'abbreviations': re.compile(r'\b[A-Z]{2,}\b')
        }
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not isinstance(text, str):
            return ""
        
        # Basic cleaning
        text = self.patterns['whitespace'].sub(' ', text)
        text = self.patterns['multiple_dots'].sub('.', text)
        text = text.strip()
        
        return text
    
    def extract_sentences(self, text: str) -> List[str]:
        """Extract sentences from text"""
        if not text:
            return []
        
        # Simple sentence splitting
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        # Filter out very short sentences
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        return sentences
    
    def preprocess_segment(self, segment_data: Dict) -> Dict:
        """Preprocess a single segment"""
        processed = segment_data.copy()
        
        if 'text' in processed:
            processed['cleaned_text'] = self.clean_text(processed['text'])
            processed['sentences'] = self.extract_sentences(processed['cleaned_text'])
        
        return processed
    
    def process_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process entire dataframe"""
        processed_df = df.copy()
        
        if 'text' in processed_df.columns:
            processed_df['cleaned_text'] = processed_df['text'].apply(self.clean_text)
        
        return processed_df

if __name__ == "__main__":
    # filename = "dataset_with_title_abstract_982560_updated_sample.xlsx"
    filename = "combined_df.xlsx"
    df = pd.read_excel(filename)

    sp = SegmentPreprocessor()
    df = sp.preprocess_dataframe(df)

    df.to_excel("dataset_segments_processed_custom_split_sample.xlsx", index=False)

    print("Sample processed Abstract with delimiters:")
    print(df["abstract_processed"].iloc[0][:1000])


    print("\nSample processed Results with delimiters:")
    # print(df["results_processed"].iloc[0][:1000])
    print(df["results_processed"].iloc[0])
################################################################################
