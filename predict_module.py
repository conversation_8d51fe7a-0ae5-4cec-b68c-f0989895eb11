# # predict_module.py
# import torch
# from transformers import <PERSON>Tokenizer, AutoModelForTokenClassification
# from config import id2label, MODEL_NAME, MAX_LENGTH

# def predict_keyphrases(sentences, model_dir="bert_keyphrase_model", device="cpu"):
#     tokenizer = AutoTokenizer.from_pretrained(model_dir)
#     model = AutoModelForTokenClassification.from_pretrained(model_dir)
#     model.to(device)
#     model.eval()

#     outputs = []
#     for sent in sentences:
#         enc = tokenizer(sent, return_offsets_mapping=True, truncation=True, max_length=MAX_LENGTH, return_tensors="pt")
#         offsets = enc["offset_mapping"][0].numpy().tolist()
#         input_ids = enc["input_ids"].to(device)

#         with torch.no_grad():
#             logits = model(input_ids).logits
#         preds = logits.argmax(-1).squeeze().cpu().numpy().tolist()

#         cur_span, spans = None, []
#         for p, (s, e) in zip(preds, offsets):
#             if s == e == 0:
#                 continue
#             label = id2label.get(p, "O")
#             if label == "B-KEY":
#                 if cur_span: spans.append(cur_span)
#                 cur_span = [s, e]
#             elif label == "I-KEY" and cur_span:
#                 cur_span[1] = e
#             else:
#                 if cur_span: 
#                     spans.append(cur_span)
#                     cur_span = None
#         if cur_span: spans.append(cur_span)

#         phrases = [sent[s:e] for s, e in spans]
#         outputs.append(phrases)
#     return outputs


#####################################################################
# predict_module.py
import torch
from transformers import AutoTokenizer, AutoModelForTokenClassification
# from config import id2label, MODEL_NAME, MAX_LENGTH
from config_scibert import id2label, MODEL_NAME, MAX_LENGTH



# def predict_keyphrases(sentences, model_dir="bert_keyphrase_model", device=None):
def predict_keyphrases(sentences, model_dir=r"D:\bert_scibert_model", device=None):
    """
    Predict keyphrases from a list of sentences using a trained model.
    """
    # Auto-detect device
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"

    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = AutoModelForTokenClassification.from_pretrained(model_dir)
    model.to(device)
    model.eval()

    outputs = []
    for sent in sentences:
        enc = tokenizer(
            sent,
            return_offsets_mapping=True,
            truncation=True,
            max_length=MAX_LENGTH,
            return_tensors="pt",
        )
        offsets = enc["offset_mapping"][0].numpy().tolist()
        input_ids = enc["input_ids"].to(device)

        with torch.no_grad():
            logits = model(input_ids).logits
        preds = logits.argmax(-1).squeeze().cpu().numpy().tolist()

        # Decode predicted spans
        cur_span, spans = None, []
        for p, (s, e) in zip(preds, offsets):
            if s == e == 0:  # special tokens
                continue
            label = id2label.get(p, "O")
            if label == "B-KEY":
                if cur_span:
                    spans.append(cur_span)
                cur_span = [s, e]
            elif label == "I-KEY" and cur_span:
                cur_span[1] = e
            else:
                if cur_span:
                    spans.append(cur_span)
                    cur_span = None
        if cur_span:
            spans.append(cur_span)

        phrases = [sent[s:e] for s, e in spans]
        outputs.append(phrases)

    return outputs


if __name__ == "__main__":
    #sample testing
    test_sentences = [
        "Different from traditional thermosetting PF, LFR was hot-melt and possessed outstanding photothermal conversion and hydrophobicity properties, which provided the possibility for its use as a photothermal power generation coating and anti/de-icing material.",  ##Hydrophobicity

        "electrochemical surface areas (ECSAs) are evaluated by cyclic voltammetry (CV) curves in an acid solution (Figure 3a ).",  ##Surface areas

        "The plot of the projected d-density of states (PDOS) reveals that the electronic redistribution of Pt in the Ga-O-PtPd interface induces a directed electron movement (Figure S6 , Supporting Information).",  ##'Density of states', 'electronic redistribution of Pt'

        "Global warming and climate change have led to the development of technologies for reducing and recycling CO2 emissions and the establishment of environmentally friendly fuel systems.",##'Global warming', 'climate change', 'Recycling', 'Environmentally friendly'

        "Your Internet connection should be able to handle multiple devices streaming HD videos at the same time."  ## Not expecting any keyphrase
    ]

    #Expected output: ["Hydrophobicity", "Surface areas", 'Density of states', 'electronic redistribution of Pt','Global warming', 'climate change', 'Recycling', 'Environmentally friendly' ]

    #Received output:  ['hydrophobicity', 'surface areas', 'density of states','Global warming', 'climate change', 'environmentally friendly', 'thermosetting',  'cyclic voltammetry',  'Internet']

    # missed  = ['electronic redistribution of Pt', 'Recycling', ]  #### synthetic data ('electronic redistribution of Pt')

    # Extra = ['electrochemical surface areas',  'thermosetting', 'cyclic voltammetry']

    # Noise = ['Internet']



    test_sentences_1 = [
        "Different from traditional thermosetting PF, LFR was hot-melt and possessed outstanding photothermal conversion and hydrophobicity properties, which provided the possibility for its use as a photothermal power generation coating and anti/de-icing material. Electrochemical surface areas (ECSAs) are evaluated by cyclic voltammetry (CV) curves in an acid solution (Figure 3a). The plot of the projected d-density of states (PDOS) reveals that the electronic redistribution of Pt in the Ga-O-PtPd interface induces a directed electron movement (Figure S6 , Supporting Information). Global warming and climate change have led to the development of technologies for reducing and recycling CO2 emissions and the establishment of environmentally friendly fuel systems. BERT is a pre-trained language model which comprises a set of transformer encoders which represents the text at word and sentence level with the help of unsupervised training techniques like masked language modeling and next sentence prediction. Being a pre-trained model, BERT trained on 3300M words. BERT uses a transformer encoder with an attention mechanism to learn contextual relations between words. Your Internet connection should be able to handle multiple devices streaming HD videos at the same time."   
    ]

    test_sentences = [
        "Different from traditional thermosetting PF, LFR was hot-melt and possessed outstanding photothermal conversion and hydrophobicity properties, which provided the possibility for its use as a photothermal power generation coating and anti/de-icing material. electrochemical surface areas (ECSAs) are evaluated by cyclic voltammetry (CV) curves in an acid solution (Figure 3a). The plot of the projected d-density of states (PDOS) reveals that the electronic redistribution of Pt in the Ga-O-PtPd interface induces a directed electron movement (Figure S6 , Supporting Information).  Global warming and climate change have led to the development of technologies for reducing and recycling CO2 emissions and the establishment of environmentally friendly fuel systems. Your Internet connection should be able to handle multiple devices streaming HD videos at the same time."  ## ["Hydrophobicity", "Surface areas", 'Density of states', 'electronic redistribution of Pt','Global warming', 'climate change', 'Recycling', 'Environmentally friendly' ]
    ]

    predictions = predict_keyphrases(test_sentences)

    print("\n Predicted Keyphrases:")
    unique_keyphrases = []
    for sent, keys in zip(test_sentences, predictions):
        print(f"Sentence: {sent}")
        unique_keys = list(set(keys))
        print(f"Keyphrases: {keys}")  # Remove duplicates
        if keys not in unique_keyphrases:
            unique_keyphrases.extend(keys)  # Add unique keyphrases to the list
        # print(f"Keyphrases: {keys}\n")
    print(f"Unique Keyphrases: {unique_keyphrases}")
