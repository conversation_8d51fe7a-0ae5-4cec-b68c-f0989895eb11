
import pandas as pd
import numpy as np
import time
import os
import glob
from pathlib import Path
from typing import Dict, List, Optional
from main_sentence_filter_final_test2 import main_processing_pipeline

def get_segmented_pdf_text_optimized(section_code: str, curator_data_folder: str, 
                                pdf_folder: str, abbr_full_form_excel: str) -> str:
    """
    Optimized PDF text extraction and segmentation with proper naming
    Returns path to the final Excel file with segmented text
    """
    print(" Starting optimized PDF text extraction...")
    
    # Extract shipment name from pdf_folder path
    shipment_name = os.path.basename(pdf_folder.rstrip('\\').rstrip('/'))
    
    try:
        # Use the main processing pipeline with shipment-specific naming
        final_excel_path = main_processing_pipeline(
            section_code, curator_data_folder, pdf_folder, abbr_full_form_excel,
            shipment_name=shipment_name  # Pass shipment name for unique naming
        )
        
        if final_excel_path:
            print(f" PDF segmentation completed: {final_excel_path}")
            return final_excel_path
        else:
            print(" PDF segmentation failed")
            return None
            
    except Exception as e:
        print(f" Error in PDF segmentation: {e}")
        return None

def process_pdf_folder(pdf_folder: str) -> Dict[str, str]:
    """Process all PDFs in a folder and extract text segments"""
    
    # This is a placeholder implementation
    # In a real scenario, this would use PDF processing libraries
    
    pdf_files = glob.glob(os.path.join(pdf_folder, "*.pdf"))
    
    results = {}
    for pdf_file in pdf_files:
        filename = os.path.basename(pdf_file)
        
        # Placeholder extracted text
        results[filename] = {
            'title': f'Sample title from {filename}',
            'abstract': f'Sample abstract from {filename}',
            'introduction': f'Sample introduction from {filename}',
            'methods': f'Sample methods from {filename}',
            'results': f'Sample results from {filename}',
            'discussion': f'Sample discussion from {filename}',
            'conclusion': f'Sample conclusion from {filename}'
        }
    
    return results

def create_segmented_dataframe(pdf_data: Dict, keyphrases: List[str]) -> pd.DataFrame:
    """Create DataFrame with segmented text data"""
    
    all_data = []
    
    for pdf_file, sections in pdf_data.items():
        for section_type, text in sections.items():
            if text and isinstance(text, str):
                # Split text into sentences
                sentences = text.split('.')
                
                for sentence in sentences:
                    sentence = sentence.strip()
                    if len(sentence) > 10:  # Filter short sentences
                        
                        # Check for keyphrases
                        found_keyphrases = []
                        for keyphrase in keyphrases:
                            if keyphrase.lower() in sentence.lower():
                                found_keyphrases.append(keyphrase)
                        
                        all_data.append({
                            'pdf_file': pdf_file,
                            'section_type': section_type,
                            'sentence': sentence,
                            'keyphrases_found': ', '.join(found_keyphrases),
                            'has_keyphrase': len(found_keyphrases) > 0
                        })
    
    return pd.DataFrame(all_data)

if __name__ == "__main__":
    section_code = "52"
    curator_data_folder = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized\curated_data_sample"
    abbr_full_form_excel = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\keyphrase_extraction_optimized\utility_data\text_abbreviations.xlsx"
    pdf_folder = r"\\***********\Bio-Act-Curation\MAC-Projects\AIML_Shared_Data\Integrated Indexing\shipments\982560"
    
    try:
        final_excel_path = get_segmented_pdf_text_optimized(
            section_code, curator_data_folder, pdf_folder, abbr_full_form_excel
        )
        
        if final_excel_path:
            print(f"\n Process completed successfully!")
            print(f" Final merged file ready for keyphrase extraction: {final_excel_path}")
        else:
            print("\n Process failed!")
            
    except Exception as e:
        print(f" Error: {e}")
