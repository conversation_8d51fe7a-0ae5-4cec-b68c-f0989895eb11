#!/usr/bin/env python3
"""
Relevance Filtering Configuration
=================================

Configuration parameters for relevance-based sentence filtering.
Adjust these parameters to control how aggressively sentences are filtered
based on their relevance to title and abstract content.
"""

# Relevance filtering parameters
RELEVANCE_CONFIG = {
    # Minimum relevance score for sentence inclusion
    # Lower values = more sentences included
    # Higher values = fewer, more relevant sentences
    'min_relevance_score': 0.1,
    
    # Maximum sentences per segment
    'max_sentences_per_segment': {
        'title': 16,        # More sentences from title
        'abstract': 16,     # More sentences from abstract  
        'results': 8,       # Moderate from results
        'conclusion': 8,    # Moderate from conclusion
        'introduction': 6,  # Fewer from introduction
        'methods': 4,       # Fewer from methods
        'discussion': 6,    # Fewer from discussion
        'default': 3        # Very few from other segments
    },
    
    # Semantic similarity settings
    'use_semantic_similarity': True,
    'semantic_model': 'all-MiniLM-L6-v2',  # Lightweight model
    
    # Keyword filtering settings
    'min_keyword_length': 3,
    'filter_common_words': True,
    
    # Scoring weights (must sum to 1.0)
    'keyword_weight': 0.4,      # Weight for keyword overlap
    'semantic_weight': 0.6,     # Weight for semantic similarity
    
    # Strict filtering for non-priority segments
    'strict_filtering_multiplier': 2.0,  # Multiply min_relevance_score for non-priority segments
    
    # Priority segments (in order of importance)
    'priority_segments': ['title', 'abstract', 'results', 'conclusion', 'introduction', 'methods'],
    
    # Stop words for keyword extraction
    'stop_words': {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 
        'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 
        'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these', 
        'those', 'was', 'were', 'been', 'have', 'has', 'had', 'will', 'would', 
        'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'are', 'is',
        'also', 'however', 'therefore', 'thus', 'furthermore', 'moreover',
        'study', 'studies', 'research', 'analysis', 'results', 'conclusion',
        'method', 'methods', 'data', 'using', 'used', 'showed', 'found',
        'present', 'presented', 'show', 'shows', 'demonstrate', 'demonstrated',
        'indicate', 'indicates', 'suggest', 'suggests', 'report', 'reported'
    }
}

# Preset configurations for different use cases
PRESET_CONFIGS = {
    'strict': {
        'min_relevance_score': 0.2,
        'max_sentences_per_segment': {
            'title': 10, 'abstract': 10, 'results': 5, 'conclusion': 5,
            'introduction': 3, 'methods': 2, 'default': 2
        },
        'strict_filtering_multiplier': 3.0
    },
    
    'moderate': {
        'min_relevance_score': 0.1,
        'max_sentences_per_segment': {
            'title': 16, 'abstract': 16, 'results': 8, 'conclusion': 8,
            'introduction': 6, 'methods': 4, 'default': 3
        },
        'strict_filtering_multiplier': 2.0
    },
    
    'lenient': {
        'min_relevance_score': 0.05,
        'max_sentences_per_segment': {
            'title': 20, 'abstract': 20, 'results': 12, 'conclusion': 12,
            'introduction': 8, 'methods': 6, 'default': 5
        },
        'strict_filtering_multiplier': 1.5
    }
}


def get_config(preset: str = 'moderate') -> dict:
    """Get configuration with optional preset"""
    
    if preset in PRESET_CONFIGS:
        config = RELEVANCE_CONFIG.copy()
        config.update(PRESET_CONFIGS[preset])
        return config
    else:
        return RELEVANCE_CONFIG


def apply_config_to_filter(relevance_filter, config: dict = None):
    """Apply configuration to a RelevanceFilter instance"""
    
    if config is None:
        config = RELEVANCE_CONFIG
    
    # Update filter attributes based on config
    if hasattr(relevance_filter, 'min_relevance_score'):
        relevance_filter.min_relevance_score = config.get('min_relevance_score', 0.1)
    
    if hasattr(relevance_filter, 'max_sentences_per_segment'):
        relevance_filter.max_sentences_per_segment = config.get('max_sentences_per_segment', {})
    
    if hasattr(relevance_filter, 'use_semantic_similarity'):
        relevance_filter.use_semantic_similarity = config.get('use_semantic_similarity', True)
    
    return relevance_filter


def print_config_summary(config: dict = None):
    """Print a summary of the current configuration"""
    
    if config is None:
        config = RELEVANCE_CONFIG
    
    print("="*60)
    print("RELEVANCE FILTERING CONFIGURATION")
    print("="*60)
    
    print(f"Minimum relevance score: {config['min_relevance_score']}")
    print(f"Use semantic similarity: {config['use_semantic_similarity']}")
    print(f"Strict filtering multiplier: {config['strict_filtering_multiplier']}")
    
    print(f"\nMax sentences per segment:")
    max_sentences = config['max_sentences_per_segment']
    for segment, count in max_sentences.items():
        print(f"  {segment}: {count}")
    
    print(f"\nScoring weights:")
    print(f"  Keyword overlap: {config['keyword_weight']}")
    print(f"  Semantic similarity: {config['semantic_weight']}")
    
    print(f"\nPriority segments: {config['priority_segments']}")


def estimate_output_size(input_sentences: int, config: dict = None) -> dict:
    """Estimate output size based on configuration"""
    
    if config is None:
        config = RELEVANCE_CONFIG
    
    # Rough estimation based on typical segment distribution
    typical_distribution = {
        'title': 0.05,
        'abstract': 0.15,
        'results': 0.30,
        'conclusion': 0.10,
        'introduction': 0.15,
        'methods': 0.15,
        'other': 0.10
    }
    
    max_sentences = config['max_sentences_per_segment']
    estimated_output = 0
    
    for segment, proportion in typical_distribution.items():
        segment_input = int(input_sentences * proportion)
        segment_limit = max_sentences.get(segment, max_sentences.get('default', 3))
        segment_output = min(segment_input, segment_limit)
        estimated_output += segment_output
    
    # Apply relevance filtering reduction (estimated)
    relevance_reduction = 0.7  # Assume 70% pass relevance filter
    final_estimate = int(estimated_output * relevance_reduction)
    
    return {
        'input_sentences': input_sentences,
        'after_segment_limits': estimated_output,
        'after_relevance_filter': final_estimate,
        'reduction_ratio': final_estimate / input_sentences if input_sentences > 0 else 0
    }


if __name__ == "__main__":
    # Demo the configuration system
    print_config_summary()
    
    print("\n" + "="*60)
    print("PRESET CONFIGURATIONS")
    print("="*60)
    
    for preset_name in PRESET_CONFIGS.keys():
        print(f"\n{preset_name.upper()} preset:")
        preset_config = get_config(preset_name)
        print(f"  Min relevance score: {preset_config['min_relevance_score']}")
        print(f"  Title/Abstract max: {preset_config['max_sentences_per_segment']['title']}")
        print(f"  Results max: {preset_config['max_sentences_per_segment']['results']}")
        print(f"  Default max: {preset_config['max_sentences_per_segment']['default']}")
    
    print("\n" + "="*60)
    print("OUTPUT SIZE ESTIMATION")
    print("="*60)
    
    test_inputs = [100, 500, 1000, 2000]
    for input_size in test_inputs:
        estimate = estimate_output_size(input_size)
        print(f"\nInput: {input_size} sentences")
        print(f"  After segment limits: {estimate['after_segment_limits']}")
        print(f"  After relevance filter: {estimate['after_relevance_filter']}")
        print(f"  Reduction ratio: {estimate['reduction_ratio']:.2%}")
