import pandas as pd
import spacy
import re
from typing import List, Dict
import ast


# Load spacy model once
nlp = spacy.load("en_core_web_sm")

# -----------------------------
# Utility functions
# -----------------------------
def normalize_text(text: str) -> str:
    """Lowercase and remove hyphens for fair comparison."""
    return re.sub(r"[-]", "", text.lower().strip())

def split_into_sentences(text: str) -> List[str]:
    """Split text into sentences using spaCy."""
    if pd.isna(text) or not isinstance(text, str) or not text.strip():
        return []
    doc = nlp(text)
    return [sent.text.strip() for sent in doc.sents if sent.text.strip()]

def find_sentences_for_term(term: str, text: str) -> List[str]:
    """Find sentences containing the term in the given text (ignoring case and hyphen)."""
    sentences = split_into_sentences(text)
    norm_term = normalize_text(term)
    matched = []
    for sent in sentences:
        if norm_term in normalize_text(sent):
            matched.append(sent)
    return matched

def process_row(row: pd.Series, text_columns: List[str]) -> List[Dict]:
    """
    For each term in terms_list, check across all text columns
    and return expanded rows.
    """
    results = []
    terms = row["terms_list"]
    
    # Ensure terms_list is a list
    if isinstance(terms, str):
        try:
            terms = eval(terms)  # in case it's stringified list
        except:
            terms = [terms]
    
    for term in terms:
        new_row = {
            "SHIPMENT_NAME": row["SHIPMENT_NAME"],
            "pdf_path": row["pdf_path"],
            "tan_name": row["tan_name"],
            "SECTION_CODE": row["SECTION_CODE"],
            "term_found": term,
            "terms_list": terms
        }
        # For each text column, collect matching sentences
        for col in text_columns:
            text = row[col]
            matches = find_sentences_for_term(term, text)
            new_row[col] = " || ".join(matches) if matches else ""
        results.append(new_row)
    return results



def safe_convert_terms(terms):
    """Convert terms_list string to list safely."""
    if isinstance(terms, str):
        try:
            # handle "['a', 'b']" or '["a","b"]'
            return ast.literal_eval(terms)
        except (ValueError, SyntaxError):
            # fallback for comma-separated strings
            return [t.strip() for t in terms.split(",") if t.strip()]
    elif isinstance(terms, list):
        return terms
    else:
        return []

def expand_dataframe(df: pd.DataFrame, text_columns: List[str]) -> pd.DataFrame:
    """
    Expand dataframe such that each term in terms_list gets its own row 
    with sentence matches.
    """
    expanded = []
    for _, row in df.iterrows():
        # ensure terms_list is list
        terms = safe_convert_terms(row["terms_list"])
        row["terms_list"] = terms
        # process each row with updated terms_list
        expanded.extend(process_row(row, text_columns))
    
    return pd.DataFrame(expanded)


# -----------------------------
# Main function
# -----------------------------
def extract_term_sentences(input_file: str, output_file: str):
    text_columns = ["title", "abstract", "experimental_procedures", "results", "conclusion", "figure_titles"]
    
    df = pd.read_excel(input_file)
    
    expanded_df = expand_dataframe(df, text_columns)
    
    # Save final output
    expanded_df.to_excel(output_file, index=False)
    print(f"Saved processed file: {output_file}")


if __name__ == "__main__":
    input_file = r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\dataset_with_title_abstract_982560_final_all.xlsx"
    output_file =r"C:\Users\<USER>\Desktop\Anand\chemindexing_2025\982560_cth_sentences.xlsx"
    extract_term_sentences(input_file, output_file)

