
import re
import spacy
import numpy as np
from sentence_transformers import SentenceTransformer, util
from typing import List, Dict, Tuple, Optional, Set
import time


class NoisySentenceRemover:
    """Remove noisy sentences and improve quality"""
    
    def __init__(self, keyphrases: List[str]):
        self.keyphrases = keyphrases
        
        # Load models
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            print("Warning: spaCy model not found. Install with: python -m spacy download en_core_web_sm")
            self.nlp = None
        
        try:
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        except Exception:
            print("Warning: SentenceTransformer not available")
            self.sentence_model = None
        
        # Compile regex patterns
        self.noise_patterns = [
            re.compile(r'^\s*\d+\s*$'),  # Only numbers
            re.compile(r'^\s*[^\w\s]+\s*$'),  # Only special characters
            re.compile(r'^.{0,10}$'),  # Too short
            re.compile(r'^.{500,}$'),  # Too long
        ]
    
    def is_noisy_sentence(self, sentence: str) -> bool:
        """Check if sentence is noisy"""
        if not sentence or not isinstance(sentence, str):
            return True
        
        sentence = sentence.strip()
        
        # Check against noise patterns
        for pattern in self.noise_patterns:
            if pattern.match(sentence):
                return True
        
        # Check word count
        words = sentence.split()
        if len(words) < 3 or len(words) > 50:
            return True
        
        # Check special character ratio
        special_chars = len(re.findall(r'[^\w\s]', sentence))
        if special_chars / len(sentence) > 0.3:
            return True
        
        return False
    
    def calculate_similarity(self, sentence: str, context: str) -> float:
        """Calculate semantic similarity"""
        if not self.sentence_model or not context:
            return 0.5  # Default similarity
        
        try:
            embeddings = self.sentence_model.encode([sentence, context])
            similarity = util.cos_sim(embeddings[0], embeddings[1]).item()
            return similarity
        except Exception:
            return 0.5
    
    def process_sentences(self, sentences: List[Dict], title: str = "", 
                        abstract: str = "") -> Tuple[List[Dict], List[Dict]]:
        """Process sentences and separate good from noisy"""
        good_sentences = []
        noisy_sentences = []
        
        context = f"{title} {abstract}".strip()
        
        for sentence_data in sentences:
            sentence = sentence_data.get('sentence', '')
            
            if self.is_noisy_sentence(sentence):
                noisy_sentences.append(sentence_data)
                continue
            
            # Calculate quality score
            similarity = self.calculate_similarity(sentence, context)
            sentence_data['quality_score'] = similarity
            
            if similarity > 0.3:  # Threshold for good sentences
                good_sentences.append(sentence_data)
            else:
                noisy_sentences.append(sentence_data)
        
        return good_sentences, noisy_sentences
    
    def process_optimized(self, text: str, title: str = "", 
                        abstract: str = "") -> Tuple[List[str], List[str], List[str]]:
        """Optimized processing method"""
        sentences = text.split('|')
        sentences = [s.strip() for s in sentences if s.strip()]
        
        positive = []
        negative = []
        unmatched = []
        
        for sentence in sentences:
            if self.is_noisy_sentence(sentence):
                negative.append(sentence)
            else:
                # Check for keyphrases
                found_keyphrase = False
                for keyphrase in self.keyphrases:
                    if keyphrase.lower() in sentence.lower():
                        positive.append(sentence)
                        found_keyphrase = True
                        break
                
                if not found_keyphrase:
                    unmatched.append(sentence)
        
        return positive, negative, unmatched


    def _generate_keyphrase_variants(self, keyphrases):
        variants = {}
        for kp in keyphrases:
            words = kp.split()
            formed_variants = set()

            # Generate singular/plural for last word (simple heuristic)
            if len(words) == 1:
                singular = self.inflector.singular_noun(kp)
                if singular:
                    formed_variants.add(singular.lower())
                plural = self.inflector.plural(kp)
                formed_variants.add(plural.lower())
                formed_variants.add(kp.lower())
            else:
                # For multiword phrases, generate singular/plural of last word only
                singular_last = self.inflector.singular_noun(words[-1]) or words[-1]
                plural_last = self.inflector.plural(words[-1])
                base_words = words[:-1]
                
                singular_form = " ".join(base_words + [singular_last]).lower()
                plural_form = " ".join(base_words + [plural_last]).lower()
                formed_variants.add(singular_form)
                formed_variants.add(plural_form)
                formed_variants.add(kp.lower())

            variants[kp] = list(formed_variants)
        return variants


    def _get_actual_matched_phrases(self, sentence):
        if self.keyphrase_embeddings is None:
            return []
        doc = self.nlp(sentence.lower())
        candidates = list({chunk.text for chunk in doc.noun_chunks} | {tok.text for tok in doc if tok.is_alpha and not tok.is_stop})
        if not candidates:
            return []
        candidate_embs = self.embedder.encode(candidates, convert_tensor=True)
        scores = util.pytorch_cos_sim(candidate_embs, self.keyphrase_embeddings)
        max_scores, max_indices = scores.max(dim=1)
        matched = []
        for i, score in enumerate(max_scores):
            if score.item() >= self.keyphrase_sim_threshold:
                matched.append((self.keyphrases[max_indices[i].item()], score.item()))
        return matched

    def _is_legal_sentence(self, sentence):
        doc = self.nlp(sentence)
        return any(tok.dep_ == "nsubj" for tok in doc) and any(tok.pos_ == "VERB" for tok in doc)

    def _has_pos_pattern(self, sentence):
        doc = self.nlp(sentence.lower())
        lowered = sentence.lower()
        for kp in self.keyphrases:
            if kp in lowered:
                kp_tokens = kp.split()
                for i in range(len(doc) - len(kp_tokens) + 1):
                    if all(doc[i + j].text == kp_tokens[j] for j in range(len(kp_tokens))):
                        w = doc[max(0, i - 5) : i + len(kp_tokens) + 5]
                        for idx in range(len(w) - 1):
                            if (w[idx].pos_ in {"NOUN","PROPN"} and w[idx+1].pos_ == "ADJ") or (w[idx].pos_ == "ADJ" and w[idx+1].pos_ in {"NOUN","PROPN"}):
                                return True
                        if any(tok.pos_ in {"NOUN","PROPN","ADJ"} for tok in w):
                            return True
        return False

    def _sentence_has_phrase(self, sentence):
        lowered = sentence.lower()
        return any(kp in lowered for kp in self.keyphrases)
    
 
    def _keyphrase_has_importance(self, sentence: str) -> bool:
        """
        Checks syntactic dependency if any keyphrase words connect to importance verbs or adjectives.
        """
        IMPORTANCE_VERBS = {
        "increase", "decrease","reduce","enhanced", "enhance", "improve", "rise", "fall", "decline","grow", "drop", "boost", "lower", "raise", "strengthen", "weaken", "promote", "suppress", "elevate", "amplify", "diminish",  # "increased", "decreased", "reduced","improved", "improving", "improves"
        }
        doc = self.nlp(sentence.lower())
        importance_lemmas = IMPORTANCE_VERBS

        # For each keyphrase, check if any token in the sentence matches it
        for kp in self.keyphrases:
            kp_tokens = kp.split()
            for i in range(len(doc) - len(kp_tokens) + 1):
                if all(doc[i + j].text == kp_tokens[j] for j in range(len(kp_tokens))):
                    # Find head tokens of this span and check their children or parents for importance words
                    span_tokens = doc[i: i + len(kp_tokens)]
                    for tok in span_tokens:
                        # Check relations in subtree and head for importance words
                        related_tokens = list(tok.subtree) + ([tok.head] if tok.head != tok else [])
                        # print(f"{related_tokens=}: {sentence =}")
                        for rel_tok in related_tokens:
                            if rel_tok.lemma_ in importance_lemmas:
                                # print(f">>>> Found importance word {rel_tok.lemma_} in {sentence}")
                                return True
        return False

    def _find_keyphrases_in_sentence(self, sentence):
        sentence_lower = sentence.lower()
        matched = set()
        for kp, variants in self.keyphrase_variants.items():
            for var in variants:
                if var in sentence_lower:
                    matched.add(kp)
                    break
        return list(matched)


    def _is_short_sentence_negative(self, sentence: str, num_words: int, num_keyphrases: int, idx: int, important_indexes: set) -> bool:
        """
        Return True if sentence should be negative example due to being short (<8 words),
        containing a single keyphrase, and NOT in important indexes.
        """
        if num_words < 8 and num_keyphrases == 1 and (important_indexes is None or idx not in important_indexes):
            return True
        return False
    
    def _is_grammatically_correct(self, sentence: str) -> bool:
        # If keyphrase importance detected, bypass grammar check
        if self._keyphrase_has_importance(sentence):
            return True
        doc = self.nlp(sentence)

        # Check for subject: active or passive
        has_subject = any(tok.dep_ in {"nsubj", "nsubjpass"} for tok in doc)
        
        # Check for verb (main verb or auxiliary)
        has_verb = any(tok.pos_ in {"VERB", "AUX"} for tok in doc)

        # Additional : if no subject, check for copula "be" constructions or nominal sentences
        if not has_subject:
            
            # Check 
            has_copula = any(tok.dep_ == "cop" for tok in doc)
            has_attr = any(tok.dep_ == "attr" for tok in doc)
            if has_copula and has_attr:
                return True
        
        # Require both verb and subject unless special constructions apply
        return has_subject and has_verb


    def filter_sentences(self, sentences, title, abstract, phrase_counts=None, important_indexes=None):
        if phrase_counts is None:
            text_all = " ".join(sentences).lower()
            phrase_counts = {kp: text_all.count(kp) for kp in self.keyphrases}

        context_text = (title + " " + abstract).strip()
        context_emb = self.embedder.encode(context_text, convert_tensor=True) if context_text else None

        positive_examples = {kp: [] for kp in self.keyphrases}
        negative_examples = {kp: [] for kp in self.keyphrases}

        unmatched_phrases = set(self.keyphrases)
        removed_stats = {"ref": 0, "pos": 0, "grammar": 0, "similarity": 0, "no_phrase": 0, "short_neg": 0, 'journal' :0}

        prev_contains_phrase = False
        candidates_for_phrases = {kp: [] for kp in self.keyphrases}

        for idx, raw_sent in enumerate(sentences):
            sent = raw_sent.strip()
            if not sent:
                continue
            

            # matched_kps = [kp for kp in self.keyphrases if kp in sent.lower()]
            matched_kps = self._find_keyphrases_in_sentence(sent)

            if not matched_kps:
                removed_stats["no_phrase"] += 1
                continue

            # New validation: short sentence check for negative
            if self._is_short_sentence_negative(
                sentence=sent,
                num_words=len(sent.split()),
                num_keyphrases=len(matched_kps),
                idx=idx,
                important_indexes=important_indexes
            ):
                removed_stats["short_neg"] +=1
                for kp in matched_kps:
                    if sent:
                        negative_examples[kp].append(sent)
                continue
            # if "journal of" in raw_sent.lower():
            #     removed_stats["journal"] +=1
            #     for kp in matched_kps:
            #         if sent:
            #             negative_examples[kp].append(sent)
            #     continue

            fail_type = None
            if self.ref_pattern.search(sent):
                fail_type = "ref"

            elif not self._has_pos_pattern(sent):
                fail_type = "pos"

            elif not self._is_grammatically_correct(sent):
                fail_type = "grammar"

            if fail_type:
                removed_stats[fail_type] += 1
                for kp in matched_kps:
                    if sent:
                        # print(f"REMOVED: {fail_type} {sent}")
                        negative_examples[kp].append(sent)
                continue

            if len(matched_kps) > 1:
                for kp in matched_kps:
                    if sent:
                        positive_examples[kp].append(sent)
                unmatched_phrases.difference_update(matched_kps)
                prev_contains_phrase = True
                continue

            if prev_contains_phrase and len(matched_kps) == 1:
                for kp in matched_kps:
                    if sent:
                        positive_examples[kp].append(sent)
                unmatched_phrases.difference_update(matched_kps)
                prev_contains_phrase = True
                continue

            kp = matched_kps[0]
            freq = phrase_counts.get(kp, 0)
            do_similarity = freq >= 3 and context_emb is not None

            if do_similarity:
                sent_emb = self.embedder.encode(sent, convert_tensor=True)
                sim_score = util.pytorch_cos_sim(sent_emb, context_emb).item()
                threshold = self.similarity_threshold
                if important_indexes and idx in important_indexes:
                    threshold = max(threshold - 0.2, 0.0)
                if sim_score < threshold:
                    removed_stats["similarity"] += 1
                    if sent:
                        negative_examples[kp].append(sent)
                    prev_contains_phrase = False
                    continue
                candidates_for_phrases[kp].append((sent, sim_score))
            else:
                candidates_for_phrases[kp].append((sent, None))

            prev_contains_phrase = True

        for kp, clist in candidates_for_phrases.items():
            freq = phrase_counts.get(kp, 0)
            if freq < 3:
                for sent, _ in clist:
                    if sent:
                        positive_examples[kp].append(sent)
            else:
                exact_match_sents = [t for t in clist if kp in t[0].lower()]
                exact_match_sents.sort(key=lambda x: x[1] if x[1] is not None else 0, reverse=True)
                selected = exact_match_sents[:2]
                if len(selected) < 2:
                    other_sents = [t for t in clist if t not in selected]
                    other_sents.sort(key=lambda x: x[1] if x[1] is not None else 0, reverse=True)
                    selected.extend(other_sents[:2 - len(selected)])
                for sent, _ in selected:
                    if sent:
                        positive_examples[kp].append(sent)

        # unmatched_phrases = [
        #     kp for kp in self.keyphrases 
        #     if kp not in positive_examples and kp not in negative_examples
        # ]

        unmatched_phrases = [
            kp for kp in self.keyphrases 
            if not positive_examples.get(kp) and not negative_examples.get(kp)
        ]

        # Also filter empty lists if preferred
        positive_examples = {k: v for k, v in positive_examples.items() if v}
        negative_examples = {k: v for k, v in negative_examples.items() if v}
# 
        # print(f"Removed stats: {removed_stats}")

        return positive_examples, negative_examples, unmatched_phrases


    def process(self, text, title, abstract):
        paragraphs = [p.strip() for p in re.split(r"\|\|", text) if p.strip()]
        all_sentences = []
        bounds = []
        idx = 0
        for para in paragraphs:
            sents = [s.strip() for s in re.split(r"\|", para) if s.strip()]
            all_sentences.extend(sents)
            bounds.append((idx, idx + len(sents) - 1))
            idx += len(sents)

        important_indexes = set()
        for start, end in bounds:
            length = end - start + 1
            if length >= 1:
                important_indexes.add(start)
            if length >= 2:
                important_indexes.add(start + 1)
            if length >= 3:
                important_indexes.add(end)
            if length >= 4:
                important_indexes.add(end - 1)

        # Get positives, negatives, unmatched
        positive, negative, unmatched = self.filter_sentences(
            all_sentences,
            title,
            abstract,
            phrase_counts=None,
            important_indexes=important_indexes
        )
        # print(f"from filter_sentences function : {unmatched =}")
        # Optionally reconstruct filtered paragraphs from POSITIVES
        positive_flat = []
        for kp, sents in positive.items():
            positive_flat.extend(sents)
        positive_flat = list(dict.fromkeys(positive_flat))  # Deduplicate, preserve order

        filtered_paragraphs = []
        curr_idx = 0
        for start, end in bounds:
            para_range = set(all_sentences[start:end+1])
            para_sents = [s for s in positive_flat if s in para_range]
            if para_sents:
                filtered_paragraphs.append(" | ".join(para_sents))

        # print("\nPOSITIVE keyphrase->sentences mapping:")
        # for kp, sents in positive.items():
            # if sents:
                # print(f"{kp}: {sents}")
        # print("\nNEGATIVE keyphrase->sentences mapping:")
        # for kp, sents in negative.items():
            # if sents:
                # print(f"{kp}: {sents}")
        # print("\nUnmatched keyphrases:")
        # print(unmatched)

        return positive, negative, unmatched
    

# Unmatched: ['dft', 'formation energy', 'super p', 'ball milling',  'clusters', 'coulombic efficiency', 'current density', 'dispersions', 'electrochemical cells', 'electrochemical impedance', 'energy barrier', 'equivalent circuits', 'interface', 'ion transport', 'lattice parameters', 'microstructure', 'solid state reaction', 'vacancies', ]

# title+abstract = 'cathodes','sodium-ion batteries', 'wetting'

# interface # Journal of Energy Chemistry 105 (2025) 252â€“260 Fig. 1. (a) Schematic illustration of the interface reconstruction process of CNMT based on H 3 BO 3 . (b) XRD patterns of CNMT, CNMT-B, CNMT-3B, and CNMT-6B. (c) Schematic diagram of crystal structure from CNMT to CNMT-3B. 

## stability ##These synergistic effects enable the highly stable operation of CNMT-3B at a high charge cutoff voltage of 4.5 V, delivering a high discharge capacity of 149.1 mAh g âˆ’ 1 at 10 mA g âˆ’ 1 and achieving substantially improved cycling stability, with a capacity retention of 86.4% at 100 mA g âˆ’ 1 after 200 cycles.   --DONE
# 'lattice parameters' --> 'lattice parameter'
# phase transition   ---> phase transitions
# vacancies ---> vacancy
# len(unmatched)=18


if __name__ == "__main__":
    remover = NoisySentenceRemover(
        keyphrases=['DFT', 'Formation energy', 'Melting', 'Super P', 'XPS spectra', 'ball milling', 'bond length', 'capacity', 'cathodes', 'clusters', 'coulombic efficiency', 'current density', 'diffusion', 'dispersions', 'electrochemical cells', 'electrochemical impedance', 'energy barrier', 'equivalent circuits', 'heating', 'interface', 'ion transport', 'lattice parameters', 'microstructure', 'phase transition', 'sodium-ion batteries', 'solid state reaction', 'stability', 'surface modification', 'vacancies', 'wetting'],

        ## lattice parameters','vacancies','phase transition', ---> check for singular/plurals of keyphrases.

        similarity_threshold=0.2,
        keyphrase_sim_threshold=0.8
    )

    title = "Melting plus reactive wetting of solid acid enabling stable high-voltage cycling of layered oxide cathodes for sodium-ion batteries"
    abstract = """Expanding the cutoff voltage of layered oxide cathodes for sodium-ion batteries (SIBs) is crucial for overcoming their existing energy d. limitations. | However, cationic/anodic redox-triggered multiple phase transitions and unfavorable interfacial side reactions accelerate capacity and voltage decay. | Herein, we present a straightforward melting plus reactive wetting strategy using H3BO3 for surface modification of O3-type Na0.9Cu0.12Ni0.33Mn0.4Ti0.15O2 (CNMT). | The transformation of H3BO3 from solid to liquid under mild heating facilitates the uniform dispersion and complete surface coverage of CNMT particles. | By neutralizing the residual alkali and extisting Na+ from the CNMT lattice, H3BO3 forms a multifunctional Na2B2O5-dominated layer on the CNMT surface. | This NaxByOz (NBO) layer plays a positive role in providing low-barrier Na+ transport channels, suppressing phase transitions, and minimizing the generation of O2/CO2 gases and resistive byproducts. | As a result, at a charge cutoff voltage of 4.5V, the NBO-coated CNMT delivers a high discharge capacity of 149.1 mAhg-1 at 10 mAg-1 and exhibits excellent cycling stability at 100 mAg-1 over 200 cycles with a higher capacity retention than that of pristine CNMT (86.4%). | This study highlights the effectiveness of surface modification using low-melting-point solid acids, with potential applications for other layered oxide cathode materials to achieve stable high-voltage cycling. | This proposed strategy opens new avenues for the construction of high-quality coatings for high-voltage layered oxide cathodes in SIBs."""

    text = """ Results and discussion  An O3-type CNMT cathode material with excellent performance  at high voltage was synthesized using a high-temperature solidstate method, which was selected as a representative system to  demonstrate the effectiveness of the proposed surface engineering  strategy. | The surface modification of CNMT was achieved using  solid-state H 3 BO 3 with a low melting point, as schematically illustrated in Fig. 1 (a). | H 3 BO 3 powder was mechanically mixed with  CNMT and subsequently heated to 300 Â° C. | During this process,  H 3 BO 3 transitioned from a solid to a liquid state ( Fig. S1 ). | The high  diffusivity of liquefied H 3 BO 3 enabled uniform surface coverage of  CNMT primary particles and penetration into grain boundaries. | In  addition to neutralizing surface residual alkali, H 3 BO 3 extracted  Na +  from the CNMT lattice, generating a specialized NBO layer that  facilitates rapid Na +  transport and enhances interfacial stability. | Surface-modified CNMT samples were prepared by adding different proportions of H 3 BO 3 (1, 3, and 6 wt% of CNMT) and were  labeled as CNMT-x B, where x represents the mass percentage of  H 3 BO 3 added. | The inductively coupled plasma optical emission  spectroscopy (ICP-OES) analyses confirm that the actual elemental  molar ratios in CNMT, CNMT-B, CNMT-3B, and CNMT-6B accord  with the designed chemical formulas ( Table S1 ). | Fig. 1 (b) displays the X-ray diffraction (XRD) patterns of CNMT,  CNMT-B, CNMT-3B, and CNMT-6B. | All diffraction peaks align with  the O3-type structure with the R-3 m space group. | The absence of  distinct diffraction peaks for boron species likely stems from their  low content and crystallinity. | Rietveld refinements ( Fig. S2 ) were  performed, and the refined crystallographic data are summarized  in Table S2 . | The (003) peaks of CNMT-B and CNMT-3B shift to  lower angles compared with those of CNMT, indicating an increase  in the lattice parameter c due to Na +  extraction from the CNMT lattice by H 3 BO 3 . | This improvement can be attributed to the selective  extraction of Na +  from the Na-rich layers by H 3 BO 3 , which balances  the Na content across the different layers. | However, increasing the  H 3 BO 3 content to 6 wt% (CNMT-6B) causes the (003) peak to shift  back to higher angles, indicating a decrease in the lattice parameter  c . | Lattice parameter a consistently decreases with increasing H 3 BO 3 addition, signifying  TMâ€“O bond contraction associated with TM oxidation induced by  Na +  extraction. | Specifically,  compared  with  pristine  CNMT,  CNMT-3B exhibits an increased Naâ€“O bond length (from 2.428 to  2.449 Ã…) and a decreased TMâ€“O bond length (from 1.962 to  1.946 Ã…) ( Fig. 1 c). | This result indicates that the increase in lattice  parameter c stems from the expansion of Na layers, which is favorable for enhancing Na +  transport kinetics. | Under the same synthesis conditions, the phase composition of NBO is found to correlate  strongly with the Na/B ratio of the starting materials. | At higher  Na/B molar ratios, H 3 BO 3 tends to polymerize into Na 4 B 2 O 5 , whereas  NaBO 2 is formed at a Na/B molar ratio of 1:1 ( Fig. S3 ). | Given the high  Na/B molar ratio in CNMT-x B, Na 4 B 2 O 5 is likely the predominant  component in the interfacial layer. | This layer acts as a reservoir for  storing and releasing electrochemically active Na + . | Scanning electron microscopy (SEM) images of CNMT and  CNMT-3B are presented in Fig. 1 (d and e). | Both samples exhibit  particles with irregular morphologies, being measured to be several micrometers in size. | The fine particles observed on the surface  of CNMT are attributed to residual alkali formed after exposure to  humid air. | Following the addition of H 3 BO 3 , the residual alkali on  the CNMT particle surfaces is neutralized and transformed into  NBO coating layers, resulting in smoother particle surfaces for  CNMT-3B. | Energy-dispersive X-ray spectroscopy (EDS) mapping  images confirm a uniform elemental distribution in the surface  layer of the CNMT-3B particles ( Fig. S4 ). | However, the detection  sensitivity of boron in EDS is generally low because of its low characteristic X-ray energy. | The microstructures of CNMT and CNMT- 3B were further analyzed by high-resolution transmission electron  microscopy (HRTEM). | A residual alkali layer with a thickness of  about 9 nm can be observed on the surface of CNMT particle  ( Fig. S5 ). | Fig. 1 (f) demonstrates that the melting and diffusion of  H 3 BO 3 result in the formation of a uniform and intact coating layer  with a thickness ranging from 18 to 29 nm on the surface of CNMT- 3B particles. | Zoomed-in images of two distinct surface regions  ( Fig. 1 g and Fig. S6 ) reveal lattice fringes with an interplanar spacing of 0.247 nm, corresponding to the (112) plane of Na 4 B 2 O 5 in the  surface coating layer. | In contrast, lattice fringes with an interplanar  spacing of 0.540 nm, corresponding to the (003) plane of the O3  phase, are observed in the interior regions of CNMT-3B. | Journal of Energy Chemistry 105 (2025) 252â€“260  Fig. 1. (a) Schematic illustration of the interface reconstruction process of CNMT based on H 3 BO 3 . (b) XRD patterns of CNMT, CNMT-B, CNMT-3B, and CNMT-6B. (c) Schematic  diagram of crystal structure from CNMT to CNMT-3B. | SEM images of (d) CNMT and (e) CNMT-3B. (f) TEM and (g) HRTEM images of CNMT-3B. | The influence of surface modification on the elemental oxidation states of CNMT and CNMT-3B was investigated using X-ray  photoelectron spectroscopy (XPS). | Fig. 2 (a) shows the B 1 s XPS  spectra of CNMT-3B on the surface and at a depth of 50 nm. | The  B 1 s peak intensity at a depth of 50 nm is only slightly lower than  that on the surface, which may be related to the diffusion of boron  into the grain boundaries. | Fig. 2 (b and c) displays the Ni 2 p and Mn  2 p XPS spectra of CNMT and CNMT-3B on the surface. | In  CNMT-3B, Ni 2+ /Ni 3+  (66.1/33.9) and Mn 3+ /Mn 4+  (35.2/64.8) ratios  are lower than those of CNMT (Ni 2+ /Ni 3+  = 70.8/29.2, Mn 3+ /Mn 4+  = 37.9/62.1). | This increase in the valence states of Ni and Mn is  attributed to charge compensation triggered by Na +  extraction  from the O3 phase during the formation of the NBO layer. | At a  depth of 50 nm, the Ni 2+ /Ni 3+  and Mn 3+ /Mn 4+  ratios in CNMT-3B  revert to higher values ( Fig. S7 ), further confirming that Na +  extraction primarily occurs in the surface layer of the O3 phase. | 254  The time-of-flight secondary-ion mass spectrometry (TOFSIMS) technique, known for its high chemical selectivity, was used  to locate and identify specific surface species on CNMT and CNMT- 3B, with a particular focus on light boron. | As illustrated in the TOFSIMS chemical mapping images and three-dimensional (3D) view  of depth profiles ( Fig. 2 d and e and Fig. S8a ), the NiO 2  âˆ’ fragments  have a similar spatial distribution to the NaB 2 O 4  âˆ’ and NaBO 2  âˆ’ fragments, confirming that the NBO coating layer is uniformly distributed over the surfaces of all CNMT-3B particles. | Notably, the  signal intensity of NaB 2 O 4  âˆ’ is significantly higher than that of  NaBO 2  âˆ’ , indicating a propensity for the boron component in the  coating layer to polymerize into Na 4 B 2 O 5 ( Fig. 2 f). | The TOF-SIMS  depth profiles of several key fragments acquired from CNMT and  CNMT-3B are shown in Fig. 2 (g and h) (absolute intensity) and  Fig. S8 (b and c) (normalized to maximum). | CNMT is prone to react  with moisture and carbon dioxide in air, forming a layer of residual  sodium species with high alkalinity on its surface that mainly consists of Na 2 CO 3 , NaOH, and NaHCO 3 . | The NaCO 3  âˆ’ fragment originates from Na 2 CO 3 , while the NaO 2 H âˆ’ fragment is related to  NaOH, NaHCO 3 , and Na 2 CO 3 x H 2 O. | For CNMT, the signal intensities  of residual alkali species (NaCO 3  âˆ’ and NaO 2 H âˆ’ ) decrease as sputtering time increases, whereas the bulk signal (NiO 2  âˆ’ ) is intensified,  confirming the presence of alkaline substances on the surface. | After surface modification, the intensities of NaCO 3  âˆ’ and NaO 2 H âˆ’  fragments in the surface region of CNMT-3B are significantly  reduced compared to those of CNMT, demonstrating effective  residual alkali removal by H 3 BO 3 . | Journal of Energy Chemistry 105 (2025) 252â€“260  Fig. 2. (a) B 1 s XPS spectra of CNMT-3B on the surface and at the depth of 50 nm. (b) Ni 2 p and (c) Mn 2 p XPS spectra of CNMT and CNMT-3B on the surface. (d) TOF-SIMS  chemical mapping and (e) 3D view of depth profiles for NiO 2  âˆ’ and NaB 2 O 4  âˆ’ fragments acquired from CNMT-3B. (f) TOF-SIMS depth profiling (absolute intensity) of NaBO 2  âˆ’ and  NaB 2 O 4  âˆ’ fragments acquired from CNMT-3B. | TOF-SIMS depth profiling (absolute intensity) of NaCO 3  âˆ’ and NaO 2 H âˆ’ fragments acquired from (g) CNMT and (h) CNMT-3B. | The electrochemical performance of CNMT and CNMT-3B was  evaluated in coin-type half-cells. | Fig. 3 (a) displays the chargedischarge curves for CNMT and CNMT-3B during the first, second,  and fifth cycles at 10 mA g âˆ’ 1 . | CNMT delivers initial discharge  capacities of 146.0 and 151.9 mAh g âˆ’ 1  in voltage ranges of 2.2â€“  4.3 and 2.2â€“4.5 V, respectively. | However, CNMT exhibits significant discharge capacity and voltage decay within just five cycles,  highlighting its poor electrochemical reversibility at high charge  cutoff voltages. | In contrast, the introduction of the NBO surface  layer reduces the initial discharge capacities of CNMT-3B to  137.3 and 149.1 mAh g âˆ’ 1  for the voltage ranges of 2.2â€“4.3 and  2.2â€“4.5 V, respectively, because of the increased material mass. | Nevertheless, CNMT-3B exhibits a markedly improved initial  Coulombic efficiency compared with CNMT. | The slightly higher initial charge voltage of CNMT-3B is attributed to the higher valence  states of Ni and Mn, which result from the prechemical extraction  of Na +  by H 3 BO 3 , as corroborated by the XPS results. | The corresponding differential capacity (d Q /d V ) plots of CNMT and CNMT- 3B at 10 mA g âˆ’ 1  across the voltage ranges of 2.2â€“4.3 and 2.2â€“  4.5 V are shown in Fig. 3 (b). | Redox peaks are primarily observed  in two voltage regions: 2.4â€“3.0 and >3.6 V. | For CNMT, the d Q /d V plots at  >3.6 V reveal poor electrochemical reversibility, which is attributed  to the irreversible release of lattice oxygen at high voltages. | This  contributes significantly to the poor cycling stability of CNMT. | In  contrast, the d Q /d V plots of CNMT-3B in the voltage range above  3.6 V exhibit a high degree of overlap, indicating substantially  improved electrochemical reversibility of lattice oxygen. | 255  Fig. 3 (c) shows the cycling performance of CNMT, CNMT-B,  CNMT-3B, and CNMT-6B at 100 mA g âˆ’ 1  within a voltage range of  2.2â€“4.3 V. | CNMT-3B achieves a capacity retention of 88.1% over  200 cycles, outperforming CNMT (76.5%), CNMT-B (81.3%), and  CNMT-6B (87.6%). | When the voltage range is expanded to 2.0â€“  4.5 V for further examining the electrochemical reversibility under  a deep discharging/charging scenario, CNMT-3B maintains a higher  capacity retention of 86.4% compared with 62.1% of CNMT over 200  cycles at the same current density ( Fig. 3 d). | The corresponding  d Q /d V plots indicate that the electrochemical polarization of  CNMT-3B increases more slowly with cycling than that of CNMT  ( Fig. S9 ). | In addition, CNMT-3B exhibits excellent long-term cycling  stability at 500 mA g âˆ’ 1 , achieving a capacity retention of 85.6% over  500 cycles ( Fig. 3 e). | This cycling performance surpasses that of  many previously reported layered oxide cathode materials for SIBs  ( Table S3 ). | Fig. 3 (f) and Fig. S10 show the rate capability of CNMT,  CNMT-B, CNMT-3B, and CNMT-6B. | CNMT-3B delivers the highest  average discharge capacity of 84.6 mAh g âˆ’ 1  at 2000 mA g âˆ’ 1 . | Moreover, as the current density increases from 20 to 2000 mA g âˆ’ 1 ,  CNMT-3B retains 60.3% of its discharge capacity, outperforming  CNMT (50.7%), CNMT-B (56.6%), and CNMT-6B (51.8%). | Journal of Energy Chemistry 105 (2025) 252â€“260  Fig. 3. (a) Charge-discharge curves and (b) corresponding d Q /d V plots of CNMT and CNMT-3B at 10 mA g âˆ’ 1  in the voltage range of 2.2â€“4.3 V and 2.2â€“4.5 V. | Cycling  performance of CNMT, CNMT-B, CNMT-3B, and CNMT-6B at 100 mA g âˆ’ 1  in the voltage range of (c) 2.2â€“4.3 V and (d) 2.0â€“4.5 V. (e) Cycling performance of CNMT-3B at 500 mA  g âˆ’ 1 . (f) Rate capability of CNMT, CNMT-B, CNMT-3B, and CNMT-6B. | This enhanced rate capability is attributed to the improved Na +  diffusion kinetics of CNMT-3B, as confirmed by galvanostatic intermittent titration technique (GITT) and electrochemical impedance  spectroscopy (EIS) tests. | Fig. S11 (aâ€“e) shows the GITT curves and  Na +  diffusion coefficients of CNMT and CNMT-3B in the 100th  cycle. | The average Na +  diffusion coefficients for CNMT-3B are calculated as 3.39 Ã— 10 âˆ’ 11  and 3.24 Ã— 10 âˆ’ 11  cm 2  s âˆ’ 1  during charge  and discharge processes, respectively, which are significantly  higher  than  the  values  for  CNMT  (2.61  Ã—  10 âˆ’ 11  and  1.87 Ã— 10 âˆ’ 11  cm 2  s âˆ’ 1 ). | Fig. S11(f) shows the Nyquist plots of CNMT  and CNMT-3B after 100 cycles. | The cathode-electrolyte interphase  (CEI) ( R CEI ) and charge transfer resistances ( R ct ) of CNMT-3B are  106 and 662 X , respectively, which are much lower than those of  CNMT (277 and 1088 X ). | Encouraged by the superior electrochemical performance of CNMT-3B in half-cells, its performance was  further evaluated and compared with that of CNMT in full cells  using commercial hard carbon as the anode ( Fig. S12a ). | The  CNMT-3B full cell exhibits reduced voltage and capacity decay during cycling compared with the CNMT full cell ( Fig. S12b and c ). | After 100 cycles, the CNMT-3B full cell achieves a discharge capacity of 111.7 mAh g âˆ’ 1  with a capacity retention of 88.1%, which significantly exceeds the 98.1 mAh g âˆ’ 1  discharge capacity and 77.7%  retention of the CNMT full cell ( Fig. S12d ). | 256  In situ XRD measurements were conducted to investigate the  structural evolution of CNMT and CNMT-3B during cycling within  a voltage range of 2.0â€“4.4 V, as shown in Fig. 4 (a and b) and  tural  reversibility  of  CNMT-3B  during  repeated  sodiation/  desodiation. | Journal of Energy Chemistry 105 (2025) 252â€“260  Fig. 4. | Contour plots of in situ XRD patterns of (a) CNMT and (b) CNMT-3B during the first cycle. | Evolution of lattice parameters of (c) CNMT and (d) CNMT-3B during the first  cycle. | The appearance of the monoclinically distorted Oâ€² 3 phase as an intermediate  is attributed to the Na + /vacancy ordering effect. | For CNMT-3B, the  Oâ€² 3 intermediate phase appears over a narrower voltage range  compared with that for CNMT. | This behavior can be explained by  the partial prechemical extraction of Na +  by H 3 BO 3 and local TM  oxidation in the surface layer, which disrupts Na + /vacancy ordering. | Significant differences are observed between the two samples  in the high-voltage region. | This intermediate phase is likely caused by the strong  Jahn-Teller effect associated with the Ni 4+ /Ni 3+  redox reaction  and the generation of anionic redox-mediated oxygen vacancies,  leading to structural distortion of the TM layers in CNMT. | In contrast, CNMT-3B transitions directly from the P3 phase to the OP2  phase without forming the Pâ€² 3 intermediate phase. | Furthermore,  the OP2 phase persists longer in the high-voltage region of  CNMT-3B, suggesting reduced gliding resistance of the TM layers. | This improved behavior can be attributed to the suppression of  anionic-redox-induced microstructural degradation by the NBO  surface layer. | Upon discharging <2.70 V, CNMT shows the reappearance of the Oâ€² 3 intermediate phase during the P3-O3 phase  transition. | However, the diffraction peaks corresponding to the Oâ€²  3 phase do not reappear in CNMT-3B, indicating a more direct  structural evolution. | Overall, CNMT-3B undergoes fewer phase  transitions than CNMT, which is favorable for improving the struc- 257  Fig. 4 (c and d) shows the evolution of the lattice parameters of  CNMT and CNMT-3B during cycling. | Upon initial Na +  extraction,  the (003) and (006) peaks shift to lower angles, corresponding to  an increase in the lattice parameter c , because of the increased  repulsion between adjacent TM layers and the O3-P3 phase transition. | The maximum increase in lattice parameter c is 4.66% for  CNMT-3B, which is lower than the 5.72% observed for CNMT. | This smoother  gliding reduces repulsion between adjacent TM layers and suppresses the formation of undesired monoclinically distorted intermediate phases. | Upon further charging to 4.4 V, the (003) and  (006) peaks of both samples shift to higher angles, indicating a  reduction in the lattice parameter c . | For CNMT-3B, this reduction  reaches a maximum of 2.12% relative to the pristine state, which  is smaller than the maximum increase upon initial Na +  extraction. | In addition, the difference between the maximum values of lattice  parameter c during charge and discharge processes for CNMT-3B is  1.24%, smaller than that for CNMT, reflecting the improved structural reversibility due to the NBO surface modification. | The (101)  and (012) peaks shift to higher angles for both samples, indicating  a decrease in lattice parameter a due to TM oxidation during Na +  extraction. | CNMT-3B shows a continuous contraction of 3.78% in  lattice parameter a , which is slightly smaller than the contraction  of 3.89% for CNMT. | The reduced fluctuations in the lattice parameters a and c contribute to the enhanced structural reversibility  and mechanical stability of CNMT-3B. | Journal of Energy Chemistry 105 (2025) 252â€“260  In situ differential electrochemical mass spectrometry (DEMS)  was used to monitor gas release during the initial chargedischarge process ( Fig. 5 a and b). | Electrochemical cells with CNMT  and CNMT-3B cathodes were cycled at 20 mA g âˆ’ 1  within a voltage  range of 2.2â€“4.5 V. | The primary gaseous products detected were O 2  and CO 2 . | O 2 gas is attributed to the irreversible release of lattice  oxygen, whereas CO 2 evolution results from the decomposition of  residual Na 2 CO 3 and the electrolyte. | For CNMT, O 2 and CO 2 gases  start evolving at 3.65 and 3.85 V, respectively, with their evolution  rates increasing and peaking at 4.27 V. | In contrast, the onset voltages for O 2 and CO 2 evolution in CNMT-3B are significantly delayed  to 4.11 V. | Throughout the charge-discharge process, CNMT-3B  releases 51.3 nmol of O 2 and 34.8 nmol of CO 2 per milligram, which  are substantially lower than the 203.9 nmol of O 2 and 108.8 nmol  of CO 2 released per milligram of CNMT. | The chemical compositions of the CEI films on the CNMT and  CNMT-3B cathodes after 50 cycles were analyzed by XPS ( Fig. 5 c  and d). | For both cathodes, the signals of Oâ€“C @ O and Oâ€“C  bonds decrease with increasing detection depth, whereas the  Naâ€“F bond signal is intensified, suggesting that organic species  are concentrated in the outer layer and that inorganic species  dominate the inner layer of the CEI film. | The CEI peaks for  CNMT-3B are weaker than those for CNMT on the surface and at  a depth of 20 nm, indicating the formation of a thinner CEI film  on CNMT-3B. | Furthermore, to examine the effect of NBO surface  modification on the dissolution of TM ions, separators from cycled  cells containing CNMT and CNMT-3B cathodes were analyzed  using ICP-OES. | For both samples, more nickel ions are dissolved  into the electrolyte than manganese ions. | Nickel ion dissolution  is significantly suppressed in CNMT-3B than in CNMT ( Table S4 ). | The reduction in interfacial side reactions for CNMT-3B is attributed to the elimination of residual alkali and the formation of a  uniform NBO coating. | In situ DEMS analysis of O 2 and CO 2 gases evolved from the electrochemical cells with the (a) CNMT and (b) CNMT-3B cathodes during the initial charge-discharge  process. (c) O 1 s and (d) F 1 s XPS spectra of the CNMT and CNMT-3B cathodes after 50 cycles detected on the surface and at the depth of 20 nm. (e) XRD patterns of the CNMT  and CNMT-3B cathodes before and after 100 cycles. | SEM images of the (f) CNMT and (g) CNMT-3B cathodes before and after 100 cycles. | Journal of Energy Chemistry 105 (2025) 252â€“260  Fig. 6. | Formation energies of oxygen vacancies in (a) CNMT and (b) CNMT-3B. (c) Na +  migration energy barriers in Na 4 B 2 O 5 and Na 2 CO 3 . | To further evaluate the effect of NBO surface modification on  the structural reversibility of the bulk material, the crystal structures and particle morphologies of the CNMT and CNMT-3B cathodes before and after 100 cycles at 100 mA g âˆ’ 1  were compared. | As shown in Fig. 5 (e), the cycled CNMT cathode retains the (003)  diffraction peak of the P3 phase, and the (003) diffraction peak of  the O3 phase shifts significantly to a lower angle compared with  that of the pristine CNMT. | In contrast, the diffraction peaks of  the O3 phase for the cycled CNMT-3B cathode return to their original positions without the appearance of P3 phase peaks, indicating  higher structural reversibility for CNMT-3B during cycling compared with that for CNMT. | SEM images reveal severe intergranular  delamination cracking in cycled CNMT primary particles, likely  caused by low reversibility of TM layer gliding. | Such cracks compromise the integrity of the CEI film, exposing fresh surfaces to  the electrolyte and promoting the accumulation of resistive  byproducts from electrolyte decomposition. | In contrast, the cycled  CNMT-3B particles maintain structural integrity with substantially  reduced cracking, preventing electrolyte infiltration and inhibiting  the formation of thick CEI films. | To understand the effect of NBO surface modification on oxygen evolution and electrochemical reaction kinetics, DFT calculations were conducted for CNMT and CNMT-3B. | In the structural  model for CNMT ( Fig. S14a ), the vacancy formation energies at  five oxygen sites adjacent to various TM elements (Ov1-NiNiTi,  Ov2-NiNiMn, Ov3-NiCuMn, Ov4-NiNiMn, and Ov5-NiMnTi) were  calculated. | Among these sites, the NiNiTi oxygen site exhibited  the lowest vacancy formation energy of 0.74 eV ( Fig. 6 a), indicating that oxygen at this site is the most susceptible to irreversible  oxidation and escaping from the lattice. | Consequently, the NiNiTi  oxygen site was selected for further evaluation of the effect of  NBO surface coating. | The structural model of CNMT-3B is shown  in Fig. S14(b) . | With the introduction of NBO on the surface, the  vacancy formation energy at the NiNiTi oxygen site increases significantly to 1.86 eV in CNMT-3B ( Fig. 6 b). | This increase indicates  that the NBO surface coating greatly enhances the stability of lattice oxygen, thereby reducing its irreversible release. | These findings are consistent with the DEMS results. | To elucidate the  improved electrochemical reaction kinetics resulting from the  conversion of residual alkali into NBO, the nudged elastic band  method was used to compute the minimum reaction pathways  ( Fig. S14c and d ) and the corresponding energy barriers ( Fig. 6 c)  in the crystal structures of Na 4 B 2 O 5 and Na 2 CO 3 . | The Na +  diffusion  barrier in Na 4 B 2 O 5 was calculated to be 0.20 eV, which is favorable for rapid Na +  diffusion and smaller than the 0.26 eV of barrier in Na 2 CO 3 . | The improved interfacial Na +  transport kinetics is favorable for enhancing the electrochemical performance of CNMT-3B. || Experimental section  Experimental  details  can  be  found  in  the  Supporting  Information . | CRediT authorship contribution statement  Debin Ye: Writing â€“ original draft, Methodology, Investigation,  Data curation, Conceptualization. | Xuehang  Wu: Writing â€“ review & editing, Supervision, Project administration, Methodology, Funding acquisition, Formal analysis, Data  curation, Conceptualization. | Declaration of competing interest  The authors declare that they have no known competing financial interests or personal relationships that could have appeared  to influence the work reported in this paper. || Conclusions  A facile and efficient strategy using H 3 BO 3 has been developed  to simultaneously construct a stable surface and regulate the bulk  structure of the O3-type CNMT cathode material. | This approach  involves the liquefaction and dispersion of solid-state H 3 BO 3 via  mild heating followed by an acid-base neutralization reaction. | The strong driving force of the interfacial chemical reaction  ensures the formation of a uniform NBO coating. | The NBO surface  coating layer not only creates specialized channels that enhance  interfacial Na +  transport kinetics but also mitigates multiple phase  transitions by improving the ordering of the layered structure and  suppressing Na + /vacancy ordering. | Furthermore, it reduces the  irreversible release of oxygen and limits the reactivity between  highly active O n âˆ’ species and the electrolyte at high voltages. | These  synergistic effects enable the highly stable operation of CNMT-3B  at a high charge cutoff voltage of 4.5 V, delivering a high discharge  capacity of 149.1 mAh g âˆ’ 1  at 10 mA g âˆ’ 1  and achieving substantially  improved cycling stability, with a capacity retention of 86.4% at  100 mA g âˆ’ 1  after 200 cycles. | This study underscores the effectiveness of surface modification using low-melting-point solid acids,  demonstrating the significant potential for extension to other layered oxide cathode materials to achieve stable high-voltage  cycling."""

    # filtered, phrase_sent_map, unmatched = remover.process(text, title, abstract)

    # print("\nFiltered sentences:")
    # for sent in filtered:
    #     print(f"- {sent}")

    # print("\nMatched phrase -> sentences mapping:")
    # for phrase, sents in phrase_sent_map.items():
    #     print(f">>{phrase}:--> {sents}")
    #     # for s in sents:
    #     #     print(f"  - {s}")

    # print("\nUnmatched phrases:")
    # print(unmatched)
    positive, negative, unmatched = remover.process(text, title, abstract)

    print(f"Positive: {positive}")
    print("="*100)
    print(f"Negative: {negative}")
    print("="*100)
    print(f"Unmatched: {unmatched}")
    print(f"{len(unmatched)=}")

