# 🚀 BERT Training Dataset Creation Pipeline

## 📋 Overview

This pipeline creates a complete training dataset for BERT keyphrase extraction model from PDF documents and curated data.

## 🔄 Pipeline Steps

### Step 1: PDF Segmentation
- **Module**: `get_segmented_title_abs_data_test1.py`
- **Function**: Extract title, abstract, and other sections from PDFs
- **Output**: Segmented text data in Excel format

### Step 2: Sentence Filtering
- **Module**: `get_cth_from_sentences_v1_optimized.py` 
- **Function**: Filter and rank sentences containing keyphrases
- **Output**: Ranked sentences for title/abstract and other sections

### Step 3: Dataset Tagging
- **Module**: `dataset_module_v1.py`
- **Function**: Create IOB tags for BERT training
- **Output**: Tagged dataset with tokens and labels

### Step 4: Training Preparation
- **Function**: Combine all tagged files for model training
- **Output**: Final training dataset ready for BERT

## 🚀 Usage

### Basic Usage:
```python
from get_training_tagged_dataset import get_model_training_data

section_code = "52"
curator_data_folder = "path/to/curated/data"
pdf_folder = "path/to/pdfs"
abbr_full_form_excel = "path/to/abbreviations.xlsx"

final_result, success = get_model_training_data(
    section_code, curator_data_folder, pdf_folder, abbr_full_form_excel
)
```

### Command Line:
```bash
python get_training_tagged_dataset.py
```

## 📁 Output Structure

```
keyphrase_extraction_optimized/
├── cth_sent_excel_data/
│   ├── title_abstract_sentences_final_ranked.xlsx
│   ├── other_section_sentences_final_ranked.xlsx
│   └── rejected_sentences_analysis.xlsx
├── processed_datasets/
│   ├── *_debug_tags.xlsx (tagged files)
│   └── combined_tagged_dataset_for_training.xlsx
└── final_training_data/
    └── all_keyphrases_combined_tagged_data.xlsx
```

## 🔧 Configuration

Edit the configuration in `__main__` section:

```python
section_code = "52"
curator_data_folder = "path/to/curated/data"
abbr_full_form_excel = "path/to/abbreviations.xlsx"
pdf_folder = "path/to/shipments/folder"
```

## 📊 Next Steps

After pipeline completion, use the training module:

```bash
python train_module_with_tagged_data_input.py
```

The pipeline creates a complete dataset ready for BERT model training with proper IOB tagging for keyphrase extraction.