"""
This module contains functions for extracting and processing experimental procedures from scientific papers.
It provides functionality to clean and format experimental text content, handling various text formatting
issues like line breaks and spacing to produce standardized output.
Author: <PERSON>
Date: 06-12-24
"""

import re


def get_experimental(content_list, experimental_location):
    """
    Extract the experimental content from the experimental section of a paper.
    Args:
        experimental_list (list): List of strings from the experimental section.

    Returns:
        str: A string containing the experimental content.
    """
    section_content = {}
    full_text = " "
    # print(f"{content_list =}")
    if content_list:
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text += item

        section_content["experimental_procedure"] = full_text.strip()
        section_content["page_number"] = experimental_location

        return section_content
    else:
        return full_text
