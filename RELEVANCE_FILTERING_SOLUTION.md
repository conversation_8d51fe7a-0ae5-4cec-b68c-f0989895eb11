# Relevance Filtering Solution for Sentence Reduction

## Problem Statement

The production pipeline was extracting too many sentences, especially from segments other than "title" and "abstract". This resulted in:
- Excessive number of predicted keyphrases
- Reduced quality due to inclusion of less relevant sentences
- Increased processing time for downstream tasks

## Solution Overview

Implemented a **RelevanceFilter** class that reduces sentence count by focusing on content most relevant to the title and abstract. The solution uses both keyword overlap and semantic similarity to score sentence relevance.

## Key Features

### 1. Dual Scoring Mechanism
- **Keyword Overlap**: Jaccard similarity between sentence terms and title/abstract terms
- **Semantic Similarity**: Cosine similarity using sentence transformers (optional)
- **Combined Score**: Weighted combination (40% keyword + 60% semantic by default)

### 2. Segment-Based Filtering
- **Priority Segments**: title > abstract > results > conclusion > introduction > methods
- **Different Limits**: More sentences from high-priority segments
- **Strict Filtering**: Higher thresholds for non-priority segments

### 3. Configurable Parameters
- Minimum relevance score threshold
- Maximum sentences per segment type
- Semantic similarity model selection
- Scoring weights and filtering multipliers

## Implementation Details

### RelevanceFilter Class
```python
class RelevanceFilter:
    def __init__(self, use_semantic_similarity=True, config=None):
        # Loads configuration from relevance_config.py
        # Initializes sentence transformer model if available
        
    def calculate_relevance_score(self, sentence, title_text, abstract_text):
        # Combines keyword overlap and semantic similarity
        
    def filter_sentences_by_relevance(self, sentences_data, title_text, abstract_text):
        # Applies relevance filtering with segment priorities
```

### Configuration System
- **relevance_config.py**: Centralized configuration management
- **Preset Configurations**: strict, moderate, lenient
- **Customizable Parameters**: All filtering parameters can be adjusted

### Integration Points
- **main_sentence_filter_final_test2.py**: Updated to use relevance filtering
- **process_single_tan_production()**: Extracts title/abstract first, then filters other segments
- **Automatic Statistics**: Shows filtering impact and segment distribution

## Configuration Options

### Default Configuration (Moderate)
```python
{
    'min_relevance_score': 0.1,
    'max_sentences_per_segment': {
        'title': 16,        # More from title
        'abstract': 16,     # More from abstract
        'results': 8,       # Moderate from results
        'conclusion': 8,    # Moderate from conclusion
        'introduction': 6,  # Fewer from introduction
        'methods': 4,       # Fewer from methods
        'default': 3        # Very few from other segments
    },
    'strict_filtering_multiplier': 2.0,  # 2x threshold for non-priority segments
    'keyword_weight': 0.4,
    'semantic_weight': 0.6
}
```

### Preset Configurations
- **Strict**: Higher thresholds, fewer sentences per segment
- **Moderate**: Balanced approach (default)
- **Lenient**: Lower thresholds, more sentences allowed

## Usage Examples

### Basic Usage
```python
# Uses default moderate configuration
relevance_filter = RelevanceFilter(use_semantic_similarity=True)
filtered_sentences = relevance_filter.filter_sentences_by_relevance(
    all_sentences, title_text, abstract_text
)
```

### Custom Configuration
```python
from relevance_config import get_config

# Use strict preset
config = get_config('strict')
relevance_filter = RelevanceFilter(config=config)
```

### Testing
```python
# Run test script to see filtering in action
python test_relevance_filtering.py
```

## Expected Results

### Sentence Reduction
- **Before**: 500-2000 sentences per TAN (depending on document length)
- **After**: 50-150 sentences per TAN (depending on configuration)
- **Reduction Ratio**: 70-90% reduction in sentence count

### Quality Improvement
- Higher relevance scores for retained sentences
- Better focus on content related to title and abstract
- Reduced noise from irrelevant segments

### Segment Distribution (Typical)
- **Title**: 10-16 sentences
- **Abstract**: 10-16 sentences  
- **Results**: 5-8 sentences
- **Conclusion**: 5-8 sentences
- **Other segments**: 2-4 sentences each

## Benefits

1. **Reduced Keyphrase Volume**: Fewer but more relevant keyphrases
2. **Improved Quality**: Focus on sentences most related to main content
3. **Faster Processing**: Less data for downstream keyphrase prediction
4. **Configurable**: Easy to adjust filtering aggressiveness
5. **Semantic Awareness**: Uses modern NLP for better relevance assessment
6. **Segment Intelligence**: Respects document structure and importance

## Dependencies

### Required
- pandas, numpy (already available)
- collections (standard library)

### Optional (for semantic similarity)
- sentence-transformers: `pip install sentence-transformers`
- torch (automatically installed with sentence-transformers)

### Fallback Behavior
- If sentence-transformers not available, uses keyword-only filtering
- Graceful degradation with warning messages

## Files Modified/Created

### Modified
- **main_sentence_filter_final_test2.py**: Added RelevanceFilter class and integration
- **process_single_tan_production()**: Updated to extract title/abstract and apply filtering

### Created
- **relevance_config.py**: Configuration management system
- **test_relevance_filtering.py**: Test script for the filtering system
- **RELEVANCE_FILTERING_SOLUTION.md**: This documentation

## Performance Considerations

### Memory Usage
- Sentence transformer model: ~90MB RAM
- Processing: Minimal additional memory overhead

### Processing Time
- Keyword filtering: Very fast (milliseconds)
- Semantic similarity: Moderate (1-2 seconds per TAN)
- Overall impact: Minimal compared to PDF extraction time

### Scalability
- Processes one TAN at a time (memory efficient)
- Can disable semantic similarity for faster processing
- Configuration allows trading quality vs speed

## Troubleshooting

### Common Issues
1. **sentence-transformers not installed**: Falls back to keyword-only filtering
2. **No title/abstract found**: Uses basic sentence limiting (first 50 sentences)
3. **Very few sentences retained**: Adjust min_relevance_score or use lenient preset

### Debugging
- Enable verbose output to see filtering statistics
- Check relevance scores in output Excel files
- Use test script to analyze filtering behavior

## Future Enhancements

1. **Domain-Specific Models**: Use scientific paper-specific sentence transformers
2. **Dynamic Thresholds**: Adjust thresholds based on document characteristics
3. **Multi-Language Support**: Extend to non-English documents
4. **Advanced Metrics**: Add more sophisticated relevance measures
5. **Caching**: Cache embeddings for repeated processing
