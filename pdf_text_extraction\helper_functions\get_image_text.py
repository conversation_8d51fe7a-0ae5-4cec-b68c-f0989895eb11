"""
This module provides functionality for extracting and cleaning text from PDF documents.

It includes a function to extract text from PDF files, identify figure captions,
and separate the main text content from figure-related text. The module uses the
PyMuPDF (fitz) library for PDF processing and regular expressions for text pattern matching.

Date 06-12-24
@author: <PERSON>
"""

# Import necessary modules
import fitz  # type: ignore
import re


def extract_and_clean_text(pdf_path):
    """
    Extracts and cleans text from a PDF file, separating main content from figure captions.

    This function processes a PDF document, extracting text from each page and identifying
    figure captions. It separates the main text content from figure-related text, cleaning
    the output by removing short lines associated with figures.

    Args:
        pdf_path (str): The file path of the input PDF document.

    """
    try:
        cleaned_text = []
        figure_texts = {}

        # Open the PDF document
        with fitz.open(pdf_path) as doc:
            for page_num, page in enumerate(doc):
                # Extract text from the page
                text = page.get_text("text")

                # Split text into lines
                lines = text.split('\n')
                lines_to_remove = set()
                figure_text_lines = []

                # Iterate over lines to find figure patterns and extract text above them
                for i, line in enumerate(lines):
                    fig_pattern = re.match(r'\bfig\.\s*\d+', line, re.IGNORECASE)
                    if fig_pattern:
                        # Collect lines starting from the figure pattern line
                        # figure_text_lines = []
                        for j in range(i - 1, -1, -1):
                            if len(lines[j].strip()) >= 50:
                                break
                            figure_text_lines.insert(0, lines[j])

                        # If no short sentences above, skip this figure line
                        if not figure_text_lines:
                            continue

                        # Check if the figure caption spans multiple lines
                        caption_lines = [i]
                        caption = line.strip()
                        while not caption.endswith('.') and i + 1 < len(lines):
                            i += 1
                            next_line = lines[i].strip()
                            caption += ' ' + next_line
                            caption_lines.append(i)
                            if next_line.endswith('.'):
                                break

                        # Add indices to lines_to_remove
                        lines_to_remove.update(figure_text_lines)
                        lines_to_remove.update(caption_lines)

                # Collect cleaned text
                cleaned_page_text = '\n'.join(line for idx, line in enumerate(lines) if idx not in lines_to_remove)
                cleaned_text.append(cleaned_page_text)

                # Collect figure text for the current page
                figure_texts[page_num + 1] = figure_text_lines

        # print(f"{figure_texts = }")
        # Transforming into the desired format
        transformed_figure_texts = {}

        for page_num, lines in figure_texts.items():
            # Filter out empty lines
            non_empty_lines = [line for line in lines if line.strip()]

            # Join non-empty lines into a single string
            transformed_figure_texts[page_num] = '\n'.join(non_empty_lines)
        # Join all cleaned pages' text
        # cleaned_text_str = '\n'.join(cleaned_text)
        transformed_figure_texts = {
            key: value
            for key, value in transformed_figure_texts.items()
            if value.strip()
        }
        return transformed_figure_texts
    except Exception as e:
        print(f"An error occurred: {e}")
        return {}
