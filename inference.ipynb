{"cells": [{"cell_type": "code", "execution_count": null, "id": "1ec55a3e", "metadata": {}, "outputs": [], "source": ["### training code copied from GPU commented out.....\n", "\n", "\n", "\n", "\n", "# \"\"\"\n", "# Complete BERT Keyphrase Extraction Training Module uses SciBERT\n", "# \"\"\"\n", "\n", "# import os\n", "# import re\n", "# import json\n", "# import pandas as pd\n", "# import numpy as np\n", "# from typing import List, Dict, Tuple, Optional\n", "# from datetime import datetime\n", "# from pathlib import Path\n", "\n", "# import torch\n", "# from sklearn.model_selection import train_test_split\n", "# from sklearn.metrics import classification_report, precision_recall_fscore_support\n", "# from datasets import Dataset, DatasetDict\n", "# from transformers import (\n", "#     AutoTokenizer,\n", "#     AutoModelForTokenClassification,\n", "#     TrainingArguments,\n", "#     <PERSON>er,\n", "#     DataCollatorForTokenClassification\n", "# )\n", "# from tqdm import tqdm\n", "# import warnings\n", "# warnings.filterwarnings(\"ignore\")\n", "\n", "# # Configuration\n", "# LABELS = ['O', 'B-KEY', 'I-KEY']\n", "# label2id = {label: i for i, label in enumerate(LABELS)}\n", "# id2label = {i: label for label, i in label2id.items()}\n", "# MAX_LENGTH = 512  # Increased for scientific text\n", "# RANDOM_SEED = 42\n", "\n", "# class KeyphraseDataProcessor:\n", "#     \"\"\"\n", "#     Processes raw sentences and keyphrases into properly aligned BIO-tagged training data\n", "#     \"\"\"\n", "    \n", "#     def __init__(self, model_name: str = \"allenai/scibert_scivocab_uncased\"):\n", "#         self.model_name = model_name\n", "#         self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "        \n", "#     def parse_keyphrases(self, keyphrase_str: str) -> List[str]:\n", "#         \"\"\"Parse comma-separated keyphrases and clean them\"\"\"\n", "#         if pd.isna(keyphrase_str) or not keyphrase_str:\n", "#             return []\n", "        \n", "#         # Split by comma and clean each phrase\n", "#         phrases = []\n", "#         for phrase in str(keyphrase_str).split(','):\n", "#             phrase = phrase.strip()\n", "#             if phrase and len(phrase) > 1:  # Filter out empty or single character phrases\n", "#                 phrases.append(phrase)\n", "#         return phrases\n", "    \n", "#     def find_keyphrase_spans(self, sentence: str, keyphrases: List[str]) -> List[Tuple[int, int, str]]:\n", "#         \"\"\"\n", "#         Find character-level spans of keyphrases in sentence\n", "#         Returns list of (start, end, phrase) tuples\n", "#         \"\"\"\n", "#         spans = []\n", "#         sentence_lower = sentence.lower()\n", "        \n", "#         for phrase in keyphrases:\n", "#             phrase_clean = phrase.lower().strip()\n", "#             if not phrase_clean:\n", "#                 continue\n", "                \n", "#             # Find all occurrences of this phrase\n", "#             start_pos = 0\n", "#             while True:\n", "#                 pos = sentence_lower.find(phrase_clean, start_pos)\n", "#                 if pos == -1:\n", "#                     break\n", "                \n", "#                 # Check word boundaries to avoid partial matches\n", "#                 word_start = pos == 0 or not sentence_lower[pos-1].isalnum()\n", "#                 word_end = pos + len(phrase_clean) == len(sentence_lower) or not sentence_lower[pos + len(phrase_clean)].isalnum()\n", "                \n", "#                 if word_start and word_end:\n", "#                     spans.append((pos, pos + len(phrase_clean), phrase))\n", "                \n", "#                 start_pos = pos + 1\n", "        \n", "#         # Sort spans by start position and remove overlaps\n", "#         spans.sort(key=lambda x: x[0])\n", "#         non_overlapping_spans = []\n", "#         for span in spans:\n", "#             if not non_overlapping_spans or span[0] >= non_overlapping_spans[-1][1]:\n", "#                 non_overlapping_spans.append(span)\n", "        \n", "#         return non_overlapping_spans\n", "    \n", "#     def align_labels_with_tokens(self, sentence: str, spans: List[Tuple[int, int, str]]) -> Dict:\n", "#         \"\"\"\n", "#         Tokenize sentence and align BIO labels with BERT tokens\n", "#         \"\"\"\n", "#         # Tokenize with offset mapping\n", "#         tokenized = self.tokenizer(\n", "#             sentence,\n", "#             truncation=True,\n", "#             max_length=MAX_LENGTH,\n", "#             return_offsets_mapping=True,\n", "#             return_tensors=None\n", "#         )\n", "        \n", "#         input_ids = tokenized['input_ids']\n", "#         attention_mask = tokenized['attention_mask']\n", "#         offset_mapping = tokenized['offset_mapping']\n", "        \n", "#         # Initialize labels as 'O' (outside)\n", "#         labels = [label2id['O']] * len(input_ids)\n", "        \n", "#         # Set special tokens to -100 (ignored in loss calculation)\n", "#         for i, (start, end) in enumerate(offset_mapping):\n", "#             if start == 0 and end == 0:  # Special tokens like [CLS], [SEP]\n", "#                 labels[i] = -100\n", "        \n", "#         # Assign BIO labels based on character spans\n", "#         for span_start, span_end, phrase in spans:\n", "#             first_token_in_span = True\n", "            \n", "#             for i, (token_start, token_end) in enumerate(offset_mapping):\n", "#                 # Skip special tokens\n", "#                 if token_start == 0 and token_end == 0:\n", "#                     continue\n", "                \n", "#                 # Check if token overlaps with keyphrase span\n", "#                 if token_start < span_end and token_end > span_start:\n", "#                     if first_token_in_span:\n", "#                         labels[i] = label2id['B-KEY']\n", "#                         first_token_in_span = False\n", "#                     else:\n", "#                         labels[i] = label2id['I-KEY']\n", "        \n", "#         return {\n", "#             'input_ids': input_ids,\n", "#             'attention_mask': attention_mask,\n", "#             'labels': labels,\n", "#             'sentence': sentence,\n", "#             'keyphrases': [span[2] for span in spans]\n", "#         }\n", "    \n", "#     def process_dataset(self, df: pd.DataFrame) -> List[Dict]:\n", "#         \"\"\"\n", "#         Process the entire dataset and return training examples\n", "#         \"\"\"\n", "#         print(\"Processing dataset for training...\")\n", "#         training_examples = []\n", "        \n", "#         for idx, row in tqdm(df.iterrows(), total=len(df), desc=\"Processing sentences\"):\n", "#             sentence = str(row['sentence'])\n", "#             keyphrase_str = row['term_found']\n", "            \n", "#             # Parse keyphrases\n", "#             keyphrases = self.parse_keyphrases(keyphrase_str)\n", "            \n", "#             if not sentence.strip():\n", "#                 continue\n", "            \n", "#             # Find keyphrase spans in sentence\n", "#             spans = self.find_keyphrase_spans(sentence, keyphrases)\n", "            \n", "#             # Create aligned training example\n", "#             example = self.align_labels_with_tokens(sentence, spans)\n", "#             training_examples.append(example)\n", "        \n", "#         print(f\"Processed {len(training_examples)} training examples\")\n", "#         return training_examples\n", "    \n", "#     def create_huggingface_dataset(self, training_examples: List[Dict], \n", "#                                  test_size: float = 0.1, \n", "#                                  val_size: float = 0.1) -> DatasetDict:\n", "#         \"\"\"\n", "#         Create HuggingFace DatasetDict from training examples\n", "#         \"\"\"\n", "#         print(\"Creating HuggingFace dataset...\")\n", "        \n", "#         # Prepare data for HuggingFace Dataset\n", "#         data = {\n", "#             'input_ids': [ex['input_ids'] for ex in training_examples],\n", "#             'attention_mask': [ex['attention_mask'] for ex in training_examples],\n", "#             'labels': [ex['labels'] for ex in training_examples]\n", "#         }\n", "        \n", "#         # Create dataset\n", "#         full_dataset = Dataset.from_dict(data)\n", "        \n", "#         # Split data\n", "#         if len(training_examples) < 10:\n", "#             # Too small for proper split\n", "#             return DatasetDict({\n", "#                 'train': full_dataset,\n", "#                 'validation': Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []}),\n", "#                 'test': Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "#             })\n", "        \n", "#         train_val, test = train_test_split(\n", "#             range(len(training_examples)), \n", "#             test_size=test_size, \n", "#             random_state=RANDOM_SEED\n", "#         )\n", "        \n", "#         if len(train_val) < 5:\n", "#             train_indices = train_val\n", "#             val_indices = []\n", "#         else:\n", "#             train_indices, val_indices = train_test_split(\n", "#                 train_val, \n", "#                 test_size=val_size/(1-test_size), \n", "#                 random_state=RANDOM_SEED\n", "#             )\n", "        \n", "#         # Create split datasets\n", "#         train_dataset = full_dataset.select(train_indices)\n", "#         val_dataset = full_dataset.select(val_indices) if val_indices else Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "#         test_dataset = full_dataset.select(test) if len(test) > 0 else Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "        \n", "#         dataset_dict = DatasetDict({\n", "#             'train': train_dataset,\n", "#             'validation': val_dataset,\n", "#             'test': test_dataset\n", "#         })\n", "        \n", "#         print(f\"Dataset splits - Train: {len(dataset_dict['train'])}, Val: {len(dataset_dict['validation'])}, Test: {len(dataset_dict['test'])}\")\n", "#         return dataset_dict\n", "\n", "# class ImprovedKeyphraseExtractor:\n", "#     \"\"\"\n", "#     Improved keyphrase extraction with proper WordPiece reconstruction\n", "#     \"\"\"\n", "    \n", "#     def __init__(self, tokenizer):\n", "#         self.tokenizer = tokenizer\n", "    \n", "#     def reconstruct_wordpieces(self, tokens: List[str]) -> str:\n", "#         \"\"\"\n", "#         Properly reconstruct WordPiece tokens into complete words\n", "#         \"\"\"\n", "#         if not tokens:\n", "#             return \"\"\n", "        \n", "#         # Filter out special tokens\n", "#         clean_tokens = [token for token in tokens if token not in ['[CLS]', '[SEP]', '[PAD]']]\n", "        \n", "#         if not clean_tokens:\n", "#             return \"\"\n", "        \n", "#         # Use tokenizer's convert_tokens_to_string for proper reconstruction\n", "#         reconstructed = self.tokenizer.convert_tokens_to_string(clean_tokens)\n", "#         return reconstructed.strip()\n", "    \n", "#     def extract_keyphrases_from_bio_labels(self, tokens: List[str], labels: List[str]) -> List[str]:\n", "#         \"\"\"\n", "#         Extract keyphrases from BIO labels with proper WordPiece reconstruction\n", "#         \"\"\"\n", "#         keyphrases = []\n", "#         current_phrase_tokens = []\n", "        \n", "#         for token, label in zip(tokens, labels):\n", "#             if label == \"B-KEY\":\n", "#                 # Save previous phrase if exists\n", "#                 if current_phrase_tokens:\n", "#                     phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "#                     if phrase and len(phrase.strip()) > 1:\n", "#                         keyphrases.append(phrase)\n", "#                 # Start new phrase\n", "#                 current_phrase_tokens = [token]\n", "                \n", "#             elif label == \"I-KEY\" and current_phrase_tokens:\n", "#                 # Continue current phrase\n", "#                 current_phrase_tokens.append(token)\n", "                \n", "#             else:\n", "#                 # End current phrase\n", "#                 if current_phrase_tokens:\n", "#                     phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "#                     if phrase and len(phrase.strip()) > 1:\n", "#                         keyphrases.append(phrase)\n", "#                     current_phrase_tokens = []\n", "        \n", "#         # Handle last phrase\n", "#         if current_phrase_tokens:\n", "#             phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "#             if phrase and len(phrase.strip()) > 1:\n", "#                 keyphrases.append(phrase)\n", "        \n", "#         return keyphrases\n", "\n", "# class SciBERTKeyphraseTrainer:\n", "#     \"\"\"\n", "#     SciBERT-based keyphrase extraction trainer\n", "#     \"\"\"\n", "    \n", "#     def __init__(self, model_name: str = \"allenai/scibert_scivocab_uncased\"):\n", "#         self.model_name = model_name\n", "#         self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "#         self.model = None\n", "#         self.extractor = ImprovedKeyphraseExtractor(self.tokenizer)\n", "        \n", "#     def prepare_model(self):\n", "#         \"\"\"Initialize SciBERT model for token classification\"\"\"\n", "#         print(f\"Loading SciBERT model: {self.model_name}\")\n", "        \n", "#         self.model = AutoModelForTokenClassification.from_pretrained(\n", "#             self.model_name,\n", "#             num_labels=len(LABELS),\n", "#             id2label=id2label,\n", "#             label2id=label2id\n", "#         )\n", "        \n", "#         print(f\"Model loaded successfully with {len(LABELS)} labels\")\n", "    \n", "#     def train_model(self, dataset_dict: DatasetDict, \n", "#                    output_dir: str,\n", "#                    num_epochs: int = 5,\n", "#                    batch_size: int = 16,\n", "#                    learning_rate: float = 2e-5,\n", "#                    weight_decay: float = 0.01) -> Trainer:\n", "#         \"\"\"\n", "#         Train the SciBERT model\n", "#         \"\"\"\n", "#         print(\"Starting SciBERT training...\")\n", "        \n", "#         # Create output directory\n", "#         os.makedirs(output_dir, exist_ok=True)\n", "        \n", "#         # Training arguments\n", "#         training_args = TrainingArguments(\n", "#             output_dir=output_dir,\n", "#             num_train_epochs=num_epochs,\n", "#             per_device_train_batch_size=batch_size,\n", "#             per_device_eval_batch_size=batch_size,\n", "#             learning_rate=learning_rate,\n", "#             weight_decay=weight_decay,\n", "#             logging_dir=os.path.join(output_dir, 'logs'),\n", "#             logging_steps=50,\n", "#             evaluation_strategy=\"epoch\" if len(dataset_dict['validation']) > 0 else \"no\",\n", "#             save_strategy=\"epoch\",\n", "#             load_best_model_at_end=True if len(dataset_dict['validation']) > 0 else False,\n", "#             metric_for_best_model=\"eval_loss\" if len(dataset_dict['validation']) > 0 else None,\n", "#             seed=RANDOM_SEED,\n", "#             report_to=[],  # Disable wandb\n", "#             save_total_limit=2,\n", "#             dataloader_num_workers=0\n", "#         )\n", "        \n", "#         # Data collator\n", "#         data_collator = DataCollatorForTokenClassification(\n", "#             tokenizer=self.tokenizer,\n", "#             padding=True\n", "#         )\n", "        \n", "#         # Initialize trainer\n", "#         trainer = Trainer(\n", "#             model=self.model,\n", "#             args=training_args,\n", "#             train_dataset=dataset_dict['train'],\n", "#             eval_dataset=dataset_dict['validation'] if len(dataset_dict['validation']) > 0 else None,\n", "#             data_collator=data_collator,\n", "#             tokenizer=self.tokenizer\n", "#         )\n", "        \n", "#         # Train\n", "#         trainer.train()\n", "        \n", "#         # Save model and tokenizer\n", "#         trainer.save_model()\n", "#         self.tokenizer.save_pretrained(output_dir)\n", "        \n", "#         print(f\"Training completed! Model saved to: {output_dir}\")\n", "#         return trainer\n", "    \n", "#     def predict_keyphrases(self, sentence: str) -> List[str]:\n", "#         \"\"\"\n", "#         Predict keyphrases from a sentence using the trained model\n", "#         \"\"\"\n", "#         if self.model is None:\n", "#             raise ValueError(\"Model not loaded. Please train or load a model first.\")\n", "        \n", "#         # Tokenize input\n", "#         inputs = self.tokenizer(\n", "#             sentence,\n", "#             return_tensors=\"pt\",\n", "#             truncation=True,\n", "#             max_length=MAX_LENGTH,\n", "#             padding=True\n", "#         )\n", "        \n", "#         # Get predictions\n", "#         with torch.no_grad():\n", "#             outputs = self.model(**inputs)\n", "#             predictions = torch.argmax(outputs.logits, dim=2)\n", "        \n", "#         # Convert to labels\n", "#         tokens = self.tokenizer.convert_ids_to_tokens(inputs[\"input_ids\"][0])\n", "#         predicted_labels = [id2label[pred.item()] for pred in predictions[0]]\n", "        \n", "#         # Extract keyphrases\n", "#         keyphrases = self.extractor.extract_keyphrases_from_bio_labels(tokens, predicted_labels)\n", "        \n", "#         return keyphrases\n", "\n", "# def evaluate_model_predictions(trainer: <PERSON><PERSON>, \n", "#                              dataset_dict: DatasetDict, \n", "#                              tokenizer, \n", "#                              output_dir: str):\n", "#     \"\"\"\n", "#     Comprehensive evaluation of model predictions\n", "#     \"\"\"\n", "#     print(\"Evaluating model predictions...\")\n", "    \n", "#     # Create extractor\n", "#     extractor = ImprovedKeyphraseExtractor(tokenizer)\n", "    \n", "#     # Evaluate on test set if available\n", "#     eval_dataset = dataset_dict['test'] if len(dataset_dict['test']) > 0 else dataset_dict['validation']\n", "    \n", "#     if len(eval_dataset) == 0:\n", "#         print(\"No evaluation data available\")\n", "#         return\n", "    \n", "#     # Get predictions\n", "#     predictions = trainer.predict(eval_dataset)\n", "    \n", "#     results = []\n", "#     all_true_labels = []\n", "#     all_pred_labels = []\n", "    \n", "#     for i in tqdm(range(len(predictions.predictions)), desc=\"Processing predictions\"):\n", "#         # Get tokens\n", "#         input_ids = eval_dataset[i]['input_ids']\n", "#         tokens = tokenizer.convert_ids_to_tokens(input_ids)\n", "        \n", "#         # Get true and predicted labels\n", "#         true_labels = predictions.label_ids[i]\n", "#         pred_logits = predictions.predictions[i]\n", "#         pred_labels = pred_logits.argmax(-1)\n", "        \n", "#         # Convert to label strings (excluding special tokens)\n", "#         true_label_strs = []\n", "#         pred_label_strs = []\n", "#         clean_tokens = []\n", "        \n", "#         for j, (token, true_label, pred_label) in enumerate(zip(tokens, true_labels, pred_labels)):\n", "#             if true_label != -100:  # Not a special token\n", "#                 clean_tokens.append(token)\n", "#                 true_label_strs.append(id2label[true_label])\n", "#                 pred_label_strs.append(id2label[pred_label.item()])\n", "#                 all_true_labels.append(id2label[true_label])\n", "#                 all_pred_labels.append(id2label[pred_label.item()])\n", "        \n", "#         # Extract keyphrases\n", "#         true_keyphrases = extractor.extract_keyphrases_from_bio_labels(clean_tokens, true_label_strs)\n", "#         pred_keyphrases = extractor.extract_keyphrases_from_bio_labels(clean_tokens, pred_label_strs)\n", "        \n", "#         results.append({\n", "#             'tokens': clean_tokens,\n", "#             'true_labels': true_label_strs,\n", "#             'predicted_labels': pred_label_strs,\n", "#             'true_keyphrases': true_keyphrases,\n", "#             'predicted_keyphrases': pred_keyphrases,\n", "#             'keyphrase_match': set(true_keyphrases) == set(pred_keyphrases)\n", "#         })\n", "    \n", "#     # Calculate metrics\n", "#     report = classification_report(all_true_labels, all_pred_labels, output_dict=True)\n", "    \n", "#     # Save detailed results\n", "#     results_df = pd.DataFrame([{\n", "#         'tokens': str(r['tokens']),\n", "#         'true_labels': str(r['true_labels']),\n", "#         'predicted_labels': str(r['predicted_labels']),\n", "#         'true_keyphrases': str(r['true_keyphrases']),\n", "#         'predicted_keyphrases': str(r['predicted_keyphrases']),\n", "#         'keyphrase_match': r['keyphrase_match']\n", "#     } for r in results])\n", "    \n", "#     results_path = os.path.join(output_dir, 'evaluation_results.xlsx')\n", "#     results_df.to_excel(results_path, index=False)\n", "    \n", "#     # Save metrics\n", "#     metrics_path = os.path.join(output_dir, 'evaluation_metrics.json')\n", "#     with open(metrics_path, 'w') as f:\n", "#         json.dump(report, f, indent=2)\n", "    \n", "#     print(f\"Evaluation results saved to: {results_path}\")\n", "#     print(f\"Evaluation metrics saved to: {metrics_path}\")\n", "    \n", "#     # Print summary\n", "#     print(\"\\n\" + \"=\"*50)\n", "#     print(\"EVALUATION SUMMARY\")\n", "#     print(\"=\"*50)\n", "#     print(f\"Overall Accuracy: {report['accuracy']:.4f}\")\n", "#     print(f\"Macro F1: {report['macro avg']['f1-score']:.4f}\")\n", "#     print(f\"Weighted F1: {report['weighted avg']['f1-score']:.4f}\")\n", "    \n", "#     if 'B-KEY' in report:\n", "#         print(f\"B-KEY F1: {report['B-KEY']['f1-score']:.4f}\")\n", "#     if 'I-KEY' in report:\n", "#         print(f\"I-KEY F1: {report['I-KEY']['f1-score']:.4f}\")\n", "    \n", "#     keyphrase_matches = sum(1 for r in results if r['keyphrase_match'])\n", "#     print(f\"Exact keyphrase matches: {keyphrase_matches}/{len(results)} ({keyphrase_matches/len(results)*100:.1f}%)\")\n", "\n", "# def main_training_pipeline(data_path: str, \n", "#                          output_dir: str = \"./scibert_keyphrase_model\",\n", "#                          model_name: str = \"allenai/scibert_scivocab_uncased\",\n", "#                          num_epochs: int = 10,\n", "#                          batch_size: int = 48,\n", "#                          learning_rate: float = 2e-5):\n", "#     \"\"\"\n", "#     Main training pipeline\n", "#     \"\"\"\n", "#     print(\"=\"*80)\n", "#     print(\"SciBERT KEYPHRASE EXTRACTION TRAINING PIPELINE\")\n", "#     print(\"=\"*80)\n", "    \n", "#     # Load data\n", "#     print(f\"Loading data from: {data_path}\")\n", "#     df = pd.read_excel(data_path)\n", "#     # df = df.head(1000)\n", "#     print(f\"Loaded {len(df)} sentences\")\n", "    \n", "#     # Process dataset\n", "#     processor = KeyphraseDataProcessor(model_name)\n", "#     training_examples = processor.process_dataset(df)\n", "#     dataset_dict = processor.create_huggingface_dataset(training_examples)\n", "    \n", "#     # Initialize trainer\n", "#     trainer = SciBERTKeyphraseTrainer(model_name)\n", "#     trainer.prepare_model()\n", "    \n", "#     # Train model\n", "#     trained_trainer = trainer.train_model(\n", "#         dataset_dict=dataset_dict,\n", "#         output_dir=output_dir,\n", "#         num_epochs=num_epochs,\n", "#         batch_size=batch_size,\n", "#         learning_rate=learning_rate\n", "#     )\n", "    \n", "#     # Evaluate model\n", "#     evaluate_model_predictions(trained_trainer, dataset_dict, trainer.tokenizer, output_dir)\n", "    \n", "#     print(\"=\"*80)\n", "#     print(\"TRAINING COMPLETED SUCCESSFULLY!\")\n", "#     print(\"=\"*80)\n", "#     print(f\"Model saved to: {output_dir}\")\n", "    \n", "#     # Test predictions on sample\n", "#     sample_sentences = [\n", "#         \"The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transform IR (FT-IR), and Thermogravimetric Analysis (TGA).\",\n", "#         \"Moreover, it provides a new approach for preparing environmentally friendly MXene-based inks for 3D printing applications.\",\n", "#         \"The catalyst showed excellent performance in CO2 methanation reactions.\"\n", "#     ]\n", "    \n", "#     print(\"\\nTesting on sample sentences:\")\n", "#     for sentence in sample_sentences:\n", "#         keyphrases = trainer.predict_keyphrases(sentence)\n", "#         print(f\"\\nSentence: {sentence}\")\n", "#         print(f\"Predicted keyphrases: {keyphrases}\")\n", "    \n", "#     return trainer, output_dir\n", "\n", "\n", "# if __name__ == \"__main__\":\n", "#     # Configuration\n", "#     DATA_PATH = \"combined_shipment_tagged_dataset.xlsx\"   \n", "#     OUTPUT_DIR = \"./scibert_keyphrase_model\"\n", "#     MODEL_NAME = \"allenai/scibert_scivocab_uncased\"  # Using SciBERT\n", "    \n", "#     # Training parameters\n", "#     NUM_EPOCHS = 10\n", "#     BATCH_SIZE = 32\n", "#     LEARNING_RATE = 2e-5\n", "    \n", "#     try:\n", "#         # Run training pipeline\n", "#         trainer, model_dir = main_training_pipeline(\n", "#             data_path=DATA_PATH,\n", "#             output_dir=OUTPUT_DIR,\n", "#             model_name=MODEL_NAME,\n", "#             num_epochs=NUM_EPOCHS,\n", "#             batch_size=BATCH_SIZE,\n", "#             learning_rate=LEARNING_RATE\n", "#         )\n", "        \n", "#         print(f\"\\nTraining completed successfully!\")\n", "#         print(f\"Model saved at: {model_dir}\")\n", "       \n", "#     except Exception as e:\n", "#         print(f\"Error during training: {e}\")\n", "#         import traceback\n", "#         traceback.print_exc()\n", "###############################################\n", "\n", "# ###########################################################"]}, {"cell_type": "code", "execution_count": null, "id": "4f80ce0b", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "BERT Keyphrase Extraction Training Module - FIXED VERSION\n", "\"\"\"\n", "\n", "import os\n", "import re\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from typing import List, Dict, Tuple, Optional\n", "from datetime import datetime\n", "from pathlib import Path\n", "import torch\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, precision_recall_fscore_support\n", "from datasets import Dataset, DatasetDict\n", "from transformers import (\n", "    AutoTokenizer,\n", "    AutoModelForTokenClassification,\n", "    TrainingArguments,\n", "    Trainer,\n", "    DataCollatorForTokenClassification\n", ")\n", "from tqdm import tqdm\n", "import warnings\n", "import shutil\n", "import traceback\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Configuration\n", "LABELS = ['O', 'B-KEY', 'I-KEY']\n", "label2id = {label: i for i, label in enumerate(LABELS)}\n", "id2label = {i: label for label, i in label2id.items()}\n", "MAX_LENGTH = 512\n", "RANDOM_SEED = 42\n", "\n", "def check_disk_space(path: str = \".\", min_gb: float = 2.0) -> bool:\n", "    \"\"\"Check if sufficient disk space is available\"\"\"\n", "    try:\n", "        free_bytes = shutil.disk_usage(path).free\n", "        free_gb = free_bytes / (1024**3)\n", "        print(f\"Available disk space: {free_gb:.2f} GB\")\n", "        return free_gb >= min_gb\n", "    except Exception as e:\n", "        print(f\"Warning: Could not check disk space: {e}\")\n", "        return True\n", "\n", "class FixedKeyphraseDataProcessor:\n", "    \"\"\"\n", "    FIXED: Processes raw sentences and keyphrases with improved span detection\n", "    \"\"\"\n", "    \n", "    def __init__(self, model_name: str = \"allenai/sci<PERSON>_scivocab_uncased\"):\n", "        self.model_name = model_name\n", "        try:\n", "            self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "            print(f\"✓ Tokenizer loaded: {model_name}\")\n", "        except Exception as e:\n", "            print(f\"Error loading tokenizer: {e}\")\n", "            print(\"Falling back to bert-base-uncased...\")\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\"bert-base-uncased\")\n", "    \n", "    def parse_keyphrases(self, keyphrase_str: str) -> List[str]:\n", "        \"\"\"Parse comma-separated keyphrases and clean them\"\"\"\n", "        try:\n", "            if pd.isna(keyphrase_str) or not keyphrase_str:\n", "                return []\n", "            \n", "            # Handle different formats\n", "            if isinstance(keyphrase_str, list):\n", "                phrases = [str(p).strip() for p in keyphrase_str]\n", "            elif str(keyphrase_str).startswith('['):\n", "                # Handle string representation of list\n", "                try:\n", "                    import ast\n", "                    phrases = ast.literal_eval(str(keyphrase_str))\n", "                    phrases = [str(p).strip() for p in phrases]\n", "                except:\n", "                    phrases = str(keyphrase_str).strip('[]').split(',')\n", "                    phrases = [p.strip().strip('\"\\'') for p in phrases]\n", "            else:\n", "                # Split by comma\n", "                phrases = str(keyphrase_str).split(',')\n", "                phrases = [p.strip() for p in phrases]\n", "            \n", "            # Filter valid phrases\n", "            valid_phrases = []\n", "            for phrase in phrases:\n", "                if phrase and len(phrase.strip()) > 1:\n", "                    valid_phrases.append(phrase.strip())\n", "            \n", "            return valid_phrases\n", "            \n", "        except Exception as e:\n", "            print(f\"Warning: Error parsing keyphrases '{keyphrase_str}': {e}\")\n", "            return []\n", "    \n", "    def find_keyphrase_spans_improved(self, sentence: str, keyphrases: List[str]) -> List[Tuple[int, int, str]]:\n", "        \"\"\"\n", "        FIXED: Much more flexible keyphrase span detection\n", "        \"\"\"\n", "        if not sentence or not keyphrases:\n", "            return []\n", "        \n", "        spans = []\n", "        sentence_lower = sentence.lower()\n", "        \n", "        for phrase in keyphrases:\n", "            phrase_clean = phrase.lower().strip()\n", "            if not phrase_clean or len(phrase_clean) <= 1:\n", "                continue\n", "            \n", "            # Method 1: Direct substring matching\n", "            start_pos = 0\n", "            while True:\n", "                pos = sentence_lower.find(phrase_clean, start_pos)\n", "                if pos == -1:\n", "                    break\n", "                \n", "                end_pos = pos + len(phrase_clean)\n", "                spans.append((pos, end_pos, phrase))\n", "                start_pos = pos + 1\n", "            \n", "            # Method 2: Word-by-word matching for multi-word phrases\n", "            if ' ' in phrase_clean:\n", "                words = phrase_clean.split()\n", "                for word in words:\n", "                    if len(word) > 2:  # Skip very short words\n", "                        start_pos = 0\n", "                        while True:\n", "                            pos = sentence_lower.find(word, start_pos)\n", "                            if pos == -1:\n", "                                break\n", "                            end_pos = pos + len(word)\n", "                            spans.append((pos, end_pos, word))\n", "                            start_pos = pos + 1\n", "            \n", "            # Method 3: Fuzzy matching for common scientific terms\n", "            if any(char in phrase_clean for char in ['-', '(', ')', '[', ']']):\n", "                # Remove special characters and try again\n", "                phrase_simple = re.sub(r'[^\\w\\s]', ' ', phrase_clean)\n", "                phrase_simple = re.sub(r'\\s+', ' ', phrase_simple).strip()\n", "                \n", "                if phrase_simple:\n", "                    pos = sentence_lower.find(phrase_simple)\n", "                    if pos != -1:\n", "                        spans.append((pos, pos + len(phrase_simple), phrase))\n", "        \n", "        # Sort and remove overlaps\n", "        spans = sorted(set(spans), key=lambda x: (x[0], -x[1]))\n", "        \n", "        # Remove overlapping spans, keep longer ones\n", "        non_overlapping = []\n", "        for span in spans:\n", "            overlap = False\n", "            for existing in non_overlapping:\n", "                if (span[0] < existing[1] and span[1] > existing[0]):\n", "                    if (span[1] - span[0]) <= (existing[1] - existing[0]):\n", "                        overlap = True\n", "                        break\n", "            if not overlap:\n", "                non_overlapping.append(span)\n", "        \n", "        return non_overlapping\n", "    \n", "    def align_labels_with_tokens_improved(self, sentence: str, spans: List[Tuple[int, int, str]]) -> Optional[Dict]:\n", "        \"\"\"\n", "        FIXED: Improved BIO label alignment with better debugging\n", "        \"\"\"\n", "        try:\n", "            # Tokenize with offset mapping\n", "            tokenized = self.tokenizer(\n", "                sentence,\n", "                truncation=True,\n", "                max_length=MAX_LENGTH,\n", "                return_offsets_mapping=True,\n", "                return_tensors=None\n", "            )\n", "            \n", "            input_ids = tokenized['input_ids']\n", "            attention_mask = tokenized['attention_mask']\n", "            offset_mapping = tokenized['offset_mapping']\n", "            \n", "            # Initialize all labels as 'O'\n", "            labels = [label2id['O']] * len(input_ids)\n", "            \n", "            # Set special tokens to -100\n", "            for i, (start, end) in enumerate(offset_mapping):\n", "                if start == 0 and end == 0:  # Special tokens\n", "                    labels[i] = -100\n", "            \n", "            # **CRITICAL FIX: Improved span-to-token alignment**\n", "            positive_labels_assigned = 0\n", "            \n", "            for span_start, span_end, phrase in spans:\n", "                first_token_in_span = True\n", "                tokens_in_span = []\n", "                \n", "                for i, (token_start, token_end) in enumerate(offset_mapping):\n", "                    # Skip special tokens\n", "                    if token_start == 0 and token_end == 0:\n", "                        continue\n", "                    \n", "                    # More flexible overlap detection\n", "                    token_overlaps = (\n", "                        (token_start < span_end and token_end > span_start) or  # Any overlap\n", "                        (token_start >= span_start and token_start < span_end) or  # Token starts in span\n", "                        (token_end > span_start and token_end <= span_end)  # Token ends in span\n", "                    )\n", "                    \n", "                    if token_overlaps:\n", "                        tokens_in_span.append(i)\n", "                        if first_token_in_span:\n", "                            labels[i] = label2id['B-KEY']\n", "                            first_token_in_span = False\n", "                            positive_labels_assigned += 1\n", "                        else:\n", "                            labels[i] = label2id['I-KEY']\n", "                            positive_labels_assigned += 1\n", "            \n", "            # **DEBUG: Check if we assigned any positive labels**\n", "            b_key_count = labels.count(label2id['B-KEY'])\n", "            i_key_count = labels.count(label2id['I-KEY'])\n", "            \n", "            if b_key_count == 0 and i_key_count == 0:\n", "                print(f\"WARNING: No positive labels assigned for sentence: {sentence[:50]}...\")\n", "                print(f\"  Spans found: {spans}\")\n", "                print(f\"  Offset mapping sample: {offset_mapping[:10]}\")\n", "            \n", "            result = {\n", "                'input_ids': input_ids,\n", "                'attention_mask': attention_mask,\n", "                'labels': labels,\n", "                'sentence': sentence,\n", "                'keyphrases': [span[2] for span in spans],\n", "                'debug_info': {\n", "                    'b_key_count': b_key_count,\n", "                    'i_key_count': i_key_count,\n", "                    'spans_count': len(spans)\n", "                }\n", "            }\n", "            \n", "            return result\n", "            \n", "        except Exception as e:\n", "            print(f\"Error aligning labels for sentence: {sentence[:50]}...\")\n", "            print(f\"Error: {e}\")\n", "            return None\n", "    \n", "    def process_dataset_with_validation(self, df: pd.DataFrame) -> List[Dict]:\n", "        \"\"\"\n", "        FIXED: Process dataset with extensive validation\n", "        \"\"\"\n", "        print(\"Processing dataset with improved span detection...\")\n", "        \n", "        training_examples = []\n", "        errors = 0\n", "        total_positive_examples = 0\n", "        total_spans_found = 0\n", "        \n", "        for idx, row in tqdm(df.iterrows(), total=len(df), desc=\"Processing sentences\"):\n", "            try:\n", "                sentence = str(row['sentence']).strip()\n", "                keyphrase_str = row['term_found']\n", "                \n", "                if not sentence:\n", "                    continue\n", "                \n", "                # Parse keyphrases\n", "                keyphrases = self.parse_keyphrases(keyphrase_str)\n", "                \n", "                if not keyphrases:\n", "                    continue\n", "                \n", "                # Find spans with improved method\n", "                spans = self.find_keyphrase_spans_improved(sentence, keyphrases)\n", "                total_spans_found += len(spans)\n", "                \n", "                # Create training example\n", "                example = self.align_labels_with_tokens_improved(sentence, spans)\n", "                \n", "                if example is not None:\n", "                    # Check if this example has positive labels\n", "                    if example['debug_info']['b_key_count'] > 0:\n", "                        total_positive_examples += 1\n", "                    \n", "                    training_examples.append(example)\n", "                else:\n", "                    errors += 1\n", "                \n", "            except Exception as e:\n", "                errors += 1\n", "                if errors <= 5:\n", "                    print(f\"Error processing row {idx}: {e}\")\n", "        \n", "        print(f\"\\nDataset Processing Results:\")\n", "        print(f\"  Total examples processed: {len(training_examples)}\")\n", "        print(f\"  Examples with positive labels: {total_positive_examples}\")\n", "        print(f\"  Total spans found: {total_spans_found}\")\n", "        print(f\"  Processing errors: {errors}\")\n", "        \n", "        # **CRITICAL CHECK**\n", "        if total_positive_examples == 0:\n", "            print(\" CRITICAL ERROR: No positive training examples generated!\")\n", "            print(\"This will cause the model to only predict 'O' labels.\")\n", "            \n", "            # Try to debug first few examples\n", "            print(\"\\nDebugging first 3 examples:\")\n", "            for idx in range(min(3, len(df))):\n", "                row = df.iloc[idx]\n", "                sentence = str(row['sentence']).strip()\n", "                keyphrases = self.parse_keyphrases(row['term_found'])\n", "                spans = self.find_keyphrase_spans_improved(sentence, keyphrases)\n", "                \n", "                print(f\"\\nExample {idx+1}:\")\n", "                print(f\"  Sentence: {sentence[:100]}...\")\n", "                print(f\"  Keyphrases: {keyphrases}\")\n", "                print(f\"  Spans found: {spans}\")\n", "        else:\n", "            print(f\" Successfully generated {total_positive_examples} positive examples\")\n", "        \n", "        return training_examples\n", "    \n", "    def create_huggingface_dataset(self, training_examples: List[Dict], test_size: float = 0.1, val_size: float = 0.1) -> DatasetDict:\n", "        \"\"\"Create HuggingFace dataset with validation\"\"\"\n", "        print(\"Creating HuggingFace dataset...\")\n", "        \n", "        if not training_examples:\n", "            raise ValueError(\"No training examples provided!\")\n", "        \n", "        try:\n", "            # Prepare data\n", "            data = {\n", "                'input_ids': [ex['input_ids'] for ex in training_examples],\n", "                'attention_mask': [ex['attention_mask'] for ex in training_examples],\n", "                'labels': [ex['labels'] for ex in training_examples]\n", "            }\n", "            \n", "            # Validate label distribution\n", "            all_labels = []\n", "            for labels in data['labels']:\n", "                all_labels.extend([l for l in labels if l != -100])\n", "            \n", "            label_counts = {\n", "                'O': all_labels.count(0),\n", "                'B-KEY': all_labels.count(1),\n", "                'I-KEY': all_labels.count(2)\n", "            }\n", "            \n", "            print(f\"Overall label distribution: {label_counts}\")\n", "            \n", "            if label_counts['B-KEY'] == 0:\n", "                raise ValueError(\" No B-KEY labels in dataset! Training will fail.\")\n", "            \n", "            # Create dataset\n", "            full_dataset = Dataset.from_dict(data)\n", "            \n", "            # Split data\n", "            if len(training_examples) < 10:\n", "                return DatasetDict({\n", "                    'train': full_dataset,\n", "                    'validation': Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []}),\n", "                    'test': Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "                })\n", "            \n", "            train_val, test = train_test_split(\n", "                range(len(training_examples)),\n", "                test_size=test_size,\n", "                random_state=RANDOM_SEED\n", "            )\n", "            \n", "            if len(train_val) < 5:\n", "                train_indices = train_val\n", "                val_indices = []\n", "            else:\n", "                train_indices, val_indices = train_test_split(\n", "                    train_val,\n", "                    test_size=val_size/(1-test_size),\n", "                    random_state=RANDOM_SEED\n", "                )\n", "            \n", "            train_dataset = full_dataset.select(train_indices)\n", "            val_dataset = full_dataset.select(val_indices) if val_indices else Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "            test_dataset = full_dataset.select(test) if len(test) > 0 else Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "            \n", "            dataset_dict = DatasetDict({\n", "                'train': train_dataset,\n", "                'validation': val_dataset,\n", "                'test': test_dataset\n", "            })\n", "            \n", "            print(f\"Dataset splits - Train: {len(dataset_dict['train'])}, Val: {len(dataset_dict['validation'])}, Test: {len(dataset_dict['test'])}\")\n", "            \n", "            return dataset_dict\n", "            \n", "        except Exception as e:\n", "            print(f\"Error creating dataset: {e}\")\n", "            traceback.print_exc()\n", "            raise\n", "\n", "# Keep your existing ImprovedKeyphraseExtractor and SciBERTKeyphraseTrainer classes\n", "class ImprovedKeyphraseExtractor:\n", "    \"\"\"Improved keyphrase extraction with proper WordPiece reconstruction\"\"\"\n", "    \n", "    def __init__(self, tokenizer):\n", "        self.tokenizer = tokenizer\n", "    \n", "    def reconstruct_wordpieces(self, tokens: List[str]) -> str:\n", "        \"\"\"Pro<PERSON>ly reconstruct WordPiece tokens into complete words\"\"\"\n", "        try:\n", "            if not tokens:\n", "                return \"\"\n", "            \n", "            # Filter out special tokens\n", "            clean_tokens = [token for token in tokens if token not in ['[CLS]', '[SEP]', '[PAD]']]\n", "            if not clean_tokens:\n", "                return \"\"\n", "            \n", "            # Use tokenizer's convert_tokens_to_string for proper reconstruction\n", "            reconstructed = self.tokenizer.convert_tokens_to_string(clean_tokens)\n", "            return reconstructed.strip()\n", "        except Exception as e:\n", "            print(f\"Warning: Error reconstructing tokens: {e}\")\n", "            return \" \".join(tokens)\n", "    \n", "    def extract_keyphrases_from_bio_labels(self, tokens: List[str], labels: List[str]) -> List[str]:\n", "        \"\"\"Extract keyphrases from BIO labels with proper WordPiece reconstruction\"\"\"\n", "        try:\n", "            keyphrases = []\n", "            current_phrase_tokens = []\n", "            \n", "            for token, label in zip(tokens, labels):\n", "                if label == \"B-KEY\":\n", "                    # Save previous phrase if exists\n", "                    if current_phrase_tokens:\n", "                        phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                        if phrase and len(phrase.strip()) > 1:\n", "                            keyphrases.append(phrase)\n", "                    # Start new phrase\n", "                    current_phrase_tokens = [token]\n", "                elif label == \"I-KEY\" and current_phrase_tokens:\n", "                    # Continue current phrase\n", "                    current_phrase_tokens.append(token)\n", "                else:\n", "                    # End current phrase\n", "                    if current_phrase_tokens:\n", "                        phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                        if phrase and len(phrase.strip()) > 1:\n", "                            keyphrases.append(phrase)\n", "                        current_phrase_tokens = []\n", "            \n", "            # Handle last phrase\n", "            if current_phrase_tokens:\n", "                phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                if phrase and len(phrase.strip()) > 1:\n", "                    keyphrases.append(phrase)\n", "            \n", "            return keyphrases\n", "        except Exception as e:\n", "            print(f\"Warning: Error extracting keyphrases: {e}\")\n", "            return []\n", "\n", "class SciBERTKeyphraseTrainer:\n", "    \"\"\"SciBERT-based keyphrase extraction trainer with device handling\"\"\"\n", "    \n", "    def __init__(self, model_name: str = \"allenai/sci<PERSON>_scivocab_uncased\"):\n", "        self.model_name = model_name\n", "        try:\n", "            self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "            print(f\" Tokenizer loaded: {model_name}\")\n", "        except Exception as e:\n", "            print(f\"Error loading tokenizer: {e}\")\n", "            print(\"Falling back to bert-base-uncased...\")\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\"bert-base-uncased\")\n", "        \n", "        self.model = None\n", "        self.extractor = ImprovedKeyphraseExtractor(self.tokenizer)\n", "    \n", "    def prepare_model(self):\n", "        \"\"\"Initialize SciBERT model for token classification\"\"\"\n", "        try:\n", "            print(f\"Loading SciBERT model: {self.model_name}\")\n", "            self.model = AutoModelForTokenClassification.from_pretrained(\n", "                self.model_name,\n", "                num_labels=len(LABELS),\n", "                id2label=id2label,\n", "                label2id=label2id\n", "            )\n", "            print(f\" Model loaded with {len(LABELS)} labels\")\n", "        except Exception as e:\n", "            print(f\"Error loading model: {e}\")\n", "            print(\"Falling back to bert-base-uncased...\")\n", "            self.model = AutoModelForTokenClassification.from_pretrained(\n", "                \"bert-base-uncased\",\n", "                num_labels=len(LABELS),\n", "                id2label=id2label,\n", "                label2id=label2id\n", "            )\n", "    \n", "    def train_model(self, dataset_dict: DatasetDict, output_dir: str, num_epochs: int = 5, batch_size: int = 16, learning_rate: float = 2e-5, weight_decay: float = 0.01) -> Optional[Trainer]:\n", "        \"\"\"Train the SciBERT model with comprehensive error handling\"\"\"\n", "        print(\"Starting SciBERT training...\")\n", "        \n", "        try:\n", "            # Check disk space\n", "            if not check_disk_space(output_dir):\n", "                print(\"Warning: Limited disk space. Training may fail.\")\n", "            \n", "            # Create output directory\n", "            os.makedirs(output_dir, exist_ok=True)\n", "            \n", "            # Training arguments\n", "            training_args = TrainingArguments(\n", "                output_dir=output_dir,\n", "                num_train_epochs=num_epochs,\n", "                per_device_train_batch_size=batch_size,\n", "                per_device_eval_batch_size=batch_size,\n", "                learning_rate=learning_rate,\n", "                weight_decay=weight_decay,\n", "                logging_dir=os.path.join(output_dir, 'logs'),\n", "                logging_steps=50,\n", "                evaluation_strategy=\"epoch\" if len(dataset_dict['validation']) > 0 else \"no\",\n", "                save_strategy=\"epoch\",\n", "                save_total_limit=2,\n", "                load_best_model_at_end=True if len(dataset_dict['validation']) > 0 else False,\n", "                seed=RANDOM_SEED,\n", "                report_to=[],\n", "                dataloader_num_workers=0,\n", "                fp16=True,\n", "                push_to_hub=False,\n", "            )\n", "            \n", "            # Data collator\n", "            data_collator = DataCollatorForTokenClassification(\n", "                tokenizer=self.tokenizer,\n", "                padding=True\n", "            )\n", "            \n", "            # Initialize trainer\n", "            trainer = Trainer(\n", "                model=self.model,\n", "                args=training_args,\n", "                train_dataset=dataset_dict['train'],\n", "                eval_dataset=dataset_dict['validation'] if len(dataset_dict['validation']) > 0 else None,\n", "                data_collator=data_collator,\n", "                tokenizer=self.tokenizer\n", "            )\n", "            \n", "            # Train\n", "            trainer.train()\n", "            print(\" Training completed successfully\")\n", "            \n", "            # Save model\n", "            trainer.save_model()\n", "            self.tokenizer.save_pretrained(output_dir)\n", "            print(\" Model saved successfully\")\n", "            \n", "            return trainer\n", "            \n", "        except Exception as e:\n", "            print(f\"Fatal error in training: {e}\")\n", "            traceback.print_exc()\n", "            return None\n", "    \n", "    def predict_keyphrases(self, sentence: str) -> List[str]:\n", "        \"\"\"Predict keyphrases from a sentence\"\"\"\n", "        if self.model is None:\n", "            raise ValueError(\"Model not loaded. Please train or load a model first.\")\n", "        \n", "        try:\n", "            # Tokenize input\n", "            inputs = self.tokenizer(\n", "                sentence,\n", "                return_tensors=\"pt\",\n", "                truncation=True,\n", "                max_length=MAX_LENGTH,\n", "                padding=True\n", "            )\n", "            \n", "            # Move inputs to same device as model\n", "            device = next(self.model.parameters()).device\n", "            inputs = {k: v.to(device) for k, v in inputs.items()}\n", "            \n", "            # Get predictions\n", "            with torch.no_grad():\n", "                outputs = self.model(**inputs)\n", "                predictions = torch.argmax(outputs.logits, dim=2)\n", "            \n", "            # Convert predictions\n", "            tokens = self.tokenizer.convert_ids_to_tokens(inputs[\"input_ids\"][0].cpu())\n", "            predicted_labels = [id2label[pred.item()] for pred in predictions[0].cpu()]\n", "            \n", "            # Extract keyphrases\n", "            keyphrases = self.extractor.extract_keyphrases_from_bio_labels(tokens, predicted_labels)\n", "            \n", "            return keyphrases\n", "            \n", "        except Exception as e:\n", "            print(f\"Error during prediction: {e}\")\n", "            traceback.print_exc()\n", "            return []\n", "\n", "def main_fixed_training_pipeline(data_path: str, output_dir: str = \"./scibert_keyphrase_model\", model_name: str = \"allenai/scibert_scivocab_uncased\", num_epochs: int = 10, batch_size: int = 16, learning_rate: float = 2e-5):\n", "    \"\"\"\n", "    FIXED: Main training pipeline with comprehensive error handling and validation\n", "    \"\"\"\n", "    print(\"=\"*80)\n", "    print(\"SCIBERT KEYPHRASE EXTRACTION - FIXED TRAINING PIPELINE\")\n", "    print(\"=\"*80)\n", "    \n", "    try:\n", "        # Load data\n", "        print(f\"Loading data from: {data_path}\")\n", "        if not os.path.exists(data_path):\n", "            raise FileNotFoundError(f\"Data file not found: {data_path}\")\n", "        \n", "        df = pd.read_excel(data_path)\n", "        print(f\" Loaded {len(df)} sentences\")\n", "        \n", "        # Check required columns\n", "        required_columns = ['sentence', 'term_found']\n", "        missing_columns = [col for col in required_columns if col not in df.columns]\n", "        if missing_columns:\n", "            raise ValueError(f\"Missing required columns: {missing_columns}\")\n", "        \n", "        # Process dataset with improved processor\n", "        print(\"Processing dataset with fixed processor...\")\n", "        processor = FixedKeyphraseDataProcessor(model_name)\n", "        training_examples = processor.process_dataset_with_validation(df)\n", "        \n", "        if not training_examples:\n", "            raise ValueError(\"No valid training examples generated\")\n", "        \n", "        # Create dataset\n", "        dataset_dict = processor.create_huggingface_dataset(training_examples)\n", "        \n", "        # Initialize and train model\n", "        print(\"Initializing trainer...\")\n", "        trainer = SciBERTKeyphraseTrainer(model_name)\n", "        trainer.prepare_model()\n", "        \n", "        print(\"Starting model training...\")\n", "        trained_trainer = trainer.train_model(\n", "            dataset_dict=dataset_dict,\n", "            output_dir=output_dir,\n", "            num_epochs=num_epochs,\n", "            batch_size=batch_size,\n", "            learning_rate=learning_rate\n", "        )\n", "        \n", "        if trained_trainer is None:\n", "            print(\" Training failed\")\n", "            return None, None\n", "        \n", "        # Test predictions\n", "        if trainer.model is not None:\n", "            sample_sentences = [\n", "                \"The synthesized adsorbents underwent characterization using X-ray diffraction (XRD).\",\n", "                \"The catalyst showed excellent performance in CO2 methanation reactions.\"\n", "            ]\n", "            \n", "            print(\"\\n\" + \"=\"*50)\n", "            print(\"TESTING TRAINED MODEL\")\n", "            print(\"=\"*50)\n", "            \n", "            for sentence in sample_sentences:\n", "                try:\n", "                    keyphrases = trainer.predict_keyphrases(sentence)\n", "                    print(f\"\\nSentence: {sentence}\")\n", "                    print(f\"Predicted keyphrases: {keyphrases}\")\n", "                    \n", "                    if keyphrases:\n", "                        print(\" Model is generating keyphrases!\")\n", "                    else:\n", "                        print(\"  No keyphrases generated\")\n", "                        \n", "                except Exception as e:\n", "                    print(f\"Error predicting: {e}\")\n", "        \n", "        print(\"\\n\" + \"=\"*80)\n", "        print(\"TRAINING COMPLETED!\")\n", "        print(\"=\"*80)\n", "        \n", "        return trainer, output_dir\n", "        \n", "    except Exception as e:\n", "        print(f\"Fatal error in training pipeline: {e}\")\n", "        traceback.print_exc()\n", "        return None, None\n", "\n", "if __name__ == \"__main__\":\n", "    # Configuration\n", "    DATA_PATH = \"combined_shipment_tagged_dataset.xlsx\"\n", "    OUTPUT_DIR = \"./scibert_keyphrase_model_fixed\"\n", "    MODEL_NAME = \"allenai/scibert_scivocab_uncased\"\n", "    \n", "    # Training parameters - reduced for testing\n", "    NUM_EPOCHS = 20\n", "    BATCH_SIZE = 32\n", "    LEARNING_RATE = 2e-5\n", "    \n", "    try:\n", "        # Run fixed training pipeline\n", "        trainer, model_dir = main_fixed_training_pipeline(\n", "            data_path=DATA_PATH,\n", "            output_dir=OUTPUT_DIR,\n", "            model_name=MODEL_NAME,\n", "            num_epochs=NUM_EPOCHS,\n", "            batch_size=BATCH_SIZE,\n", "            learning_rate=LEARNING_RATE\n", "        )\n", "        \n", "        if trainer is not None:\n", "            print(f\"\\n Training completed successfully!\")\n", "            print(f\" Model saved at: {model_dir}\")\n", "            print(f\" Ready for inference!\")\n", "        else:\n", "            print(f\"\\n Training failed - check errors above\")\n", "            \n", "    except Exception as e:\n", "        print(f\"Fatal error in main pipeline: {e}\")\n", "        traceback.print_exc()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4e703927", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Complete SciBERT Keyphrase Extraction Training Module\n", "Includes: Training, Validation, Testing, Metrics, Confusion Matrix, Excel Reports\n", "Fixed version with improved span detection and comprehensive evaluation\n", "\"\"\"\n", "\n", "import os\n", "import re\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from typing import List, Dict, Tuple, Optional\n", "from datetime import datetime\n", "from pathlib import Path\n", "import torch\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, precision_recall_fscore_support, confusion_matrix, accuracy_score\n", "from datasets import Dataset, DatasetDict\n", "from transformers import (\n", "    AutoTokenizer,\n", "    AutoModelForTokenClassification,\n", "    TrainingArguments,\n", "    Trainer,\n", "    DataCollatorForTokenClassification\n", ")\n", "from tqdm import tqdm\n", "import warnings\n", "import shutil\n", "import traceback\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Configuration\n", "LABELS = ['O', 'B-KEY', 'I-KEY']\n", "label2id = {label: i for i, label in enumerate(LABELS)}\n", "id2label = {i: label for label, i in label2id.items()}\n", "MAX_LENGTH = 512\n", "RANDOM_SEED = 42\n", "\n", "def check_disk_space(path: str = \".\", min_gb: float = 2.0) -> bool:\n", "    \"\"\"Check if sufficient disk space is available\"\"\"\n", "    try:\n", "        free_bytes = shutil.disk_usage(path).free\n", "        free_gb = free_bytes / (1024**3)\n", "        print(f\"Available disk space: {free_gb:.2f} GB\")\n", "        return free_gb >= min_gb\n", "    except Exception as e:\n", "        print(f\"Warning: Could not check disk space: {e}\")\n", "        return True\n", "\n", "class FixedKeyphraseDataProcessor:\n", "    \"\"\"FIXED: Enhanced data processor with improved span detection\"\"\"\n", "    \n", "    def __init__(self, model_name: str = \"allenai/sci<PERSON>_scivocab_uncased\"):\n", "        self.model_name = model_name\n", "        try:\n", "            self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "            print(f\" Tokenizer loaded: {model_name}\")\n", "        except Exception as e:\n", "            print(f\"Error loading tokenizer: {e}\")\n", "            print(\"Falling back to bert-base-uncased...\")\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\"bert-base-uncased\")\n", "    \n", "    def parse_keyphrases(self, keyphrase_str: str) -> List[str]:\n", "        \"\"\"Enhanced keyphrase parsing with multiple format support\"\"\"\n", "        try:\n", "            if pd.isna(keyphrase_str) or not keyphrase_str:\n", "                return []\n", "            \n", "            # Handle different formats\n", "            if isinstance(keyphrase_str, list):\n", "                phrases = [str(p).strip() for p in keyphrase_str]\n", "            elif str(keyphrase_str).startswith('['):\n", "                try:\n", "                    import ast\n", "                    phrases = ast.literal_eval(str(keyphrase_str))\n", "                    phrases = [str(p).strip() for p in phrases]\n", "                except:\n", "                    phrases = str(keyphrase_str).strip('[]').split(',')\n", "                    phrases = [p.strip().strip('\"\\'') for p in phrases]\n", "            else:\n", "                phrases = str(keyphrase_str).split(',')\n", "                phrases = [p.strip() for p in phrases]\n", "            \n", "            # Filter valid phrases\n", "            valid_phrases = []\n", "            for phrase in phrases:\n", "                if phrase and len(phrase.strip()) > 1:\n", "                    valid_phrases.append(phrase.strip())\n", "            \n", "            return valid_phrases\n", "            \n", "        except Exception as e:\n", "            print(f\"Warning: Error parsing keyphrases '{keyphrase_str}': {e}\")\n", "            return []\n", "    \n", "    def find_keyphrase_spans_improved(self, sentence: str, keyphrases: List[str]) -> List[Tuple[int, int, str]]:\n", "        \"\"\"FIXED: Much more flexible keyphrase span detection\"\"\"\n", "        if not sentence or not keyphrases:\n", "            return []\n", "        \n", "        spans = []\n", "        sentence_lower = sentence.lower()\n", "        \n", "        for phrase in keyphrases:\n", "            phrase_clean = phrase.lower().strip()\n", "            if not phrase_clean or len(phrase_clean) <= 1:\n", "                continue\n", "            \n", "            # Method 1: Direct substring matching\n", "            start_pos = 0\n", "            while True:\n", "                pos = sentence_lower.find(phrase_clean, start_pos)\n", "                if pos == -1:\n", "                    break\n", "                end_pos = pos + len(phrase_clean)\n", "                spans.append((pos, end_pos, phrase))\n", "                start_pos = pos + 1\n", "            \n", "            # Method 2: Word-by-word matching for multi-word phrases\n", "            if ' ' in phrase_clean:\n", "                words = phrase_clean.split()\n", "                for word in words:\n", "                    if len(word) > 2:  # Skip very short words\n", "                        start_pos = 0\n", "                        while True:\n", "                            pos = sentence_lower.find(word, start_pos)\n", "                            if pos == -1:\n", "                                break\n", "                            end_pos = pos + len(word)\n", "                            spans.append((pos, end_pos, word))\n", "                            start_pos = pos + 1\n", "            \n", "            # Method 3: Fuzzy matching for scientific terms with special characters\n", "            if any(char in phrase_clean for char in ['-', '(', ')', '[', ']']):\n", "                phrase_simple = re.sub(r'[^\\w\\s]', ' ', phrase_clean)\n", "                phrase_simple = re.sub(r'\\s+', ' ', phrase_simple).strip()\n", "                \n", "                if phrase_simple and phrase_simple != phrase_clean:\n", "                    pos = sentence_lower.find(phrase_simple)\n", "                    if pos != -1:\n", "                        spans.append((pos, pos + len(phrase_simple), phrase))\n", "        \n", "        # Sort and remove overlaps, keep longer spans\n", "        spans = sorted(set(spans), key=lambda x: (x[0], -x[1]))\n", "        non_overlapping = []\n", "        for span in spans:\n", "            overlap = False\n", "            for existing in non_overlapping:\n", "                if (span[0] < existing[1] and span[1] > existing[0]):\n", "                    if (span[1] - span[0]) <= (existing[1] - existing[0]):\n", "                        overlap = True\n", "                        break\n", "            if not overlap:\n", "                non_overlapping.append(span)\n", "        \n", "        return non_overlapping\n", "    \n", "    def align_labels_with_tokens_improved(self, sentence: str, spans: List[Tuple[int, int, str]]) -> Optional[Dict]:\n", "        \"\"\"FIXED: Improved BIO label alignment with comprehensive debugging\"\"\"\n", "        try:\n", "            tokenized = self.tokenizer(\n", "                sentence,\n", "                truncation=True,\n", "                max_length=MAX_LENGTH,\n", "                return_offsets_mapping=True,\n", "                return_tensors=None\n", "            )\n", "            \n", "            input_ids = tokenized['input_ids']\n", "            attention_mask = tokenized['attention_mask']\n", "            offset_mapping = tokenized['offset_mapping']\n", "            \n", "            # Initialize all labels as 'O'\n", "            labels = [label2id['O']] * len(input_ids)\n", "            \n", "            # Set special tokens to -100\n", "            for i, (start, end) in enumerate(offset_mapping):\n", "                if start == 0 and end == 0:\n", "                    labels[i] = -100\n", "            \n", "            # Improved span-to-token alignment\n", "            positive_labels_assigned = 0\n", "            \n", "            for span_start, span_end, phrase in spans:\n", "                first_token_in_span = True\n", "                \n", "                for i, (token_start, token_end) in enumerate(offset_mapping):\n", "                    if token_start == 0 and token_end == 0:\n", "                        continue\n", "                    \n", "                    # More flexible overlap detection\n", "                    token_overlaps = (\n", "                        (token_start < span_end and token_end > span_start) or\n", "                        (token_start >= span_start and token_start < span_end) or\n", "                        (token_end > span_start and token_end <= span_end)\n", "                    )\n", "                    \n", "                    if token_overlaps:\n", "                        if first_token_in_span:\n", "                            labels[i] = label2id['B-KEY']\n", "                            first_token_in_span = False\n", "                            positive_labels_assigned += 1\n", "                        else:\n", "                            labels[i] = label2id['I-KEY']\n", "                            positive_labels_assigned += 1\n", "            \n", "            # Debug info\n", "            b_key_count = labels.count(label2id['B-KEY'])\n", "            i_key_count = labels.count(label2id['I-KEY'])\n", "            \n", "            return {\n", "                'input_ids': input_ids,\n", "                'attention_mask': attention_mask,\n", "                'labels': labels,\n", "                'sentence': sentence,\n", "                'keyphrases': [span[2] for span in spans],\n", "                'debug_info': {\n", "                    'b_key_count': b_key_count,\n", "                    'i_key_count': i_key_count,\n", "                    'spans_count': len(spans)\n", "                }\n", "            }\n", "            \n", "        except Exception as e:\n", "            print(f\"Error aligning labels for sentence: {sentence[:50]}...\")\n", "            return None\n", "    \n", "    def process_dataset_with_validation(self, df: pd.DataFrame) -> List[Dict]:\n", "        \"\"\"Process dataset with extensive validation and debugging\"\"\"\n", "        print(\"Processing dataset with improved span detection...\")\n", "        \n", "        training_examples = []\n", "        errors = 0\n", "        total_positive_examples = 0\n", "        total_spans_found = 0\n", "        \n", "        for idx, row in tqdm(df.iterrows(), total=len(df), desc=\"Processing sentences\"):\n", "            try:\n", "                sentence = str(row['sentence']).strip()\n", "                keyphrase_str = row['term_found']\n", "                \n", "                if not sentence:\n", "                    continue\n", "                \n", "                keyphrases = self.parse_keyphrases(keyphrase_str)\n", "                if not keyphrases:\n", "                    continue\n", "                \n", "                spans = self.find_keyphrase_spans_improved(sentence, keyphrases)\n", "                total_spans_found += len(spans)\n", "                \n", "                example = self.align_labels_with_tokens_improved(sentence, spans)\n", "                \n", "                if example is not None:\n", "                    if example['debug_info']['b_key_count'] > 0:\n", "                        total_positive_examples += 1\n", "                    training_examples.append(example)\n", "                else:\n", "                    errors += 1\n", "                \n", "            except Exception as e:\n", "                errors += 1\n", "                if errors <= 5:\n", "                    print(f\"Error processing row {idx}: {e}\")\n", "        \n", "        print(f\"\\nDataset Processing Results:\")\n", "        print(f\"  Total examples processed: {len(training_examples)}\")\n", "        print(f\"  Examples with positive labels: {total_positive_examples}\")\n", "        print(f\"  Total spans found: {total_spans_found}\")\n", "        print(f\"  Processing errors: {errors}\")\n", "        \n", "        if total_positive_examples == 0:\n", "            print(\" CRITICAL ERROR: No positive training examples generated!\")\n", "            raise ValueError(\"Training data contains no positive labels!\")\n", "        else:\n", "            print(f\" Successfully generated {total_positive_examples} positive examples\")\n", "        \n", "        return training_examples\n", "    \n", "    def create_huggingface_dataset(self, training_examples: List[Dict], test_size: float = 0.1, val_size: float = 0.1) -> DatasetDict:\n", "        \"\"\"Create HuggingFace dataset with validation\"\"\"\n", "        print(\"Creating HuggingFace dataset...\")\n", "        \n", "        if not training_examples:\n", "            raise ValueError(\"No training examples provided!\")\n", "        \n", "        try:\n", "            data = {\n", "                'input_ids': [ex['input_ids'] for ex in training_examples],\n", "                'attention_mask': [ex['attention_mask'] for ex in training_examples],\n", "                'labels': [ex['labels'] for ex in training_examples]\n", "            }\n", "            \n", "            # Validate label distribution\n", "            all_labels = []\n", "            for labels in data['labels']:\n", "                all_labels.extend([l for l in labels if l != -100])\n", "            \n", "            label_counts = {\n", "                'O': all_labels.count(0),\n", "                'B-KEY': all_labels.count(1),\n", "                'I-KEY': all_labels.count(2)\n", "            }\n", "            \n", "            print(f\"Overall label distribution: {label_counts}\")\n", "            \n", "            if label_counts['B-KEY'] == 0:\n", "                raise ValueError(\" No B-KEY labels in dataset! Training will fail.\")\n", "            \n", "            full_dataset = Dataset.from_dict(data)\n", "            \n", "            # Split data\n", "            if len(training_examples) < 10:\n", "                return DatasetDict({\n", "                    'train': full_dataset,\n", "                    'validation': Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []}),\n", "                    'test': Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "                })\n", "            \n", "            train_val, test = train_test_split(\n", "                range(len(training_examples)),\n", "                test_size=test_size,\n", "                random_state=RANDOM_SEED\n", "            )\n", "            \n", "            if len(train_val) < 5:\n", "                train_indices = train_val\n", "                val_indices = []\n", "            else:\n", "                train_indices, val_indices = train_test_split(\n", "                    train_val,\n", "                    test_size=val_size/(1-test_size),\n", "                    random_state=RANDOM_SEED\n", "                )\n", "            \n", "            train_dataset = full_dataset.select(train_indices)\n", "            val_dataset = full_dataset.select(val_indices) if val_indices else Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "            test_dataset = full_dataset.select(test) if len(test) > 0 else Dataset.from_dict({'input_ids': [], 'attention_mask': [], 'labels': []})\n", "            \n", "            dataset_dict = DatasetDict({\n", "                'train': train_dataset,\n", "                'validation': val_dataset,\n", "                'test': test_dataset\n", "            })\n", "            \n", "            print(f\"Dataset splits - Train: {len(dataset_dict['train'])}, Val: {len(dataset_dict['validation'])}, Test: {len(dataset_dict['test'])}\")\n", "            \n", "            return dataset_dict\n", "            \n", "        except Exception as e:\n", "            print(f\"Error creating dataset: {e}\")\n", "            traceback.print_exc()\n", "            raise\n", "\n", "class ImprovedKeyphraseExtractor:\n", "    \"\"\"Improved keyphrase extraction with proper WordPiece reconstruction\"\"\"\n", "    \n", "    def __init__(self, tokenizer):\n", "        self.tokenizer = tokenizer\n", "    \n", "    def reconstruct_wordpieces(self, tokens: List[str]) -> str:\n", "        \"\"\"Pro<PERSON>ly reconstruct WordPiece tokens into complete words\"\"\"\n", "        try:\n", "            if not tokens:\n", "                return \"\"\n", "            \n", "            clean_tokens = [token for token in tokens if token not in ['[CLS]', '[SEP]', '[PAD]']]\n", "            if not clean_tokens:\n", "                return \"\"\n", "            \n", "            reconstructed = self.tokenizer.convert_tokens_to_string(clean_tokens)\n", "            return reconstructed.strip()\n", "        except Exception as e:\n", "            return \" \".join(tokens)\n", "    \n", "    def extract_keyphrases_from_bio_labels(self, tokens: List[str], labels: List[str]) -> List[str]:\n", "        \"\"\"Extract keyphrases from BIO labels with proper WordPiece reconstruction\"\"\"\n", "        try:\n", "            keyphrases = []\n", "            current_phrase_tokens = []\n", "            \n", "            for token, label in zip(tokens, labels):\n", "                if label == \"B-KEY\":\n", "                    if current_phrase_tokens:\n", "                        phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                        if phrase and len(phrase.strip()) > 1:\n", "                            keyphrases.append(phrase)\n", "                    current_phrase_tokens = [token]\n", "                elif label == \"I-KEY\" and current_phrase_tokens:\n", "                    current_phrase_tokens.append(token)\n", "                else:\n", "                    if current_phrase_tokens:\n", "                        phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                        if phrase and len(phrase.strip()) > 1:\n", "                            keyphrases.append(phrase)\n", "                        current_phrase_tokens = []\n", "            \n", "            if current_phrase_tokens:\n", "                phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                if phrase and len(phrase.strip()) > 1:\n", "                    keyphrases.append(phrase)\n", "            \n", "            return keyphrases\n", "        except Exception as e:\n", "            return []\n", "\n", "class ComprehensiveSciBERTKeyphraseTrainer:\n", "    \"\"\"Complete SciBERT trainer with training, validation, testing, and comprehensive evaluation\"\"\"\n", "    \n", "    def __init__(self, model_name: str = \"allenai/sci<PERSON>_scivocab_uncased\"):\n", "        self.model_name = model_name\n", "        try:\n", "            self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "            print(f\" Tokenizer loaded: {model_name}\")\n", "        except Exception as e:\n", "            print(f\"Error loading tokenizer: {e}\")\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\"bert-base-uncased\")\n", "        \n", "        self.model = None\n", "        self.extractor = ImprovedKeyphraseExtractor(self.tokenizer)\n", "        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "        print(f\"Device: {self.device}\")\n", "    \n", "    def prepare_model(self):\n", "        \"\"\"Initialize SciBERT model for token classification\"\"\"\n", "        try:\n", "            print(f\"Loading SciBERT model: {self.model_name}\")\n", "            self.model = AutoModelForTokenClassification.from_pretrained(\n", "                self.model_name,\n", "                num_labels=len(LABELS),\n", "                id2label=id2label,\n", "                label2id=label2id\n", "            )\n", "            self.model.to(self.device)\n", "            print(f\" Model loaded with {len(LABELS)} labels on {self.device}\")\n", "        except Exception as e:\n", "            print(f\"Error loading model: {e}\")\n", "            self.model = AutoModelForTokenClassification.from_pretrained(\n", "                \"bert-base-uncased\",\n", "                num_labels=len(LABELS),\n", "                id2label=id2label,\n", "                label2id=label2id\n", "            )\n", "            self.model.to(self.device)\n", "    \n", "    def compute_metrics(self, p):\n", "        \"\"\"Compute comprehensive metrics for evaluation\"\"\"\n", "        predictions, labels = p\n", "        predictions = np.argmax(predictions, axis=2)\n", "        \n", "        # Remove ignored index (special tokens)\n", "        true_predictions = [\n", "            [id2label[p] for (p, l) in zip(prediction, label) if l != -100]\n", "            for prediction, label in zip(predictions, labels)\n", "        ]\n", "        true_labels = [\n", "            [id2label[l] for (p, l) in zip(prediction, label) if l != -100]\n", "            for prediction, label in zip(predictions, labels)\n", "        ]\n", "        \n", "        # Flatten for metrics calculation\n", "        flat_true_labels = []\n", "        flat_predictions = []\n", "        for true_label, prediction in zip(true_labels, true_predictions):\n", "            flat_true_labels.extend(true_label)\n", "            flat_predictions.extend(prediction)\n", "        \n", "        # Calculate metrics\n", "        accuracy = accuracy_score(flat_true_labels, flat_predictions)\n", "        precision, recall, f1, _ = precision_recall_fscore_support(\n", "            flat_true_labels, flat_predictions, average='weighted', zero_division=0\n", "        )\n", "        \n", "        # Calculate per-class metrics\n", "        precision_per_class, recall_per_class, f1_per_class, _ = precision_recall_fscore_support(\n", "            flat_true_labels, flat_predictions, average=None, zero_division=0, labels=LABELS\n", "        )\n", "        \n", "        return {\n", "            \"accuracy\": accuracy,\n", "            \"f1\": f1,\n", "            \"precision\": precision,\n", "            \"recall\": recall,\n", "            \"f1_B-KEY\": f1_per_class[1] if len(f1_per_class) > 1 else 0.0,\n", "            \"f1_I-KEY\": f1_per_class[2] if len(f1_per_class) > 2 else 0.0,\n", "        }\n", "    \n", "    def train_model(self, dataset_dict: DatasetDict, output_dir: str, num_epochs: int = 10, \n", "                   batch_size: int = 16, learning_rate: float = 2e-5) -> Optional[Trainer]:\n", "        \"\"\"Train the SciBERT model with comprehensive logging\"\"\"\n", "        print(\"Starting comprehensive SciBERT training...\")\n", "        \n", "        try:\n", "            if not check_disk_space(output_dir):\n", "                print(\"Warning: Limited disk space. Training may fail.\")\n", "            \n", "            os.makedirs(output_dir, exist_ok=True)\n", "            \n", "            training_args = TrainingArguments(\n", "                output_dir=output_dir,\n", "                num_train_epochs=num_epochs,\n", "                per_device_train_batch_size=batch_size,\n", "                per_device_eval_batch_size=batch_size,\n", "                learning_rate=learning_rate,\n", "                weight_decay=0.01,\n", "                logging_dir=os.path.join(output_dir, 'logs'),\n", "                logging_steps=50,\n", "                evaluation_strategy=\"epoch\" if len(dataset_dict['validation']) > 0 else \"no\",\n", "                save_strategy=\"epoch\",\n", "                save_total_limit=3,\n", "                load_best_model_at_end=True if len(dataset_dict['validation']) > 0 else False,\n", "                metric_for_best_model=\"f1\",\n", "                greater_is_better=True,\n", "                seed=RANDOM_SEED,\n", "                report_to=[],\n", "                dataloader_num_workers=0,\n", "                fp16=True if self.device.type == 'cuda' else False,\n", "                push_to_hub=False,\n", "            )\n", "            \n", "            data_collator = DataCollatorForTokenClassification(\n", "                tokenizer=self.tokenizer,\n", "                padding=True\n", "            )\n", "            \n", "            trainer = Trainer(\n", "                model=self.model,\n", "                args=training_args,\n", "                train_dataset=dataset_dict['train'],\n", "                eval_dataset=dataset_dict['validation'] if len(dataset_dict['validation']) > 0 else None,\n", "                data_collator=data_collator,\n", "                tokenizer=self.tokenizer,\n", "                compute_metrics=self.compute_metrics\n", "            )\n", "            \n", "            # Train model\n", "            print(\"Starting training...\")\n", "            trainer.train()\n", "            print(\" Training completed successfully\")\n", "            \n", "            # Save model and tokenizer\n", "            trainer.save_model()\n", "            self.tokenizer.save_pretrained(output_dir)\n", "            print(f\" Model saved to {output_dir}\")\n", "            \n", "            return trainer\n", "            \n", "        except Exception as e:\n", "            print(f\"Fatal error in training: {e}\")\n", "            traceback.print_exc()\n", "            return None\n", "    \n", "    def evaluate_and_save_results(self, trainer: Trainer, dataset_dict: DatasetDict, \n", "                                 output_dir: str, dataset_name: str) -> Dict:\n", "        \"\"\"Comprehensive evaluation with detailed results and visualization\"\"\"\n", "        print(f\"Evaluating on {dataset_name} dataset...\")\n", "        \n", "        # Select appropriate dataset\n", "        if dataset_name == \"validation\":\n", "            eval_dataset = dataset_dict['validation']\n", "        elif dataset_name == \"test\":\n", "            eval_dataset = dataset_dict['test']\n", "        else:\n", "            raise ValueError(\"dataset_name must be 'validation' or 'test'\")\n", "        \n", "        if len(eval_dataset) == 0:\n", "            print(f\"No {dataset_name} data available\")\n", "            return {}\n", "        \n", "        try:\n", "            # Get predictions\n", "            predictions = trainer.predict(eval_dataset)\n", "            preds = np.argmax(predictions.predictions, axis=2)\n", "            \n", "            # Process results\n", "            results = []\n", "            all_true_labels = []\n", "            all_pred_labels = []\n", "            all_true_keyphrases = []\n", "            all_pred_keyphrases = []\n", "            \n", "            for i in range(len(predictions.predictions)):\n", "                # Get tokens and labels\n", "                input_ids = eval_dataset[i]['input_ids']\n", "                tokens = self.tokenizer.convert_ids_to_tokens(input_ids)\n", "                true_labels = predictions.label_ids[i]\n", "                pred_labels = preds[i]\n", "                \n", "                # Convert to label strings (excluding special tokens)\n", "                true_label_strs = []\n", "                pred_label_strs = []\n", "                clean_tokens = []\n", "                \n", "                for j, (token, true_label, pred_label) in enumerate(zip(tokens, true_labels, pred_labels)):\n", "                    if true_label != -100:  # Not a special token\n", "                        clean_tokens.append(token)\n", "                        true_label_strs.append(id2label[true_label])\n", "                        pred_label_strs.append(id2label[pred_label])\n", "                        all_true_labels.append(id2label[true_label])\n", "                        all_pred_labels.append(id2label[pred_label])\n", "                \n", "                # Extract keyphrases\n", "                true_keyphrases = self.extractor.extract_keyphrases_from_bio_labels(clean_tokens, true_label_strs)\n", "                pred_keyphrases = self.extractor.extract_keyphrases_from_bio_labels(clean_tokens, pred_label_strs)\n", "                \n", "                all_true_keyphrases.extend(true_keyphrases)\n", "                all_pred_keyphrases.extend(pred_keyphrases)\n", "                \n", "                # Reconstruct sentence\n", "                sentence = self.tokenizer.convert_tokens_to_string(clean_tokens)\n", "                \n", "                results.append({\n", "                    'sentence': sentence,\n", "                    'tokens': ' '.join(clean_tokens),\n", "                    'true_labels': ' '.join(true_label_strs),\n", "                    'predicted_labels': ' '.join(pred_label_strs),\n", "                    'true_keyphrases': ', '.join(true_keyphrases),\n", "                    'predicted_keyphrases': ', '.join(pred_keyphrases),\n", "                    'keyphrase_exact_match': set(true_keyphrases) == set(pred_keyphrases),\n", "                    'true_keyphrase_count': len(true_keyphrases),\n", "                    'pred_keyphrase_count': len(pred_keyphrases)\n", "                })\n", "            \n", "            # Calculate comprehensive metrics\n", "            report = classification_report(all_true_labels, all_pred_labels, output_dict=True)\n", "            cm = confusion_matrix(all_true_labels, all_pred_labels, labels=LABELS)\n", "            \n", "            # Keyphrase-level metrics\n", "            exact_matches = sum(1 for r in results if r['keyphrase_exact_match'])\n", "            total_true_keyphrases = sum(len(r['true_keyphrases'].split(', ')) for r in results if r['true_keyphrases'])\n", "            total_pred_keyphrases = sum(len(r['predicted_keyphrases'].split(', ')) for r in results if r['predicted_keyphrases'])\n", "            \n", "            # Calculate keyphrase precision/recall\n", "            true_kp_set = set(all_true_keyphrases)\n", "            pred_kp_set = set(all_pred_keyphrases)\n", "            matched_keyphrases = len(true_kp_set & pred_kp_set)\n", "            \n", "            kp_precision = matched_keyphrases / len(pred_kp_set) if len(pred_kp_set) > 0 else 0\n", "            kp_recall = matched_keyphrases / len(true_kp_set) if len(true_kp_set) > 0 else 0\n", "            kp_f1 = 2 * kp_precision * kp_recall / (kp_precision + kp_recall) if (kp_precision + kp_recall) > 0 else 0\n", "            \n", "            # Save detailed results to Excel\n", "            results_df = pd.DataFrame(results)\n", "            results_file = os.path.join(output_dir, f'{dataset_name}_detailed_results.xlsx')\n", "            results_df.to_excel(results_file, index=False)\n", "            print(f\" Detailed results saved to {results_file}\")\n", "            \n", "            # Save metrics\n", "            comprehensive_metrics = {\n", "                'dataset': dataset_name,\n", "                'total_examples': len(results),\n", "                \n", "                # Token-level metrics\n", "                'token_accuracy': report['accuracy'],\n", "                'token_macro_f1': report['macro avg']['f1-score'],\n", "                'token_weighted_f1': report['weighted avg']['f1-score'],\n", "                \n", "                # Per-class token metrics\n", "                'O_precision': report.get('O', {}).get('precision', 0),\n", "                'O_recall': report.get('O', {}).get('recall', 0),\n", "                'O_f1': report.get('O', {}).get('f1-score', 0),\n", "                'B-KEY_precision': report.get('B-KEY', {}).get('precision', 0),\n", "                'B-KEY_recall': report.get('B-KEY', {}).get('recall', 0),\n", "                'B-KEY_f1': report.get('B-KEY', {}).get('f1-score', 0),\n", "                'I-KEY_precision': report.get('I-KEY', {}).get('precision', 0),\n", "                'I-KEY_recall': report.get('I-KEY', {}).get('recall', 0),\n", "                'I-KEY_f1': report.get('I-KEY', {}).get('f1-score', 0),\n", "                \n", "                # Keyphrase-level metrics\n", "                'keyphrase_precision': kp_precision,\n", "                'keyphrase_recall': kp_recall,\n", "                'keyphrase_f1': kp_f1,\n", "                'exact_match_accuracy': exact_matches / len(results),\n", "                'total_true_keyphrases': total_true_keyphrases,\n", "                'total_pred_keyphrases': total_pred_keyphrases,\n", "                'matched_keyphrases': matched_keyphrases,\n", "            }\n", "            \n", "            metrics_file = os.path.join(output_dir, f'{dataset_name}_metrics.json')\n", "            with open(metrics_file, 'w') as f:\n", "                json.dump(comprehensive_metrics, f, indent=2)\n", "            print(f\" Metrics saved to {metrics_file}\")\n", "            \n", "            # Create and save confusion matrix\n", "            plt.figure(figsize=(8, 6))\n", "            sns.heatmap(cm, annot=True, fmt='d', xticklabels=LABELS, yticklabels=LABELS, cmap='Blues')\n", "            plt.title(f'Confusion Matrix - {dataset_name.title()} Set')\n", "            plt.ylabel('True Label')\n", "            plt.xlabel('Predicted Label')\n", "            plt.tight_layout()\n", "            \n", "            cm_file = os.path.join(output_dir, f'{dataset_name}_confusion_matrix.png')\n", "            plt.savefig(cm_file, dpi=300, bbox_inches='tight')\n", "            plt.close()\n", "            print(f\" Confusion matrix saved to {cm_file}\")\n", "            \n", "            # Print summary\n", "            print(f\"\\n{dataset_name.upper()} SET EVALUATION SUMMARY\")\n", "            print(\"=\" * 60)\n", "            print(f\"Total examples: {len(results)}\")\n", "            print(f\"Token-level accuracy: {report['accuracy']:.4f}\")\n", "            print(f\"Token-level weighted F1: {report['weighted avg']['f1-score']:.4f}\")\n", "            print(f\"B-KEY F1: {report.get('B-KEY', {}).get('f1-score', 0):.4f}\")\n", "            print(f\"I-KEY F1: {report.get('I-KEY', {}).get('f1-score', 0):.4f}\")\n", "            print(f\"Keyphrase precision: {kp_precision:.4f}\")\n", "            print(f\"Keyphrase recall: {kp_recall:.4f}\")\n", "            print(f\"Keyphrase F1: {kp_f1:.4f}\")\n", "            print(f\"Exact match accuracy: {exact_matches/len(results):.4f}\")\n", "            print(f\"Total keyphrases (true/pred): {total_true_keyphrases}/{total_pred_keyphrases}\")\n", "            \n", "            return comprehensive_metrics\n", "            \n", "        except Exception as e:\n", "            print(f\"Error during evaluation: {e}\")\n", "            traceback.print_exc()\n", "            return {}\n", "    \n", "    def predict_keyphrases(self, sentence: str) -> List[str]:\n", "        \"\"\"Predict keyphrases from a sentence\"\"\"\n", "        if self.model is None:\n", "            raise ValueError(\"Model not loaded. Please train or load a model first.\")\n", "        \n", "        try:\n", "            inputs = self.tokenizer(\n", "                sentence,\n", "                return_tensors=\"pt\",\n", "                truncation=True,\n", "                max_length=MAX_LENGTH,\n", "                padding=True\n", "            )\n", "            \n", "            inputs = {k: v.to(self.device) for k, v in inputs.items()}\n", "            \n", "            with torch.no_grad():\n", "                outputs = self.model(**inputs)\n", "                predictions = torch.argmax(outputs.logits, dim=2)\n", "            \n", "            tokens = self.tokenizer.convert_ids_to_tokens(inputs[\"input_ids\"][0].cpu())\n", "            predicted_labels = [id2label[pred.item()] for pred in predictions[0].cpu()]\n", "            \n", "            keyphrases = self.extractor.extract_keyphrases_from_bio_labels(tokens, predicted_labels)\n", "            \n", "            return keyphrases\n", "            \n", "        except Exception as e:\n", "            print(f\"Error during prediction: {e}\")\n", "            return []\n", "\n", "def main_comprehensive_training_pipeline(\n", "    data_path: str,\n", "    output_dir: str = \"./scibert_keyphrase_model_comprehensive\",\n", "    model_name: str = \"allenai/scibert_scivocab_uncased\",\n", "    num_epochs: int = 15,\n", "    batch_size: int = 16,\n", "    learning_rate: float = 2e-5\n", "):\n", "    \"\"\"\n", "    Complete training pipeline with comprehensive evaluation and reporting\n", "    \"\"\"\n", "    print(\"=\"*100)\n", "    print(\"COMPREHENSIVE SCIBERT KEYPHRASE EXTRACTION TRAINING PIPELINE\")\n", "    print(\"=\"*100)\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    final_output_dir = f\"{output_dir}_{timestamp}\"\n", "    \n", "    try:\n", "        # Load and validate data\n", "        print(f\"Loading data from: {data_path}\")\n", "        if not os.path.exists(data_path):\n", "            raise FileNotFoundError(f\"Data file not found: {data_path}\")\n", "        \n", "        df = pd.read_excel(data_path)\n", "        print(f\" Loaded {len(df)} sentences\")\n", "        \n", "        required_columns = ['sentence', 'term_found']\n", "        missing_columns = [col for col in required_columns if col not in df.columns]\n", "        if missing_columns:\n", "            raise ValueError(f\"Missing required columns: {missing_columns}\")\n", "        \n", "        # Process dataset with fixed processor\n", "        print(\"\\nProcessing dataset...\")\n", "        processor = FixedKeyphraseDataProcessor(model_name)\n", "        training_examples = processor.process_dataset_with_validation(df)\n", "        \n", "        if not training_examples:\n", "            raise ValueError(\"No valid training examples generated\")\n", "        \n", "        # Create datasets\n", "        dataset_dict = processor.create_huggingface_dataset(training_examples)\n", "        \n", "        # Initialize trainer\n", "        print(\"\\nInitializing comprehensive trainer...\")\n", "        trainer = ComprehensiveSciBERTKeyphraseTrainer(model_name)\n", "        trainer.prepare_model()\n", "        \n", "        # Train model\n", "        print(\"\\nStarting model training...\")\n", "        trained_trainer = trainer.train_model(\n", "            dataset_dict=dataset_dict,\n", "            output_dir=final_output_dir,\n", "            num_epochs=num_epochs,\n", "            batch_size=batch_size,\n", "            learning_rate=learning_rate\n", "        )\n", "        \n", "        if trained_trainer is None:\n", "            raise RuntimeError(\"Training failed\")\n", "        \n", "        # Comprehensive evaluation\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(\"COMPREHENSIVE EVALUATION\")\n", "        print(\"=\"*60)\n", "        \n", "        # Evaluate on validation set\n", "        if len(dataset_dict['validation']) > 0:\n", "            val_metrics = trainer.evaluate_and_save_results(\n", "                trained_trainer, dataset_dict, final_output_dir, \"validation\"\n", "            )\n", "        \n", "        # Evaluate on test set\n", "        if len(dataset_dict['test']) > 0:\n", "            test_metrics = trainer.evaluate_and_save_results(\n", "                trained_trainer, dataset_dict, final_output_dir, \"test\"\n", "            )\n", "        \n", "        # Test sample predictions\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(\"SAMPLE PREDICTIONS\")\n", "        print(\"=\"*60)\n", "        \n", "        sample_sentences = [\n", "            \"The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transform IR (FT-IR), and Thermogravimetric Analysis (TGA).\",\n", "            \"Moreover, it provides a new approach for preparing environmentally friendly MXene-based inks for 3D printing applications.\",\n", "            \"The catalyst showed excellent performance in CO2 methanation reactions with high selectivity.\",\n", "            \"Scanning electron microscopy (SEM) analysis revealed the morphological features of the nanoparticles.\"\n", "        ]\n", "        \n", "        prediction_results = []\n", "        for i, sentence in enumerate(sample_sentences, 1):\n", "            try:\n", "                keyphrases = trainer.predict_keyphrases(sentence)\n", "                print(f\"\\n{i}. Sentence: {sentence}\")\n", "                print(f\"   Predicted keyphrases: {keyphrases}\")\n", "                \n", "                prediction_results.append({\n", "                    'sentence': sentence,\n", "                    'predicted_keyphrases': ', '.join(keyphrases),\n", "                    'keyphrase_count': len(keyphrases)\n", "                })\n", "                \n", "                if keyphrases:\n", "                    print(\"    Keyphrases detected!\")\n", "                else:\n", "                    print(\"     No keyphrases detected\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"    Error: {e}\")\n", "        \n", "        # Save sample predictions\n", "        if prediction_results:\n", "            sample_df = pd.DataFrame(prediction_results)\n", "            sample_file = os.path.join(final_output_dir, 'sample_predictions.xlsx')\n", "            sample_df.to_excel(sample_file, index=False)\n", "            print(f\"\\n Sample predictions saved to {sample_file}\")\n", "        \n", "        # Create summary report\n", "        summary = {\n", "            'training_completed': True,\n", "            'model_path': final_output_dir,\n", "            'training_examples': len(training_examples),\n", "            'train_size': len(dataset_dict['train']),\n", "            'validation_size': len(dataset_dict['validation']),\n", "            'test_size': len(dataset_dict['test']),\n", "            'num_epochs': num_epochs,\n", "            'batch_size': batch_size,\n", "            'learning_rate': learning_rate,\n", "            'timestamp': timestamp\n", "        }\n", "        \n", "        summary_file = os.path.join(final_output_dir, 'training_summary.json')\n", "        with open(summary_file, 'w') as f:\n", "            json.dump(summary, f, indent=2)\n", "        \n", "        print(\"\\n\" + \"=\"*100)\n", "        print(\" COMPREH<PERSON>SIVE TRAINING COMPLETED SUCCESSFULLY! \")\n", "        print(\"=\"*100)\n", "        print(f\" Model saved to: {final_output_dir}\")\n", "        print(f\" Training examples: {len(training_examples)}\")\n", "        print(f\" Datasets created: Train({len(dataset_dict['train'])}), Val({len(dataset_dict['validation'])}), Test({len(dataset_dict['test'])})\")\n", "        print(f\" Evaluation reports generated\")\n", "        print(f\" Confusion matrices saved\")\n", "        print(f\" Excel files with detailed results created\")\n", "        print(f\" Sample predictions tested\")\n", "        \n", "        # List all generated files\n", "        print(f\"\\n Generated Files in {final_output_dir}:\")\n", "        for file in sorted(os.listdir(final_output_dir)):\n", "            if os.path.isfile(os.path.join(final_output_dir, file)):\n", "                print(f\"   • {file}\")\n", "        \n", "        return trainer, final_output_dir\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n Fatal error in comprehensive training pipeline: {e}\")\n", "        traceback.print_exc()\n", "        return None, None\n", "\n", "if __name__ == \"__main__\":\n", "    # Configuration\n", "    DATA_PATH = \"combined_shipment_tagged_dataset.xlsx\"\n", "    OUTPUT_DIR = \"./scibert_keyphrase_model_comprehensive\"\n", "    MODEL_NAME = \"allenai/scibert_scivocab_uncased\"\n", "    \n", "    # Training parameters\n", "    NUM_EPOCHS = 15\n", "    BATCH_SIZE = 32\n", "    LEARNING_RATE = 2e-5\n", "    \n", "    try:\n", "        # Run comprehensive training pipeline\n", "        trainer, model_dir = main_comprehensive_training_pipeline(\n", "            data_path=DATA_PATH,\n", "            output_dir=OUTPUT_DIR,\n", "            model_name=MODEL_NAME,\n", "            num_epochs=NUM_EPOCHS,\n", "            batch_size=BATCH_SIZE,\n", "            learning_rate=LEARNING_RATE\n", "        )\n", "        \n", "        if trainer is not None:\n", "            print(f\"\\n SUCCESS! Your SciBERT model is ready for production use!\")\n", "            print(f\" Model location: {model_dir}\")\n", "            print(f\" Check the Excel files and metrics for detailed analysis\")\n", "        else:\n", "            print(f\"\\n Training failed - please check the error messages above\")\n", "            \n", "    except Exception as e:\n", "        print(f\"\\n Fatal error in main pipeline: {e}\")\n", "        traceback.print_exc()\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "21bb7db7-6d4f-40ea-8169-492d369e7e0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "BERT KEYPHRASE EXTRACTION INFERENCE\n", "================================================================================\n", "Loading model from: training_outputs_v0/models/bert_ner_20250914_103444\n", "Model loaded successfully!\n", "Model type: bert\n", "Vocab size: 30522\n", "Max length: 512\n", "Label mapping: {0: 'O', 1: 'B-KEY', 2: 'I-KEY'}\n", "\n", "================================================================================\n", "TESTING ON SAMPLE SENTENCES\n", "================================================================================\n", "\n", "--- Test Sentence 1 ---\n", "Input: The final frame exhibits slight deformation in addition to the bending of substrate, which indicates the thermal stability of 2(CLi 3 ) @GY at room temperature, and the strong binding between CLi 3 unit and GY against the agglomeration of CLi 3 clusters.\n", "Predicted keyphrases: ['agglomeration']\n", "Number of keyphrases: 1\n", "\n", "Token-level breakdown:\n", "  [CLS]           -> O\n", "  the             -> O\n", "  final           -> O\n", "  frame           -> O\n", "  exhibits        -> O\n", "  slight          -> O\n", "  deformation     -> O\n", "  in              -> O\n", "  addition        -> O\n", "  to              -> O\n", "  the             -> O\n", "  bending         -> O\n", "  of              -> O\n", "  substrate       -> O\n", "  ,               -> O\n", "  which           -> O\n", "  indicates       -> O\n", "  the             -> O\n", "  thermal         -> O\n", "  stability       -> O\n", "  ... (39 more tokens)\n", "\n", "--- Test Sen<PERSON>ce 2 ---\n", "Input: This novel approach offers designing highly efficient HER electrocatalysts with low noble metal content through tailored structural features and interfacial synergy to accelerate proton Reduction kinetics.\n", "Predicted keyphrases: ['electrocatalysts', 'noble', 'synergy', 'proton', 'reduction kinetics']\n", "Number of keyphrases: 5\n", "\n", "--- Test Sentence 3 ---\n", "Input: The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transform IR (FT-IR), Proton NMR (1H NMR), Thermogravimetric Analysis (TGA), field-emission SEM (FE-SEM), transmission electron microscopy (TEM), Energy Dispersive Spectrometer (EDS), and <PERSON><PERSON>auer Emmett-Teller (BET).\n", "Predicted keyphrases: ['adsorbents', 'x - ray diffraction', 'xrd', 'fourier transform', 'ft - ir', 'proton', 'thermogravimetric analysis', 'emission']\n", "Number of keyphrases: 8\n", "\n", "--- <PERSON> 4 ---\n", "Input: We synthesized nickel catalysts supported on carbon nanotubes (Ni/NTC), graphene nanoplatelets (Ni/NPG), and silica (Ni/SiO2) and evaluated their performance in CO2 methanation.\n", "Predicted keyphrases: ['carbon nanotubes', 'nanoplatelets', 'methanation']\n", "Number of keyphrases: 3\n", "\n", "--- <PERSON> 5 ---\n", "Input: The morphologies and microstructures of the UV photolysis products were examined using TEM (JEM- 2100, JEOL, Ltd., Japan).\n", "Predicted keyphrases: ['microstructures', 'uv photolysis']\n", "Number of keyphrases: 2\n", "\n", "================================================================================\n", "SAVING PREDICTIONS\n", "================================================================================\n", "Predictions saved to: inference_test_results.xlsx\n", "\n", "================================================================================\n", "INFERENCE TESTING COMPLETED!\n", "================================================================================\n", "Model path: training_outputs_v0/models/bert_ner_20250914_103444\n", "Results saved to: inference_test_results.xlsx\n"]}], "source": ["\"\"\"\n", "BERT Keyphrase Extraction Inference Module\n", "For testing trained models and extracting keyphrases from scientific text\n", "\n", "Usage:\n", "    python bert_keyphrase_inference.py\n", "\"\"\"\n", "\n", "import os\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from typing import List, Dict, Tuple, Optional\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "try:\n", "    import torch\n", "    from transformers import AutoTokenizer, AutoModelForTokenClassification\n", "    TRANSFORMERS_AVAILABLE = True\n", "except ImportError:\n", "    print(\"Warning: transformers and/or torch not available. Please install them.\")\n", "    TRANSFORMERS_AVAILABLE = False\n", "\n", "class ImprovedKeyphraseExtractor:\n", "    \"\"\"\n", "    Enhanced keyphrase extractor that properly handles WordPiece reconstruction\n", "    \"\"\"\n", "    \n", "    def __init__(self, tokenizer):\n", "        self.tokenizer = tokenizer\n", "    \n", "    def clean_tokens(self, tokens: List[str]) -> List[str]:\n", "        \"\"\"Remove special tokens and clean the token list\"\"\"\n", "        cleaned = []\n", "        for token in tokens:\n", "            if token not in ['[CLS]', '[SEP]', '[PAD]', '[UNK]']:\n", "                cleaned.append(token)\n", "        return cleaned\n", "    \n", "    def reconstruct_wordpieces(self, tokens: List[str]) -> str:\n", "        \"\"\"\n", "        <PERSON><PERSON>ly reconstruct WordPiece tokens using tokenizer's built-in method\n", "        \"\"\"\n", "        if not tokens:\n", "            return \"\"\n", "        \n", "        # Clean tokens first\n", "        clean_tokens = self.clean_tokens(tokens)\n", "        \n", "        if not clean_tokens:\n", "            return \"\"\n", "        \n", "        # Use tokenizer's convert_tokens_to_string for proper reconstruction\n", "        try:\n", "            reconstructed = self.tokenizer.convert_tokens_to_string(clean_tokens)\n", "            return reconstructed.strip()\n", "        except:\n", "            # Fallback manual reconstruction\n", "            result = \"\"\n", "            for token in clean_tokens:\n", "                if token.startswith(\"##\"):\n", "                    result += token[2:]  # Remove ## prefix\n", "                else:\n", "                    if result:\n", "                        result += \" \"\n", "                    result += token\n", "            return result.strip()\n", "    \n", "    def extract_keyphrases_from_labels(self, tokens: List[str], labels: List[str]) -> List[str]:\n", "        \"\"\"\n", "        Extract keyphrases from BIO labels with proper WordPiece reconstruction\n", "        \"\"\"\n", "        if len(tokens) != len(labels):\n", "            print(f\"Warning: Token/label length mismatch: {len(tokens)} vs {len(labels)}\")\n", "            min_len = min(len(tokens), len(labels))\n", "            tokens = tokens[:min_len]\n", "            labels = labels[:min_len]\n", "        \n", "        keyphrases = []\n", "        current_phrase_tokens = []\n", "        \n", "        for token, label in zip(tokens, labels):\n", "            if label == \"B-KEY\":\n", "                # Save previous phrase if exists\n", "                if current_phrase_tokens:\n", "                    phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                    if phrase and len(phrase.strip()) > 1:\n", "                        keyphrases.append(phrase)\n", "                # Start new phrase\n", "                current_phrase_tokens = [token]\n", "                \n", "            elif label == \"I-KEY\" and current_phrase_tokens:\n", "                # Continue current phrase\n", "                current_phrase_tokens.append(token)\n", "                \n", "            else:\n", "                # End current phrase\n", "                if current_phrase_tokens:\n", "                    phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                    if phrase and len(phrase.strip()) > 1:\n", "                        keyphrases.append(phrase)\n", "                    current_phrase_tokens = []\n", "        \n", "        # Handle last phrase\n", "        if current_phrase_tokens:\n", "            phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "            if phrase and len(phrase.strip()) > 1:\n", "                keyphrases.append(phrase)\n", "        \n", "        return keyphrases\n", "\n", "class BERTKeyphraseInference:\n", "    \"\"\"\n", "    Inference class for BERT keyphrase extraction with improved tokenization handling\n", "    \"\"\"\n", "    \n", "    def __init__(self, model_path: str):\n", "        \"\"\"\n", "        Initialize the inference model\n", "        \n", "        Args:\n", "            model_path: Path to the trained model directory\n", "        \"\"\"\n", "        if not TRANSFORMERS_AVAILABLE:\n", "            raise ImportError(\"Please install transformers and torch: pip install transformers torch\")\n", "        \n", "        self.model_path = model_path\n", "        self.tokenizer = None\n", "        self.model = None\n", "        self.extractor = None\n", "        self.id2label = {0: 'O', 1: 'B-KEY', 2: 'I-KEY'}  # Default mapping\n", "        \n", "        self._load_model()\n", "    \n", "    def _load_model(self):\n", "        \"\"\"Load the trained model and tokenizer\"\"\"\n", "        print(f\"Loading model from: {self.model_path}\")\n", "        \n", "        if not os.path.exists(self.model_path):\n", "            raise FileNotFoundError(f\"Model path does not exist: {self.model_path}\")\n", "        \n", "        try:\n", "            # Load tokenizer\n", "            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)\n", "            \n", "            # Load model\n", "            self.model = AutoModelForTokenClassification.from_pretrained(self.model_path)\n", "            \n", "            # Try to load label mapping if available\n", "            config_path = os.path.join(self.model_path, 'config.json')\n", "            if os.path.exists(config_path):\n", "                with open(config_path, 'r') as f:\n", "                    config = json.load(f)\n", "                    if 'id2label' in config:\n", "                        self.id2label = {int(k): v for k, v in config['id2label'].items()}\n", "            \n", "            # Initialize extractor\n", "            self.extractor = ImprovedKeyphraseExtractor(self.tokenizer)\n", "            \n", "            print(f\"Model loaded successfully!\")\n", "            print(f\"Model type: {self.model.config.model_type}\")\n", "            print(f\"Vocab size: {self.tokenizer.vocab_size}\")\n", "            print(f\"Max length: {self.tokenizer.model_max_length}\")\n", "            print(f\"Label mapping: {self.id2label}\")\n", "            \n", "        except Exception as e:\n", "            raise RuntimeError(f\"Failed to load model: {str(e)}\")\n", "    \n", "    def predict_single(self, sentence: str, max_length: int = 512) -> Dict:\n", "        \"\"\"\n", "        Predict keyphrases for a single sentence\n", "        \n", "        Args:\n", "            sentence: Input sentence\n", "            max_length: Maximum sequence length\n", "            \n", "        Returns:\n", "            Dictionary with prediction results\n", "        \"\"\"\n", "        if not sentence.strip():\n", "            return {\n", "                'sentence': sentence,\n", "                'tokens': [],\n", "                'predicted_labels': [],\n", "                'predicted_keyphrases': [],\n", "                'confidence_scores': []\n", "            }\n", "        \n", "        # Tokenize input\n", "        inputs = self.tokenizer(\n", "            sentence,\n", "            return_tensors=\"pt\",\n", "            truncation=True,\n", "            max_length=max_length,\n", "            padding=True,\n", "            return_attention_mask=True\n", "        )\n", "        \n", "        # Get predictions\n", "        with torch.no_grad():\n", "            outputs = self.model(**inputs)\n", "            predictions = torch.argmax(outputs.logits, dim=2)\n", "            # Get confidence scores (max probability for each token)\n", "            probabilities = torch.softmax(outputs.logits, dim=2)\n", "            confidence_scores = torch.max(probabilities, dim=2)[0]\n", "        \n", "        # Convert to readable format\n", "        tokens = self.tokenizer.convert_ids_to_tokens(inputs[\"input_ids\"][0])\n", "        predicted_labels = [self.id2label.get(pred.item(), 'O') for pred in predictions[0]]\n", "        confidences = confidence_scores[0].tolist()\n", "        \n", "        # Extract keyphrases\n", "        predicted_keyphrases = self.extractor.extract_keyphrases_from_labels(tokens, predicted_labels)\n", "        \n", "        return {\n", "            'sentence': sentence,\n", "            'tokens': tokens,\n", "            'predicted_labels': predicted_labels,\n", "            'predicted_keyphrases': predicted_keyphrases,\n", "            'confidence_scores': confidences\n", "        }\n", "    \n", "    def predict_batch(self, sentences: List[str], max_length: int = 512, batch_size: int = 8) -> List[Dict]:\n", "        \"\"\"\n", "        Predict keyphrases for a batch of sentences\n", "        \n", "        Args:\n", "            sentences: List of input sentences\n", "            max_length: Maximum sequence length\n", "            batch_size: Processing batch size\n", "            \n", "        Returns:\n", "            List of prediction dictionaries\n", "        \"\"\"\n", "        results = []\n", "        \n", "        # Process in batches to avoid memory issues\n", "        for i in range(0, len(sentences), batch_size):\n", "            batch_sentences = sentences[i:i + batch_size]\n", "            \n", "            for sentence in batch_sentences:\n", "                result = self.predict_single(sentence, max_length)\n", "                results.append(result)\n", "        \n", "        return results\n", "    \n", "    def evaluate_on_test_data(self, test_file: str) -> Dict:\n", "        \"\"\"\n", "        Evaluate model on test data and compare with ground truth\n", "        \n", "        Args:\n", "            test_file: Path to test Excel file with 'sentence' and 'term_found' columns\n", "            \n", "        Returns:\n", "            Evaluation metrics\n", "        \"\"\"\n", "        print(f\"Evaluating on test file: {test_file}\")\n", "        \n", "        # Load test data\n", "        df = pd.read_excel(test_file)\n", "        \n", "        if 'sentence' not in df.columns:\n", "            raise ValueError(\"Test file must have 'sentence' column\")\n", "        \n", "        results = []\n", "        total_sentences = len(df)\n", "        \n", "        print(f\"Processing {total_sentences} test sentences...\")\n", "        \n", "        for idx, row in df.iterrows():\n", "            sentence = str(row['sentence'])\n", "            \n", "            # Get ground truth if available\n", "            true_keyphrases = []\n", "            if 'term_found' in row and pd.notna(row['term_found']):\n", "                true_keyphrases = [phrase.strip() for phrase in str(row['term_found']).split(',')]\n", "            \n", "            # Get prediction\n", "            prediction = self.predict_single(sentence)\n", "            \n", "            result = {\n", "                'sentence': sentence,\n", "                'true_keyphrases': true_keyphrases,\n", "                'predicted_keyphrases': prediction['predicted_keyphrases'],\n", "                'tokens': prediction['tokens'],\n", "                'predicted_labels': prediction['predicted_labels']\n", "            }\n", "            \n", "            # Calculate match metrics if ground truth available\n", "            if true_keyphrases:\n", "                result['exact_match'] = set(true_keyphrases) == set(prediction['predicted_keyphrases'])\n", "                result['partial_match'] = len(set(true_keyphrases) & set(prediction['predicted_keyphrases'])) > 0\n", "                result['precision'] = len(set(true_keyphrases) & set(prediction['predicted_keyphrases'])) / max(len(prediction['predicted_keyphrases']), 1)\n", "                result['recall'] = len(set(true_keyphrases) & set(prediction['predicted_keyphrases'])) / max(len(true_keyphrases), 1)\n", "            \n", "            results.append(result)\n", "            \n", "            if (idx + 1) % 10 == 0:\n", "                print(f\"Processed {idx + 1}/{total_sentences} sentences\")\n", "        \n", "        # Calculate overall metrics\n", "        if 'term_found' in df.columns:\n", "            exact_matches = sum(1 for r in results if r.get('exact_match', False))\n", "            partial_matches = sum(1 for r in results if r.get('partial_match', False))\n", "            avg_precision = np.mean([r.get('precision', 0) for r in results])\n", "            avg_recall = np.mean([r.get('recall', 0) for r in results])\n", "            \n", "            metrics = {\n", "                'total_sentences': total_sentences,\n", "                'exact_match_accuracy': exact_matches / total_sentences,\n", "                'partial_match_accuracy': partial_matches / total_sentences,\n", "                'average_precision': avg_precision,\n", "                'average_recall': avg_recall,\n", "                'average_f1': 2 * (avg_precision * avg_recall) / (avg_precision + avg_recall) if (avg_precision + avg_recall) > 0 else 0\n", "            }\n", "        else:\n", "            metrics = {'total_sentences': total_sentences}\n", "        \n", "        return {\n", "            'metrics': metrics,\n", "            'detailed_results': results\n", "        }\n", "    \n", "    def save_predictions(self, results: List[Dict], output_file: str):\n", "        \"\"\"Save prediction results to Excel file\"\"\"\n", "        # Prepare data for Excel\n", "        data = []\n", "        for result in results:\n", "            data.append({\n", "                'sentence': result['sentence'],\n", "                'predicted_keyphrases': ', '.join(result['predicted_keyphrases']),\n", "                'keyphrase_count': len(result['predicted_keyphrases']),\n", "                'tokens': str(result['tokens']),\n", "                'predicted_labels': str(result['predicted_labels'])\n", "            })\n", "        \n", "        df = pd.DataFrame(data)\n", "        df.to_excel(output_file, index=False)\n", "        print(f\"Predictions saved to: {output_file}\")\n", "\n", "def main():\n", "    \"\"\"Main function to demonstrate the inference module\"\"\"\n", "    \n", "    # Configuration\n", "    MODEL_PATH = \"training_outputs_v0/models/bert_ner_20250914_103444\"\n", "    \n", "    print(\"=\"*80)\n", "    print(\"BERT KEYPHRASE EXTRACTION INFERENCE\")\n", "    print(\"=\"*80)\n", "    \n", "    try:\n", "        # Initialize inference model\n", "        inference_model = BERTKeyphraseInference(MODEL_PATH)\n", "        \n", "        # Test sentences (scientific examples)\n", "        test_sentences = [\n", "            \"The final frame exhibits slight deformation in addition to the bending of substrate, which indicates the thermal stability of 2(CLi 3 ) @GY at room temperature, and the strong binding between CLi 3 unit and GY against the agglomeration of CLi 3 clusters.\",\n", "            \"This novel approach offers designing highly efficient HER electrocatalysts with low noble metal content through tailored structural features and interfacial synergy to accelerate proton Reduction kinetics.\",\n", "            \"The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transform IR (FT-IR), Proton NMR (1H NMR), Thermogravimetric Analysis (TGA), field-emission SEM (FE-SEM), transmission electron microscopy (TEM), Energy Dispersive Spectrometer (EDS), and <PERSON><PERSON>auer Emmett-Teller (BET).\",\n", "            \"We synthesized nickel catalysts supported on carbon nanotubes (Ni/NTC), graphene nanoplatelets (Ni/NPG), and silica (Ni/SiO2) and evaluated their performance in CO2 methanation.\",\n", "            \"The morphologies and microstructures of the UV photolysis products were examined using TEM (JEM- 2100, JEOL, Ltd., Japan).\"\n", "        ]\n", "        \n", "        print(\"\\n\" + \"=\"*80)\n", "        print(\"TESTING ON SAMPLE SENTENCES\")\n", "        print(\"=\"*80)\n", "        \n", "        # Test single predictions\n", "        for i, sentence in enumerate(test_sentences, 1):\n", "            print(f\"\\n--- Test Sentence {i} ---\")\n", "            print(f\"Input: {sentence}\")\n", "            \n", "            prediction = inference_model.predict_single(sentence)\n", "            \n", "            print(f\"Predicted keyphrases: {prediction['predicted_keyphrases']}\")\n", "            print(f\"Number of keyphrases: {len(prediction['predicted_keyphrases'])}\")\n", "            \n", "            # Show token-level predictions for first sentence\n", "            if i == 1:\n", "                print(f\"\\nToken-level breakdown:\")\n", "                tokens = prediction['tokens']\n", "                labels = prediction['predicted_labels']\n", "                for token, label in zip(tokens[:20], labels[:20]):  # Show first 20 tokens\n", "                    print(f\"  {token:15s} -> {label}\")\n", "                if len(tokens) > 20:\n", "                    print(f\"  ... ({len(tokens)-20} more tokens)\")\n", "        \n", "        # Save predictions\n", "        print(f\"\\n\" + \"=\"*80)\n", "        print(\"SAVING PREDICTIONS\")\n", "        print(\"=\"*80)\n", "        \n", "        batch_results = inference_model.predict_batch(test_sentences)\n", "        inference_model.save_predictions(batch_results, \"inference_test_results.xlsx\")\n", "        \n", "        ##### uncomment to run evaluation\n", "        # print(f\"\\n\" + \"=\"*80)\n", "        # print(\"EVALUATING ON TEST FILE\")\n", "        # print(\"=\"*80)\n", "        # \n", "        # test_file = \"test_file.xlsx\"  # Update tjis\n", "        # evaluation = inference_model.evaluate_on_test_data(test_file)\n", "        # \n", "        # print(\"Evaluation Metrics:\")\n", "        # for metric, value in evaluation['metrics'].items():\n", "        #     print(f\"  {metric}: {value:.4f}\")\n", "        \n", "        print(f\"\\n\" + \"=\"*80)\n", "        print(\"INFERENCE TESTING COMPLETED!\")\n", "        print(\"=\"*80)\n", "        print(f\"Model path: {MODEL_PATH}\")\n", "        print(f\"Results saved to: inference_test_results.xlsx\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during inference: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "# Additional utility functions\n", "def batch_inference_from_file(model_path: str, input_file: str, output_file: str):\n", "    \"\"\"\n", "    Utility function to run inference on an entire Excel file\n", "    \n", "    Args:\n", "        model_path: Path to trained model\n", "        input_file: Excel file with 'sentence' column\n", "        output_file: Output Excel file for results\n", "    \"\"\"\n", "    inference_model = BERTKeyphraseInference(model_path)\n", "    \n", "    # Load sentences\n", "    df = pd.read_excel(input_file)\n", "    sentences = df['sentence'].astype(str).tolist()\n", "    \n", "    print(f\"Processing {len(sentences)} sentences from {input_file}\")\n", "    \n", "    # Run inference\n", "    results = inference_model.predict_batch(sentences, batch_size=8)\n", "    \n", "    # Save results\n", "    inference_model.save_predictions(results, output_file)\n", "    \n", "    print(f\"Batch inference completed! Results saved to {output_file}\")\n", "\n", "def compare_with_ground_truth(model_path: str, test_file: str, output_file: str):\n", "    \"\"\"\n", "    Utility function to compare predictions with ground truth\n", "    \n", "    Args:\n", "        model_path: Path to trained model\n", "        test_file: Test Excel file with 'sentence' and 'term_found' columns\n", "        output_file: Output file for comparison results\n", "    \"\"\"\n", "    inference_model = BERTKeyphraseInference(model_path)\n", "    \n", "    # Run evaluation\n", "    evaluation = inference_model.evaluate_on_test_data(test_file)\n", "    \n", "    # Save detailed results\n", "    detailed_results = evaluation['detailed_results']\n", "    comparison_data = []\n", "    \n", "    for result in detailed_results:\n", "        comparison_data.append({\n", "            'sentence': result['sentence'],\n", "            'true_keyphrases': ', '.join(result.get('true_keyphrases', [])),\n", "            'predicted_keyphrases': ', '.join(result['predicted_keyphrases']),\n", "            'exact_match': result.get('exact_match', False),\n", "            'partial_match': result.get('partial_match', False),\n", "            'precision': result.get('precision', 0),\n", "            'recall': result.get('recall', 0)\n", "        })\n", "    \n", "    df_comparison = pd.DataFrame(comparison_data)\n", "    df_comparison.to_excel(output_file, index=False)\n", "    \n", "    # Print metrics\n", "    metrics = evaluation['metrics']\n", "    print(\"\\nEvaluation Metrics:\")\n", "    print(\"=\"*50)\n", "    for metric, value in metrics.items():\n", "        if isinstance(value, float):\n", "            print(f\"{metric}: {value:.4f}\")\n", "        else:\n", "            print(f\"{metric}: {value}\")\n", "    \n", "    print(f\"\\nDetailed comparison saved to: {output_file}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4b76382b-cd5b-46b3-b6ba-bcedd3fc1426", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "id": "8816c343-1a0c-4da7-bc7e-fef3beb4d125", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing improved matching logic...\n", "TESTING IMPROVED MATCHING LOGIC\n", "==================================================\n", "True keyphrases: ['Proton', '1H NMR', 'FE-SEM', 'Fourier transform', 'Thermogravimetric Analysis', 'field emission', 'XRD', 'Energy', 'X-ray diffraction', 'emission', 'FT-IR', 'adsorbents']\n", "Predicted keyphrases: ['adsorbents', 'x - ray diffraction', 'xrd', 'fourier transform ir', 'ft - ir', 'proton', 'thermogravimetric analysis', 'emission']\n", "\n", " Matched (8): ['field emission', 'Proton', 'X-ray diffraction', 'Fourier transform', 'adsorbents', 'FT-IR', 'XRD', 'Thermogravimetric Analysis']\n", " Missed (4): ['1H NMR', 'FE-SEM', 'Energy', 'emission']\n", " Noise (0): []\n", "\n", "================================================================================\n", "Initializing BERT Keyphrase Inference with Improved Evaluation...\n", "Loading model from: training_outputs_v0/models/bert_ner_20250914_103444\n", "✓ Model loaded successfully on cpu\n", "✓ Tokenizer vocab size: 30522\n", "✓ Label mapping: {0: 'O', 1: 'B-KEY', 2: 'I-KEY'}\n", "================================================================================\n", "BERT KEYPHRASE EXTRACTION - IMPROVED EVALUATION\n", "================================================================================\n", "Loading test data from: test_dataset.xlsx\n", "Loaded 1500 test sentences\n", "\n", "Processing sentences...\n", "Processed 10/1500 sentences\n", "Processed 20/1500 sentences\n", "Processed 30/1500 sentences\n", "Processed 40/1500 sentences\n", "Processed 50/1500 sentences\n", "Processed 60/1500 sentences\n", "Processed 70/1500 sentences\n", "Processed 80/1500 sentences\n", "Processed 90/1500 sentences\n", "Processed 100/1500 sentences\n", "Processed 110/1500 sentences\n", "Processed 120/1500 sentences\n", "Processed 130/1500 sentences\n", "Processed 140/1500 sentences\n", "Processed 150/1500 sentences\n", "Processed 160/1500 sentences\n", "Processed 170/1500 sentences\n", "Processed 180/1500 sentences\n", "Processed 190/1500 sentences\n", "Processed 200/1500 sentences\n", "Processed 210/1500 sentences\n", "Processed 220/1500 sentences\n", "Processed 230/1500 sentences\n", "Processed 240/1500 sentences\n", "Processed 250/1500 sentences\n", "Processed 260/1500 sentences\n", "Processed 270/1500 sentences\n", "Processed 280/1500 sentences\n", "Processed 290/1500 sentences\n", "Processed 300/1500 sentences\n", "Processed 310/1500 sentences\n", "Processed 320/1500 sentences\n", "Processed 330/1500 sentences\n", "Processed 340/1500 sentences\n", "Processed 350/1500 sentences\n", "Processed 360/1500 sentences\n", "Processed 370/1500 sentences\n", "Processed 380/1500 sentences\n", "Processed 390/1500 sentences\n", "Processed 400/1500 sentences\n", "Processed 410/1500 sentences\n", "Processed 420/1500 sentences\n", "Processed 430/1500 sentences\n", "Processed 440/1500 sentences\n", "Processed 450/1500 sentences\n", "Processed 460/1500 sentences\n", "Processed 470/1500 sentences\n", "Processed 480/1500 sentences\n", "Processed 490/1500 sentences\n", "Processed 500/1500 sentences\n", "Processed 510/1500 sentences\n", "Processed 520/1500 sentences\n", "Processed 530/1500 sentences\n", "Processed 540/1500 sentences\n", "Processed 550/1500 sentences\n", "Processed 560/1500 sentences\n", "Processed 570/1500 sentences\n", "Processed 580/1500 sentences\n", "Processed 590/1500 sentences\n", "Processed 600/1500 sentences\n", "Processed 610/1500 sentences\n", "Processed 620/1500 sentences\n", "Processed 630/1500 sentences\n", "Processed 640/1500 sentences\n", "Processed 650/1500 sentences\n", "Processed 660/1500 sentences\n", "Processed 670/1500 sentences\n", "Processed 680/1500 sentences\n", "Processed 690/1500 sentences\n", "Processed 700/1500 sentences\n", "Processed 710/1500 sentences\n", "Processed 720/1500 sentences\n", "Processed 730/1500 sentences\n", "Processed 740/1500 sentences\n", "Processed 750/1500 sentences\n", "Processed 760/1500 sentences\n", "Processed 770/1500 sentences\n", "Processed 780/1500 sentences\n", "Processed 790/1500 sentences\n", "Processed 800/1500 sentences\n", "Processed 810/1500 sentences\n", "Processed 820/1500 sentences\n", "Processed 830/1500 sentences\n", "Processed 840/1500 sentences\n", "Processed 850/1500 sentences\n", "Processed 860/1500 sentences\n", "Processed 870/1500 sentences\n", "Processed 880/1500 sentences\n", "Processed 890/1500 sentences\n", "Processed 900/1500 sentences\n", "Processed 910/1500 sentences\n", "Processed 920/1500 sentences\n", "Processed 930/1500 sentences\n", "Processed 940/1500 sentences\n", "Processed 950/1500 sentences\n", "Processed 960/1500 sentences\n", "Processed 970/1500 sentences\n", "Processed 980/1500 sentences\n", "Processed 990/1500 sentences\n", "Processed 1000/1500 sentences\n", "Processed 1010/1500 sentences\n", "Processed 1020/1500 sentences\n", "Processed 1030/1500 sentences\n", "Processed 1040/1500 sentences\n", "Processed 1050/1500 sentences\n", "Processed 1060/1500 sentences\n", "Processed 1070/1500 sentences\n", "Processed 1080/1500 sentences\n", "Processed 1090/1500 sentences\n", "Processed 1100/1500 sentences\n", "Processed 1110/1500 sentences\n", "Processed 1120/1500 sentences\n", "Processed 1130/1500 sentences\n", "Processed 1140/1500 sentences\n", "Processed 1150/1500 sentences\n", "Processed 1160/1500 sentences\n", "Processed 1170/1500 sentences\n", "Processed 1180/1500 sentences\n", "Processed 1190/1500 sentences\n", "Processed 1200/1500 sentences\n", "Processed 1210/1500 sentences\n", "Processed 1220/1500 sentences\n", "Processed 1230/1500 sentences\n", "Processed 1240/1500 sentences\n", "Processed 1250/1500 sentences\n", "Processed 1260/1500 sentences\n", "Processed 1270/1500 sentences\n", "Processed 1280/1500 sentences\n", "Processed 1290/1500 sentences\n", "Processed 1300/1500 sentences\n", "Processed 1310/1500 sentences\n", "Processed 1320/1500 sentences\n", "Processed 1330/1500 sentences\n", "Processed 1340/1500 sentences\n", "Processed 1350/1500 sentences\n", "Processed 1360/1500 sentences\n", "Processed 1370/1500 sentences\n", "Processed 1380/1500 sentences\n", "Processed 1390/1500 sentences\n", "Processed 1400/1500 sentences\n", "Processed 1410/1500 sentences\n", "Processed 1420/1500 sentences\n", "Processed 1430/1500 sentences\n", "Processed 1440/1500 sentences\n", "Processed 1450/1500 sentences\n", "Processed 1460/1500 sentences\n", "Processed 1470/1500 sentences\n", "Processed 1480/1500 sentences\n", "Processed 1490/1500 sentences\n", "Processed 1500/1500 sentences\n", "\n", "================================================================================\n", "IMPROVED EVALUATION RESULTS\n", "================================================================================\n", "Total test sentences: 1500\n", "\n", "KEYPHRASE-LEVEL METRICS:\n", "  Total true keyphrases: 10514\n", "  Total predicted keyphrases: 6250\n", "  Matched keyphrases: 5322\n", "  Missed keyphrases: 5192\n", "  Noise keyphrases: 772\n", "\n", "PERFORMANCE METRICS:\n", "  Recall (Sensitivity): 0.5062 (50.62%)\n", "  Precision: 0.8515 (85.15%)\n", "  F1 Score: 0.6349\n", "\n", "SENTENCE-LEVEL METRICS:\n", "  Exact match sentences: 48/1500\n", "  Sentence-level accuracy: 0.0320 (3.20%)\n", "\n", "==================================================\n", "EXAMPLE OF IMPROVED MATCHING (First sentence)\n", "==================================================\n", "Sentence: The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transfo...\n", "True keyphrases: ['Proton', '1H NMR', 'FE-SEM', 'Fourier transform', 'Thermogravimetric Analysis', 'field emission', 'XRD', 'Energy', 'X-ray diffraction', 'emission', 'FT-IR', 'adsorbents']\n", "Predicted keyphrases: ['adsorbents', 'x - ray diffraction', 'xrd', 'fourier transform ir', 'ft - ir', 'proton', 'thermogravimetric analysis', 'emission']\n", " Matched: ['field emission', 'Proton', 'X-ray diffraction', 'Fourier transform', 'adsorbents', 'FT-IR', 'XRD', 'Thermogravimetric Analysis'] (8)\n", " Missed: ['1H NMR', 'FE-SEM', 'Energy', 'emission'] (4)\n", " Noise: [] (0)\n", "\n", "================================================================================\n", "FILES SAVED:\n", "================================================================================\n", "   Detailed results: test_results_improved_evaluation.xlsx\n", "   Metrics summary: test_results_improved_evaluation_metrics.json\n", "\n", "==================================================\n", "ERROR PATTERN ANALYSIS\n", "==================================================\n", "\n", "Top 10 most commonly MISSED keyphrases:\n", "   1. '<PERSON><PERSON>' (missed 100 times)\n", "   2. '<PERSON><PERSON>' (missed 100 times)\n", "   3. 'Catalysts' (missed 99 times)\n", "   4. '<PERSON>' (missed 98 times)\n", "   5. 'cations' (missed 93 times)\n", "   6. 'Catalyst' (missed 83 times)\n", "   7. '<PERSON><PERSON><PERSON>' (missed 67 times)\n", "   8. 'lectrocatalysts' (missed 65 times)\n", "   9. 'catalyst' (missed 53 times)\n", "  10. 'Composites' (missed 50 times)\n", "\n", "Top 10 most common NOISE keyphrases:\n", "   1. 'synthesized' (noise 23 times)\n", "   2. 'electrocatalysts' (noise 19 times)\n", "   3. 'composite' (noise 18 times)\n", "   4. 'engineering' (noise 18 times)\n", "   5. 'designed' (noise 15 times)\n", "   6. 'nanoparticles' (noise 15 times)\n", "   7. 'energy storage' (noise 15 times)\n", "   8. 'feeds' (noise 15 times)\n", "   9. 'reduction' (noise 13 times)\n", "  10. 'bandgap' (noise 11 times)\n", "\n", " Improved evaluation completed successfully!\n", " Check 'test_results_improved_evaluation.xlsx' for detailed results with proper matching\n"]}], "source": ["\"\"\"\n", "BERT Keyphrase Extraction Inference Module with Improved Evaluation\n", "Fixed keyphrase matching to handle case, special characters, and partial matches\n", "\"\"\"\n", "\n", "import os\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "from typing import List, Dict, Tuple, Set, Optional\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "try:\n", "    import torch\n", "    from transformers import AutoTokenizer, AutoModelForTokenClassification\n", "    TRANSFORMERS_AVAILABLE = True\n", "except ImportError:\n", "    print(\"Error: transformers and/or torch not available. Please install them:\")\n", "    print(\"pip install transformers torch\")\n", "    TRANSFORMERS_AVAILABLE = False\n", "\n", "class ImprovedKeyphraseExtractor:\n", "    \"\"\"Enhanced keyphrase extractor with proper WordPiece reconstruction\"\"\"\n", "    \n", "    def __init__(self, tokenizer):\n", "        self.tokenizer = tokenizer\n", "    \n", "    def clean_tokens(self, tokens: List[str]) -> List[str]:\n", "        \"\"\"Remove special tokens\"\"\"\n", "        return [token for token in tokens if token not in ['[CLS]', '[SEP]', '[PAD]', '[UNK]']]\n", "    \n", "    def reconstruct_wordpieces(self, tokens: List[str]) -> str:\n", "        \"\"\"Properly reconstruct WordPiece tokens using tokenizer\"\"\"\n", "        if not tokens:\n", "            return \"\"\n", "        \n", "        clean_tokens = self.clean_tokens(tokens)\n", "        if not clean_tokens:\n", "            return \"\"\n", "        \n", "        try:\n", "            # Use tokenizer's built-in method for proper reconstruction\n", "            reconstructed = self.tokenizer.convert_tokens_to_string(clean_tokens)\n", "            return reconstructed.strip()\n", "        except Exception:\n", "            # Fallback manual reconstruction\n", "            result = \"\"\n", "            for token in clean_tokens:\n", "                if token.startswith(\"##\"):\n", "                    result += token[2:]  # Remove ## prefix\n", "                else:\n", "                    if result:\n", "                        result += \" \"\n", "                    result += token\n", "            return result.strip()\n", "    \n", "    def extract_keyphrases_from_labels(self, tokens: List[str], labels: List[str]) -> List[str]:\n", "        \"\"\"Extract keyphrases with proper BIO label handling\"\"\"\n", "        if len(tokens) != len(labels):\n", "            min_len = min(len(tokens), len(labels))\n", "            tokens = tokens[:min_len]\n", "            labels = labels[:min_len]\n", "        \n", "        keyphrases = []\n", "        current_phrase_tokens = []\n", "        \n", "        for token, label in zip(tokens, labels):\n", "            if label == \"B-KEY\":\n", "                # Save previous phrase\n", "                if current_phrase_tokens:\n", "                    phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                    if phrase and len(phrase.strip()) > 1:\n", "                        keyphrases.append(phrase)\n", "                current_phrase_tokens = [token]\n", "                \n", "            elif label == \"I-KEY\" and current_phrase_tokens:\n", "                current_phrase_tokens.append(token)\n", "                \n", "            else:\n", "                # End current phrase\n", "                if current_phrase_tokens:\n", "                    phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "                    if phrase and len(phrase.strip()) > 1:\n", "                        keyphrases.append(phrase)\n", "                    current_phrase_tokens = []\n", "        \n", "        # Handle last phrase\n", "        if current_phrase_tokens:\n", "            phrase = self.reconstruct_wordpieces(current_phrase_tokens)\n", "            if phrase and len(phrase.strip()) > 1:\n", "                keyphrases.append(phrase)\n", "        \n", "        return keyphrases\n", "\n", "class ImprovedKeyphraseMatching:\n", "    \"\"\"\n", "    Improved keyphrase matching that handles:\n", "    - Case insensitivity\n", "    - Special characters (hyphens, brackets, etc.)\n", "    - Numbers\n", "    - Partial token matches\n", "    \"\"\"\n", "    \n", "    @staticmethod\n", "    def normalize_keyphrase_for_matching(phrase: str) -> str:\n", "        \"\"\"\n", "        Normalize keyphrase for matching by removing special chars, digits, hyphens\n", "        \"\"\"\n", "        if not phrase or not isinstance(phrase, str):\n", "            return \"\"\n", "        \n", "        # Convert to lowercase\n", "        phrase = phrase.lower().strip()\n", "        \n", "        # Remove digits, special characters, and punctuation\n", "        # Keep only alphabetic characters and spaces\n", "        phrase = re.sub(r'[\\d\\[\\](){}:;,./\\\\-]', ' ', phrase)\n", "        phrase = re.sub(r'[^a-z\\s]', ' ', phrase)\n", "        \n", "        # Convert multiple spaces to single space\n", "        phrase = re.sub(r'\\s+', ' ', phrase)\n", "        phrase = phrase.strip()\n", "        \n", "        return phrase\n", "    \n", "    @staticmethod\n", "    def get_token_set(phrase: str) -> Set[str]:\n", "        \"\"\"Get normalized token set from phrase\"\"\"\n", "        normalized = ImprovedKeyphraseMatching.normalize_keyphrase_for_matching(phrase)\n", "        if not normalized:\n", "            return set()\n", "        tokens = set(normalized.split())\n", "        # Remove empty tokens\n", "        tokens = {t for t in tokens if t}\n", "        return tokens\n", "    \n", "    @staticmethod\n", "    def calculate_matches_improved(true_keyphrases: List[str], predicted_keyphrases: List[str]) -> Dict[str, List[str]]:\n", "        \"\"\"\n", "        Calculate matched, missed, and noise keyphrases with improved matching\n", "        \"\"\"\n", "        # Clean input lists\n", "        true_keyphrases = [kp for kp in true_keyphrases if kp and isinstance(kp, str) and kp.strip()]\n", "        predicted_keyphrases = [kp for kp in predicted_keyphrases if kp and isinstance(kp, str) and kp.strip()]\n", "        \n", "        # Create token sets for each keyphrase\n", "        true_tokens_map = {kp: ImprovedKeyphraseMatching.get_token_set(kp) for kp in true_keyphrases}\n", "        pred_tokens_map = {kp: ImprovedKeyphraseMatching.get_token_set(kp) for kp in predicted_keyphrases}\n", "        \n", "        matched_true = set()\n", "        matched_pred = set()\n", "        \n", "        # Match predicted keyphrases with true keyphrases\n", "        for pred_kp, pred_tokens in pred_tokens_map.items():\n", "            if not pred_tokens:\n", "                continue\n", "                \n", "            best_match = None\n", "            best_score = 0\n", "            \n", "            for true_kp, true_tokens in true_tokens_map.items():\n", "                if not true_tokens or true_kp in matched_true:\n", "                    continue\n", "                \n", "                # Calculate token overlap\n", "                intersection = pred_tokens & true_tokens\n", "                if not intersection:\n", "                    continue\n", "                \n", "                # Calculate match scores\n", "                pred_ratio = len(intersection) / len(pred_tokens)\n", "                true_ratio = len(intersection) / len(true_tokens)\n", "                \n", "                # Match if significant overlap (at least 50% of tokens on either side)\n", "                if pred_ratio >= 0.5 or true_ratio >= 0.5:\n", "                    # Use the better of the two ratios as match score\n", "                    match_score = max(pred_ratio, true_ratio)\n", "                    if match_score > best_score:\n", "                        best_match = true_kp\n", "                        best_score = match_score\n", "            \n", "            if best_match:\n", "                matched_true.add(best_match)\n", "                matched_pred.add(pred_kp)\n", "        \n", "        # Calculate results\n", "        matched = list(matched_true)\n", "        missed = [kp for kp in true_keyphrases if kp not in matched_true]\n", "        noise = [kp for kp in predicted_keyphrases if kp not in matched_pred]\n", "        \n", "        return {\n", "            'matched': matched,\n", "            'missed': missed,\n", "            'noise': noise\n", "        }\n", "\n", "class BERTKeyphraseInferenceWithImprovedEvaluation:\n", "    \"\"\"\n", "    Complete inference and evaluation module with improved keyphrase matching\n", "    \"\"\"\n", "    \n", "    def __init__(self, model_path: str):\n", "        \"\"\"Initialize the inference model with evaluation capabilities\"\"\"\n", "        if not TRANSFORMERS_AVAILABLE:\n", "            raise ImportError(\"Please install required packages: pip install transformers torch\")\n", "        \n", "        self.model_path = model_path\n", "        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "        self.tokenizer = None\n", "        self.model = None\n", "        self.extractor = None\n", "        self.matcher = ImprovedKeyphraseMatching()\n", "        self.id2label = {0: 'O', 1: 'B-KEY', 2: 'I-KEY'}  # Default mapping\n", "        \n", "        self._load_model()\n", "    \n", "    def _load_model(self):\n", "        \"\"\"Load the trained model and tokenizer\"\"\"\n", "        print(f\"Loading model from: {self.model_path}\")\n", "        \n", "        if not os.path.exists(self.model_path):\n", "            raise FileNotFoundError(f\"Model path does not exist: {self.model_path}\")\n", "        \n", "        try:\n", "            # Load tokenizer and model\n", "            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)\n", "            self.model = AutoModelForTokenClassification.from_pretrained(self.model_path).to(self.device)\n", "            \n", "            # Load label mapping if available\n", "            config_path = os.path.join(self.model_path, 'config.json')\n", "            if os.path.exists(config_path):\n", "                with open(config_path, 'r') as f:\n", "                    config = json.load(f)\n", "                    if 'id2label' in config:\n", "                        self.id2label = {int(k): v for k, v in config['id2label'].items()}\n", "            \n", "            # Initialize extractor\n", "            self.extractor = ImprovedKeyphraseExtractor(self.tokenizer)\n", "            \n", "            print(f\"✓ Model loaded successfully on {self.device}\")\n", "            print(f\"✓ Tokenizer vocab size: {self.tokenizer.vocab_size}\")\n", "            print(f\"✓ Label mapping: {self.id2label}\")\n", "            \n", "        except Exception as e:\n", "            raise RuntimeError(f\"Failed to load model: {str(e)}\")\n", "    \n", "    def predict_single(self, sentence: str, max_length: int = 512) -> List[str]:\n", "        \"\"\"Predict keyphrases for a single sentence\"\"\"\n", "        if not sentence.strip():\n", "            return []\n", "        \n", "        # Tokenize input\n", "        inputs = self.tokenizer(\n", "            sentence,\n", "            return_tensors=\"pt\",\n", "            truncation=True,\n", "            max_length=max_length,\n", "            padding=True\n", "        ).to(self.device)\n", "        \n", "        # Get predictions\n", "        with torch.no_grad():\n", "            outputs = self.model(**inputs)\n", "            predictions = torch.argmax(outputs.logits, dim=2)\n", "        \n", "        # Convert to labels\n", "        tokens = self.tokenizer.convert_ids_to_tokens(inputs[\"input_ids\"][0])\n", "        predicted_labels = [self.id2label.get(pred.item(), 'O') for pred in predictions[0]]\n", "        \n", "        # Extract keyphrases\n", "        keyphrases = self.extractor.extract_keyphrases_from_labels(tokens, predicted_labels)\n", "        \n", "        return keyphrases\n", "    \n", "    def parse_ground_truth_keyphrases(self, term_found_str) -> List[str]:\n", "        \"\"\"Parse ground truth keyphrases from various formats\"\"\"\n", "        if pd.isna(term_found_str) or not term_found_str:\n", "            return []\n", "        \n", "        if isinstance(term_found_str, list):\n", "            return [str(term).strip() for term in term_found_str if str(term).strip()]\n", "        \n", "        if isinstance(term_found_str, str):\n", "            # Check if it's a string representation of a list\n", "            if term_found_str.startswith('[') and term_found_str.endswith(']'):\n", "                try:\n", "                    # Try to evaluate as Python list\n", "                    import ast\n", "                    parsed_list = ast.literal_eval(term_found_str)\n", "                    if isinstance(parsed_list, list):\n", "                        return [str(term).strip() for term in parsed_list if str(term).strip()]\n", "                except:\n", "                    pass\n", "            \n", "            # Split by comma and clean\n", "            terms = []\n", "            for term in term_found_str.split(','):\n", "                term = term.strip().strip(\"'\\\"[]\")  # Remove quotes and brackets\n", "                if term and len(term) > 0:\n", "                    terms.append(term)\n", "            return terms\n", "        \n", "        return [str(term_found_str).strip()] if str(term_found_str).strip() else []\n", "    \n", "    def predict_and_evaluate(self, input_file: str, output_file: str) -> pd.DataFrame:\n", "        \"\"\"\n", "        Complete prediction and evaluation pipeline with improved matching\n", "        \"\"\"\n", "        print(\"=\"*80)\n", "        print(\"BERT KEYPHRASE EXTRACTION - IMPROVED EVALUATION\")\n", "        print(\"=\"*80)\n", "        \n", "        # Load test data\n", "        print(f\"Loading test data from: {input_file}\")\n", "        df = pd.read_excel(input_file)\n", "        \n", "        if 'sentence' not in df.columns:\n", "            raise ValueError(\"Input file must contain 'sentence' column\")\n", "        if 'term_found' not in df.columns:\n", "            raise ValueError(\"Input file must contain 'term_found' column\")\n", "        \n", "        print(f\"Loaded {len(df)} test sentences\")\n", "        \n", "        # Process each sentence\n", "        results = []\n", "        print(f\"\\nProcessing sentences...\")\n", "        \n", "        for idx, row in df.iterrows():\n", "            sentence = str(row['sentence'])\n", "            term_found = row['term_found']\n", "            \n", "            # Parse ground truth\n", "            true_keyphrases = self.parse_ground_truth_keyphrases(term_found)\n", "            \n", "            # Get predictions\n", "            predicted_keyphrases = self.predict_single(sentence)\n", "            \n", "            # Calculate matches using improved matching\n", "            match_results = self.matcher.calculate_matches_improved(true_keyphrases, predicted_keyphrases)\n", "            \n", "            # Prepare result with lists (not comma-separated strings)\n", "            result = {\n", "                'sentence': sentence,\n", "                'term_found': true_keyphrases,  # Keep as list\n", "                'predicted_keyphrases': predicted_keyphrases,  # Keep as list\n", "                'matched_keyphrases': match_results['matched'],  # List\n", "                'missed_keyphrases': match_results['missed'],  # List\n", "                'noise_keyphrases': match_results['noise'],  # List\n", "                'matched_count': len(match_results['matched']),\n", "                'missed_count': len(match_results['missed']),\n", "                'noise_count': len(match_results['noise']),\n", "                'true_total': len(true_keyphrases),\n", "                'predicted_total': len(predicted_keyphrases)\n", "            }\n", "            \n", "            results.append(result)\n", "            \n", "            # Progress update\n", "            if (idx + 1) % 10 == 0:\n", "                print(f\"Processed {idx + 1}/{len(df)} sentences\")\n", "        \n", "        # Create results DataFrame\n", "        results_df = pd.DataFrame(results)\n", "        \n", "        # Calculate overall metrics\n", "        total_matched = results_df['matched_count'].sum()\n", "        total_missed = results_df['missed_count'].sum()\n", "        total_noise = results_df['noise_count'].sum()\n", "        total_true = results_df['true_total'].sum()\n", "        total_predicted = results_df['predicted_total'].sum()\n", "        \n", "        # Calculate metrics\n", "        recall = total_matched / total_true if total_true > 0 else 0\n", "        precision = total_matched / total_predicted if total_predicted > 0 else 0\n", "        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "        \n", "        # Sentence-level exact match accuracy\n", "        exact_matches = sum(1 for _, row in results_df.iterrows() \n", "                           if row['missed_count'] == 0 and row['noise_count'] == 0)\n", "        sentence_level_accuracy = exact_matches / len(results_df) if len(results_df) > 0 else 0\n", "        \n", "        # Save results (convert lists to strings for Excel compatibility)\n", "        results_df_for_excel = results_df.copy()\n", "        list_columns = ['term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases']\n", "        for col in list_columns:\n", "            results_df_for_excel[col] = results_df_for_excel[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else str(x))\n", "        \n", "        results_df_for_excel.to_excel(output_file, index=False)\n", "        \n", "        # Print comprehensive evaluation results\n", "        print(f\"\\n\" + \"=\"*80)\n", "        print(\"IMPROVED EVALUATION RESULTS\")\n", "        print(\"=\"*80)\n", "        print(f\"Total test sentences: {len(results_df)}\")\n", "        print(f\"\\nKEYPHRASE-LEVEL METRICS:\")\n", "        print(f\"  Total true keyphrases: {total_true}\")\n", "        print(f\"  Total predicted keyphrases: {total_predicted}\")\n", "        print(f\"  Matched keyphrases: {total_matched}\")\n", "        print(f\"  Missed keyphrases: {total_missed}\")\n", "        print(f\"  Noise keyphrases: {total_noise}\")\n", "        print(f\"\\nPERFORMANCE METRICS:\")\n", "        print(f\"  Recall (Sensitivity): {recall:.4f} ({recall*100:.2f}%)\")\n", "        print(f\"  Precision: {precision:.4f} ({precision*100:.2f}%)\")\n", "        print(f\"  F1 Score: {f1_score:.4f}\")\n", "        print(f\"\\nSENTENCE-LEVEL METRICS:\")\n", "        print(f\"  Exact match sentences: {exact_matches}/{len(results_df)}\")\n", "        print(f\"  Sentence-level accuracy: {sentence_level_accuracy:.4f} ({sentence_level_accuracy*100:.2f}%)\")\n", "        \n", "        # Show example of improved matching\n", "        if len(results_df) > 0:\n", "            print(f\"\\n\" + \"=\"*50)\n", "            print(\"EXAMPLE OF IMPROVED MATCHING (First sentence)\")\n", "            print(\"=\"*50)\n", "            first_result = results_df.iloc[0]\n", "            print(f\"Sentence: {first_result['sentence'][:100]}...\")\n", "            print(f\"True keyphrases: {first_result['term_found']}\")\n", "            print(f\"Predicted keyphrases: {first_result['predicted_keyphrases']}\")\n", "            print(f\" Matched: {first_result['matched_keyphrases']} ({first_result['matched_count']})\")\n", "            print(f\" Missed: {first_result['missed_keyphrases']} ({first_result['missed_count']})\")\n", "            print(f\" Noise: {first_result['noise_keyphrases']} ({first_result['noise_count']})\")\n", "        \n", "        # Save metrics summary\n", "        metrics_summary = {\n", "            'total_sentences': len(results_df),\n", "            'total_true_keyphrases': int(total_true),\n", "            'total_predicted_keyphrases': int(total_predicted),\n", "            'matched_keyphrases': int(total_matched),\n", "            'missed_keyphrases': int(total_missed),\n", "            'noise_keyphrases': int(total_noise),\n", "            'recall': float(recall),\n", "            'precision': float(precision),\n", "            'f1_score': float(f1_score),\n", "            'sentence_level_accuracy': float(sentence_level_accuracy),\n", "            'exact_match_sentences': int(exact_matches)\n", "        }\n", "        \n", "        # Save metrics to JSON\n", "        metrics_file = output_file.replace('.xlsx', '_metrics.json')\n", "        with open(metrics_file, 'w') as f:\n", "            json.dump(metrics_summary, f, indent=2)\n", "        \n", "        print(f\"\\n\" + \"=\"*80)\n", "        print(\"FILES SAVED:\")\n", "        print(\"=\"*80)\n", "        print(f\"   Detailed results: {output_file}\")\n", "        print(f\"   Metrics summary: {metrics_file}\")\n", "        \n", "        return results_df\n", "    \n", "    def analyze_error_patterns(self, results_df: pd.DataFrame, top_n: int = 10):\n", "        \"\"\"Analyze common error patterns\"\"\"\n", "        print(f\"\\n\" + \"=\"*50)\n", "        print(\"ERROR PATTERN ANALYSIS\")\n", "        print(\"=\"*50)\n", "        \n", "        # Most commonly missed keyphrases\n", "        all_missed = []\n", "        for _, row in results_df.iterrows():\n", "            if isinstance(row['missed_keyphrases'], list):\n", "                all_missed.extend(row['missed_keyphrases'])\n", "        \n", "        if all_missed:\n", "            missed_counts = pd.Series(all_missed).value_counts().head(top_n)\n", "            print(f\"\\nTop {top_n} most commonly MISSED keyphrases:\")\n", "            for i, (keyphrase, count) in enumerate(missed_counts.items(), 1):\n", "                print(f\"  {i:2d}. '{keyphrase}' (missed {count} times)\")\n", "        \n", "        # Most common noise keyphrases\n", "        all_noise = []\n", "        for _, row in results_df.iterrows():\n", "            if isinstance(row['noise_keyphrases'], list):\n", "                all_noise.extend(row['noise_keyphrases'])\n", "        \n", "        if all_noise:\n", "            noise_counts = pd.Series(all_noise).value_counts().head(top_n)\n", "            print(f\"\\nTop {top_n} most common NOISE keyphrases:\")\n", "            for i, (keyphrase, count) in enumerate(noise_counts.items(), 1):\n", "                print(f\"  {i:2d}. '{keyphrase}' (noise {count} times)\")\n", "\n", "def main():\n", "    \"\"\"Main function to run inference and evaluation\"\"\"\n", "    \n", "    # Configuration\n", "    MODEL_PATH = \"training_outputs_v0/models/bert_ner_20250914_103444\"\n", "    INPUT_FILE = \"test_dataset.xlsx\"\n", "    OUTPUT_FILE = \"test_results_improved_evaluation.xlsx\"\n", "    \n", "    try:\n", "        print(\"Initializing BERT Keyphrase Inference with Improved Evaluation...\")\n", "        \n", "        # Initialize inference model\n", "        inference_model = BERTKeyphraseInferenceWithImprovedEvaluation(MODEL_PATH)\n", "        \n", "        # Run prediction and evaluation\n", "        results_df = inference_model.predict_and_evaluate(INPUT_FILE, OUTPUT_FILE)\n", "        \n", "        # Analyze error patterns\n", "        inference_model.analyze_error_patterns(results_df, top_n=10)\n", "        \n", "        print(f\"\\n Improved evaluation completed successfully!\")\n", "        print(f\" Check '{OUTPUT_FILE}' for detailed results with proper matching\")\n", "        \n", "    except FileNotFoundError as e:\n", "        print(f\" File not found: {e}\")\n", "        print(\"Please ensure your model path and test file are correct\")\n", "    except Exception as e:\n", "        print(f\" Error during evaluation: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "# Utility function for testing the matching logic\n", "def test_matching_logic():\n", "    \"\"\"Test the improved matching with your example\"\"\"\n", "    \n", "    term_found = ['Proton', '1H NMR', 'FE-SEM', 'Fourier transform', 'Thermogravimetric Analysis', \n", "                  'field emission', 'XRD', 'Energy', 'X-ray diffraction', 'emission', 'FT-IR', 'adsorbents']\n", "    \n", "    predicted_keyphrases = ['adsorbents', 'x - ray diffraction', 'xrd', 'fourier transform ir', \n", "                           'ft - ir', 'proton', 'thermogravimetric analysis', 'emission']\n", "    \n", "    matcher = ImprovedKeyphraseMatching()\n", "    results = matcher.calculate_matches_improved(term_found, predicted_keyphrases)\n", "    \n", "    print(\"TESTING IMPROVED MATCHING LOGIC\")\n", "    print(\"=\"*50)\n", "    print(f\"True keyphrases: {term_found}\")\n", "    print(f\"Predicted keyphrases: {predicted_keyphrases}\")\n", "    print(f\"\\n Matched ({len(results['matched'])}): {results['matched']}\")\n", "    print(f\" Missed ({len(results['missed'])}): {results['missed']}\")\n", "    print(f\" Noise ({len(results['noise'])}): {results['noise']}\")\n", "    \n", "    return results\n", "\n", "if __name__ == \"__main__\":\n", "    # Test the matching logic first\n", "    print(\"Testing improved matching logic...\")\n", "    test_matching_logic()\n", "    print(\"\\n\" + \"=\"*80)\n", "    \n", "    # Run main evaluation\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f8b402b9-6131-4d9e-b232-9715c382953a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TESTING PARTIAL MATCHING LOGIC\n", "==================================================\n", "Is 'printing' partially in ['3d printing', 'inks', 'mxene']? True\n", "Is 'stems' partially in ['3D printing', 'inks', 'printing', 'stems', 'MXene']? True\n", "Is '3D printing' partially in ['printing material', 'electrodes']? True\n", "\n", "================================================================================\n", "================================================================================\n", "PROCESSING PARTIAL KEYPHRASE MATCHES\n", "================================================================================\n", "Loading existing results from: test_results_improved_evaluation.xlsx\n", "✓ Loaded 1500 records with 11 columns\n", "Columns: ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'matched_count', 'missed_count', 'noise_count', 'true_total', 'predicted_total']\n", "✓ All required columns validated\n", "Available columns: ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'matched_count', 'missed_count', 'noise_count', 'true_total', 'predicted_total']\n", "Processing partial matches...\n", "\n", "DEBUGGING DATAFRAME COLUMNS:\n", "==================================================\n", "DataFrame shape: (1500, 11)\n", "All columns: ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'matched_count', 'missed_count', 'noise_count', 'true_total', 'predicted_total']\n", "\n", "sentence:\n", "  Total rows: 1500\n", "  Non-null values: 1500\n", "  Non-empty values: 1500\n", "\n", "term_found:\n", "  Total rows: 1500\n", "  Non-null values: 1500\n", "  Non-empty values: 1500\n", "  Sample values: ['Proton, 1H NMR, FE-SEM, Fourier transform, Thermogravimetric Analysis, field emission, XRD, Energy, X-ray diffraction, emission, FT-IR, adsorbents', 'environmentally friendly, 3D printing, inks, printing, stems, MXene', 'printing, microdevice, 3D printing']...\n", "  Parsed sample: ['Proton', '1H NMR', 'FE-SEM', 'Fourier transform', 'Thermogravimetric Analysis', 'field emission', 'XRD', 'Energy', 'X-ray diffraction', 'emission', 'FT-IR', 'adsorbents']\n", "\n", "predicted_keyphrases:\n", "  Total rows: 1500\n", "  Non-null values: 1499\n", "  Non-empty values: 1499\n", "  Sample values: ['adsorbents, x - ray diffraction, xrd, fourier transform ir, ft - ir, proton, thermogravimetric analysis, emission', 'environmentally friendly, mxene, inks, 3d printing, electronic', '3d printing, energy storage, electrodes']...\n", "  Parsed sample: ['adsorbents', 'x - ray diffraction', 'xrd', 'fourier transform ir', 'ft - ir', 'proton', 'thermogravimetric analysis', 'emission']\n", "\n", "matched_keyphrases:\n", "  Total rows: 1500\n", "  Non-null values: 1494\n", "  Non-empty values: 1494\n", "  Sample values: ['field emission, Proton, X-ray diffraction, Fourier transform, adsorbents, FT-IR, XRD, Thermogravimetric Analysis', 'environmentally friendly, inks, MXene, 3D printing', 'printing']...\n", "  Parsed sample: ['field emission', 'Proton', 'X-ray diffraction', 'Fourier transform', 'adsorbents', 'FT-IR', 'XRD', 'Thermogravimetric Analysis']\n", "\n", "missed_keyphrases:\n", "  Total rows: 1500\n", "  Non-null values: 1405\n", "  Non-empty values: 1405\n", "  Sample values: ['1H NMR, FE-SEM, Energy, emission', 'printing, stems', 'microdevice, 3D printing']...\n", "  Parsed sample: ['1H NMR', 'FE-SEM', 'Energy', 'emission']\n", "\n", "noise_keyphrases:\n", "  Total rows: 1500\n", "  Non-null values: 518\n", "  Non-empty values: 518\n", "  Sample values: ['electronic', 'energy storage, electrodes', 'mesenchy']...\n", "  Parsed sample: ['electronic']\n", "\n", "Row 0 debug:\n", "  Debug info: {'term_found_count': 12, 'predicted_count': 8, 'original_missed_count': 4, 'original_noise_count': 0}\n", "  Partial matches found: 1\n", "  Partial matches: ['emission']\n", "\n", "Row 1 debug:\n", "  Debug info: {'term_found_count': 6, 'predicted_count': 5, 'original_missed_count': 2, 'original_noise_count': 1}\n", "  Partial matches found: 1\n", "  Partial matches: ['printing']\n", "\n", "Row 2 debug:\n", "  Debug info: {'term_found_count': 3, 'predicted_count': 3, 'original_missed_count': 2, 'original_noise_count': 2}\n", "  Partial matches found: 1\n", "  Partial matches: ['3D printing']\n", "\n", "Processing summary:\n", "  Successful rows: 1500\n", "  Failed rows: 0\n", "  Total partial matches found: 2153\n", "Updating counts...\n", "Count update summary:\n", "  Successful count updates: 1500\n", "  Failed count updates: 0\n", "Preserving original data integrity...\n", "  Updated column: matched_keyphrases\n", "  Updated column: missed_keyphrases\n", "  Updated column: noise_keyphrases\n", "  Updated column: partial_matches\n", "  Updated column: partial_match_count\n", "  Updated column: matched_count\n", "  Updated column: missed_count\n", "  Updated column: noise_count\n", "  Updated column: true_total\n", "  Updated column: predicted_total\n", "  Added new column: partial_match_count\n", "✓ All critical columns preserved\n", "Final dataframe shape: (1500, 13)\n", "Original dataframe shape: (1500, 11)\n", "\n", "Final verification:\n", "  Input rows: 1500\n", "  Output rows: 1500\n", "  Input columns: 11\n", "  Output columns: 13\n", "  Data integrity: ✓ PASSED\n", "\n", "============================================================\n", "METRICS SUMMARY\n", "============================================================\n", "Total sentences processed: 1500\n", "Total matched keyphrases: 5322\n", "Total missed keyphrases: 3065\n", "Total noise keyphrases: 746\n", "Total partial matches found: 2153\n", "\n", "STRICT METRICS (exact matches only):\n", "  Recall: 0.5049 (50.49%)\n", "  Precision: 0.6474 (64.74%)\n", "  F1-Score: 0.5673 (56.73%)\n", "\n", "IMPROVED METRICS (including partial matches):\n", "  Recall: 0.7092 (70.92%)\n", "  Precision: 0.9093 (90.93%)\n", "  F1-Score: 0.7969 (79.69%)\n", "\n", "IMPROVEMENT FROM PARTIAL MATCHING:\n", "  Recall improvement: +0.2043 (20.43%)\n", "  Precision improvement: +0.2619 (26.19%)\n", "  F1 improvement: +0.2295 (22.95%)\n", "\n", "Saving results to test_results_with_partial_matches-2.xlsx...\n", "Converting list columns to strings for Excel export...\n", "  Converted columns: ['term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'partial_matches']\n", "  Original dataframe shape: (1500, 13)\n", "  Excel dataframe shape: (1500, 13)\n", "  All columns preserved: True\n", "  ✓ Successfully saved 1500 rows and 13 columns\n", "  ✓ Metrics saved to: test_results_with_partial_matches-2_partial_metrics.json\n", "\n", "================================================================================\n", "✓ PROCESSING COMPLETED SUCCESSFULLY!\n", "✓ Updated results saved to: 'test_results_with_partial_matches-2.xlsx'\n", "✓ All original data preserved with 13 columns\n", "================================================================================\n", "\n", "Sample of preserved critical columns:\n", "                                                                                               sentence                                                                                                                                         term_found                                                                                              predicted_keyphrases                                                                                                 matched_keyphrases  partial_match_count\n", "The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transfo... Proton, 1H NMR, FE-SEM, Fourier transform, Thermogravimetric Analysis, field emission, XRD, Energy, X-ray diffraction, emission, FT-IR, adsorbents adsorbents, x - ray diffraction, xrd, fourier transform ir, ft - ir, proton, thermogravimetric analysis, emission [field emission, Proton, X-ray diffraction, Fourier transform, adsorbents, FT-IR, XRD, Thermogravimetric Analysis]                    1\n", "Moreover, it provides a new approach for preparing environmentally friendly MXene-based inks for the...                                                                                environmentally friendly, 3D printing, inks, printing, stems, MXene                                                    environmentally friendly, mxene, inks, 3d printing, electronic                                                               [environmentally friendly, inks, MXene, 3D printing]                    1\n", "The use of 3D printing technology in fabricating high-performance energy storage electrodes opens up...                                                                                                                 printing, microdevice, 3D printing                                                                           3d printing, energy storage, electrodes                                                                                                         [printing]                    1\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from typing import List, Dict, Set\n", "import ast\n", "import re\n", "import json\n", "\n", "def normalize_for_partial_match(text: str) -> str:\n", "    \"\"\"Normalize text for partial matching\"\"\"\n", "    if not text or pd.isna(text):\n", "        return \"\"\n", "    text = str(text).lower().strip()\n", "    # Remove extra spaces and normalize punctuation\n", "    text = re.sub(r'[^\\w\\s-]', ' ', text)  # Replace punctuation with spaces except hyphens\n", "    text = re.sub(r'\\s+', ' ', text)  # Replace multiple spaces with single space\n", "    return text.strip()\n", "\n", "def get_words_from_phrase(phrase: str) -> Set[str]:\n", "    \"\"\"Extract individual words from a phrase\"\"\"\n", "    if not phrase or pd.isna(phrase):\n", "        return set()\n", "    \n", "    normalized = normalize_for_partial_match(phrase)\n", "    # Split on spaces and hyphens, filter out empty strings\n", "    words = set()\n", "    for word in re.split(r'[\\s\\-]+', normalized):\n", "        word = word.strip()\n", "        if word and len(word) > 1:  # Only include words with more than 1 character\n", "            words.add(word)\n", "    return words\n", "\n", "def is_partial_match(term: str, candidate_list: List[str]) -> bool:\n", "    \"\"\"\n", "    Check if any word from term appears in any phrase from candidate_list\n", "    \"\"\"\n", "    if not term or not candidate_list:\n", "        return False\n", "    \n", "    term_words = get_words_from_phrase(term)\n", "    if not term_words:\n", "        return False\n", "    \n", "    for candidate in candidate_list:\n", "        if not candidate:\n", "            continue\n", "            \n", "        candidate_words = get_words_from_phrase(candidate)\n", "        if not candidate_words:\n", "            continue\n", "            \n", "        # Check for word-level overlap\n", "        if term_words & candidate_words:  # Set intersection\n", "            return True\n", "    \n", "    return False\n", "\n", "def parse_keyphrase_column(value, debug_info=\"\") -> List[str]:\n", "    \"\"\"\n", "    Parse keyphrase column that might be a list, stringified list, or comma-separated string\n", "    Enhanced with debugging and better error handling\n", "    \"\"\"\n", "    if pd.isna(value) or not value:\n", "        return []\n", "    \n", "    # Handle None, NaN, empty string cases\n", "    if value is None or str(value).strip() == '' or str(value).strip().lower() in ['nan', 'none']:\n", "        return []\n", "    \n", "    # If it's already a list\n", "    if isinstance(value, list):\n", "        result = [str(item).strip() for item in value if str(item).strip() and str(item).strip().lower() not in ['nan', 'none', '']]\n", "        return result\n", "    \n", "    # Convert to string and process\n", "    value_str = str(value).strip()\n", "    \n", "    # Handle stringified lists like \"[item1, item2]\"\n", "    if value_str.startswith('[') and value_str.endswith(']'):\n", "        try:\n", "            parsed = ast.literal_eval(value_str)\n", "            if isinstance(parsed, list):\n", "                result = [str(item).strip() for item in parsed if str(item).strip() and str(item).strip().lower() not in ['nan', 'none', '']]\n", "                return result\n", "        except (ValueError, SyntaxError) as e:\n", "            # If literal_eval fails, try to parse manually\n", "            content = value_str[1:-1]  # Remove brackets\n", "            if content.strip():\n", "                items = [item.strip().strip('\"').strip(\"'\") for item in content.split(',')]\n", "                result = [item for item in items if item and item.lower() not in ['nan', 'none', '']]\n", "                return result\n", "    \n", "    # Handle comma-separated values\n", "    if ',' in value_str:\n", "        items = []\n", "        for item in value_str.split(','):\n", "            item = item.strip().strip('\"').strip(\"'\")  # Remove quotes and whitespace\n", "            if item and item.lower() not in ['nan', 'none', '']:\n", "                items.append(item)\n", "        return items\n", "    \n", "    # Single item\n", "    if value_str and value_str.lower() not in ['nan', 'none', '']:\n", "        return [value_str]\n", "    \n", "    return []\n", "\n", "def debug_dataframe_columns(df: pd.DataFrame) -> None:\n", "    \"\"\"Debug function to check the state of key columns\"\"\"\n", "    print(\"\\nDEBUGGING DATAFRAME COLUMNS:\")\n", "    print(\"=\"*50)\n", "    print(f\"DataFrame shape: {df.shape}\")\n", "    print(f\"All columns: {list(df.columns)}\")\n", "    \n", "    # Check key columns\n", "    key_columns = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases']\n", "    \n", "    for col in key_columns:\n", "        if col in df.columns:\n", "            non_null_count = df[col].notna().sum()\n", "            non_empty_count = df[col].apply(lambda x: bool(str(x).strip()) and str(x).strip().lower() not in ['nan', 'none']).sum()\n", "            print(f\"\\n{col}:\")\n", "            print(f\"  Total rows: {len(df)}\")\n", "            print(f\"  Non-null values: {non_null_count}\")\n", "            print(f\"  Non-empty values: {non_empty_count}\")\n", "            \n", "            # Show sample values for non-sentence columns\n", "            if col != 'sentence':\n", "                sample_values = df[col].dropna().head(3).tolist()\n", "                print(f\"  Sample values: {sample_values[:100]}...\")  # Limit output length\n", "                \n", "                # Test parsing on sample\n", "                if sample_values:\n", "                    test_val = sample_values[0]\n", "                    parsed = parse_keyphrase_column(test_val, f\"{col}_sample\")\n", "                    print(f\"  Parsed sample: {parsed}\")\n", "        else:\n", "            print(f\"\\n{col}: COLUMN MISSING!\")\n", "\n", "def find_partial_matches_in_row(row: pd.Series) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    Process a single row to find partial matches with enhanced debugging\n", "    \"\"\"\n", "    # Parse all columns with debugging\n", "    term_found_list = parse_keyphrase_column(row.get('term_found', ''), 'term_found')\n", "    predicted_list = parse_keyphrase_column(row.get('predicted_keyphrases', ''), 'predicted_keyphrases')\n", "    missed_list = parse_keyphrase_column(row.get('missed_keyphrases', ''), 'missed_keyphrases')\n", "    noise_list = parse_keyphrase_column(row.get('noise_keyphrases', ''), 'noise_keyphrases')\n", "    \n", "    # Keep existing matched keyphrases\n", "    matched_list = parse_keyphrase_column(row.get('matched_keyphrases', ''), 'matched_keyphrases')\n", "    \n", "    partial_matches = []\n", "    new_missed = []\n", "    new_noise = []\n", "    \n", "    # Process missed keyphrases - check if they partially match predicted keyphrases\n", "    for missed_kp in missed_list:\n", "        if is_partial_match(missed_kp, predicted_list):\n", "            partial_matches.append(missed_kp)\n", "        else:\n", "            new_missed.append(missed_kp)\n", "    \n", "    # Process noise keyphrases - check if they partially match term_found\n", "    for noise_kp in noise_list:\n", "        if is_partial_match(noise_kp, term_found_list):\n", "            partial_matches.append(noise_kp)\n", "        else:\n", "            new_noise.append(noise_kp)\n", "    \n", "    return {\n", "        'matched_keyphrases': matched_list,\n", "        'missed_keyphrases': new_missed,\n", "        'noise_keyphrases': new_noise,\n", "        'partial_matches': partial_matches,\n", "        'debug_info': {\n", "            'term_found_count': len(term_found_list),\n", "            'predicted_count': len(predicted_list),\n", "            'original_missed_count': len(missed_list),\n", "            'original_noise_count': len(noise_list)\n", "        }\n", "    }\n", "\n", "def calculate_counts_for_row(row: pd.Series) -> Dict[str, int]:\n", "    \"\"\"Calculate counts for a single row\"\"\"\n", "    matched_list = row.get('matched_keyphrases', [])\n", "    missed_list = row.get('missed_keyphrases', [])\n", "    noise_list = row.get('noise_keyphrases', [])\n", "    partial_list = row.get('partial_matches', [])\n", "    \n", "    # Ensure they are lists\n", "    if not isinstance(matched_list, list):\n", "        matched_list = parse_keyphrase_column(matched_list)\n", "    if not isinstance(missed_list, list):\n", "        missed_list = parse_keyphrase_column(missed_list)\n", "    if not isinstance(noise_list, list):\n", "        noise_list = parse_keyphrase_column(noise_list)\n", "    if not isinstance(partial_list, list):\n", "        partial_list = parse_keyphrase_column(partial_list)\n", "    \n", "    matched_count = len(matched_list)\n", "    missed_count = len(missed_list)\n", "    noise_count = len(noise_list)\n", "    partial_match_count = len(partial_list)\n", "    \n", "    # Calculate totals\n", "    true_total = matched_count + missed_count + partial_match_count\n", "    predicted_total = matched_count + noise_count + partial_match_count\n", "    \n", "    return {\n", "        'matched_count': matched_count,\n", "        'missed_count': missed_count,\n", "        'noise_count': noise_count,\n", "        'partial_match_count': partial_match_count,\n", "        'true_total': true_total,\n", "        'predicted_total': predicted_total\n", "    }\n", "\n", "def preserve_original_data(df_original: pd.DataFrame, df_processed: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Ensure all original columns are preserved in the final output\n", "    \"\"\"\n", "    print(\"Preserving original data integrity...\")\n", "    \n", "    # Start with original dataframe to preserve all columns and data types\n", "    df_final = df_original.copy()\n", "    \n", "    # List of columns that should be updated with processed data\n", "    columns_to_update = [\n", "        'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', \n", "        'partial_matches', 'partial_match_count',\n", "        'matched_count', 'missed_count', 'noise_count', \n", "        'true_total', 'predicted_total'\n", "    ]\n", "    \n", "    # Update only the processed columns, keeping everything else intact\n", "    for col in columns_to_update:\n", "        if col in df_processed.columns:\n", "            df_final[col] = df_processed[col]\n", "            print(f\"  Updated column: {col}\")\n", "    \n", "    # Add new partial_match_count column if it doesn't exist in original\n", "    if 'partial_match_count' not in df_original.columns and 'partial_match_count' in df_processed.columns:\n", "        df_final['partial_match_count'] = df_processed['partial_match_count']\n", "        print(\"  Added new column: partial_match_count\")\n", "    \n", "    # Verify critical columns are preserved\n", "    critical_columns = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases']\n", "    missing_critical = [col for col in critical_columns if col not in df_final.columns]\n", "    \n", "    if missing_critical:\n", "        print(f\"WARNING: Missing critical columns in final output: {missing_critical}\")\n", "    else:\n", "        print(\" All critical columns preserved\")\n", "    \n", "    print(f\"Final dataframe shape: {df_final.shape}\")\n", "    print(f\"Original dataframe shape: {df_original.shape}\")\n", "    \n", "    return df_final\n", "\n", "def process_partial_matches(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Process DataFrame to identify and move partial matches while preserving all original data\n", "    \"\"\"\n", "    print(\"Processing partial matches...\")\n", "    \n", "    # Store original dataframe for data preservation\n", "    df_original = df.copy()\n", "    \n", "    # Create working copy for processing\n", "    df_working = df.copy()\n", "    \n", "    # Debug the dataframe first\n", "    debug_dataframe_columns(df_working)\n", "    \n", "    # Initialize partial_matches column if it doesn't exist\n", "    if 'partial_matches' not in df_working.columns:\n", "        df_working['partial_matches'] = [[] for _ in range(len(df_working))]\n", "    \n", "    # Initialize partial_match_count column if it doesn't exist  \n", "    if 'partial_match_count' not in df_working.columns:\n", "        df_working['partial_match_count'] = 0\n", "    \n", "    total_partial_matches = 0\n", "    successful_rows = 0\n", "    failed_rows = 0\n", "    \n", "    # Process each row\n", "    for idx, row in df_working.iterrows():\n", "        try:\n", "            partial_results = find_partial_matches_in_row(row)\n", "            \n", "            # Update the row with processed results\n", "            df_working.at[idx, 'matched_keyphrases'] = partial_results['matched_keyphrases']\n", "            df_working.at[idx, 'missed_keyphrases'] = partial_results['missed_keyphrases'] \n", "            df_working.at[idx, 'noise_keyphrases'] = partial_results['noise_keyphrases']\n", "            df_working.at[idx, 'partial_matches'] = partial_results['partial_matches']\n", "            \n", "            # Count partial matches found in this row\n", "            partial_count = len(partial_results['partial_matches'])\n", "            total_partial_matches += partial_count\n", "            successful_rows += 1\n", "            \n", "            # Debug first few rows\n", "            if idx < 3:\n", "                print(f\"\\nRow {idx} debug:\")\n", "                print(f\"  Debug info: {partial_results['debug_info']}\")\n", "                print(f\"  Partial matches found: {partial_count}\")\n", "                if partial_count > 0:\n", "                    print(f\"  Partial matches: {partial_results['partial_matches']}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"  Error processing row {idx}: {e}\")\n", "            failed_rows += 1\n", "            continue\n", "    \n", "    print(f\"\\nProcessing summary:\")\n", "    print(f\"  Successful rows: {successful_rows}\")\n", "    print(f\"  Failed rows: {failed_rows}\")\n", "    print(f\"  Total partial matches found: {total_partial_matches}\")\n", "    \n", "    # Calculate updated counts for all rows\n", "    print(\"Updating counts...\")\n", "    count_update_success = 0\n", "    count_update_failed = 0\n", "    \n", "    for idx, row in df_working.iterrows():\n", "        try:\n", "            counts = calculate_counts_for_row(row)\n", "            df_working.at[idx, 'matched_count'] = counts['matched_count']\n", "            df_working.at[idx, 'missed_count'] = counts['missed_count'] \n", "            df_working.at[idx, 'noise_count'] = counts['noise_count']\n", "            df_working.at[idx, 'partial_match_count'] = counts['partial_match_count']\n", "            df_working.at[idx, 'true_total'] = counts['true_total']\n", "            df_working.at[idx, 'predicted_total'] = counts['predicted_total']\n", "            count_update_success += 1\n", "        except Exception as e:\n", "            print(f\"  Error updating counts for row {idx}: {e}\")\n", "            count_update_failed += 1\n", "            continue\n", "    \n", "    print(f\"Count update summary:\")\n", "    print(f\"  Successful count updates: {count_update_success}\")\n", "    print(f\"  Failed count updates: {count_update_failed}\")\n", "    \n", "    # Preserve all original data while incorporating processed results\n", "    df_final = preserve_original_data(df_original, df_working)\n", "    \n", "    return df_final\n", "\n", "def calculate_metrics(df: pd.DataFrame) -> Dict:\n", "    \"\"\"Calculate metrics including partial matches\"\"\"\n", "    # Ensure count columns exist and are numeric\n", "    count_columns = ['matched_count', 'missed_count', 'noise_count', 'partial_match_count']\n", "    for col in count_columns:\n", "        if col in df.columns:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)\n", "        else:\n", "            df[col] = 0\n", "    \n", "    total_matched = df['matched_count'].sum()\n", "    total_missed = df['missed_count'].sum()\n", "    total_noise = df['noise_count'].sum()\n", "    total_partial = df['partial_match_count'].sum()\n", "    \n", "    total_true = total_matched + total_missed + total_partial\n", "    total_predicted = total_matched + total_noise + total_partial\n", "    \n", "    # Calculate improved metrics (including partial matches)\n", "    improved_recall = (total_matched + total_partial) / total_true if total_true > 0 else 0\n", "    improved_precision = (total_matched + total_partial) / total_predicted if total_predicted > 0 else 0\n", "    improved_f1 = 2 * improved_precision * improved_recall / (improved_precision + improved_recall) if (improved_precision + improved_recall) > 0 else 0\n", "    \n", "    # Traditional metrics (exact matches only)\n", "    strict_recall = total_matched / total_true if total_true > 0 else 0\n", "    strict_precision = total_matched / total_predicted if total_predicted > 0 else 0\n", "    strict_f1 = 2 * strict_precision * strict_recall / (strict_precision + strict_recall) if (strict_precision + strict_recall) > 0 else 0\n", "    \n", "    return {\n", "        'total_sentences': len(df),\n", "        'total_matched': int(total_matched),\n", "        'total_missed': int(total_missed),\n", "        'total_noise': int(total_noise), \n", "        'total_partial_matches': int(total_partial),\n", "        'total_true_keyphrases': int(total_true),\n", "        'total_predicted_keyphrases': int(total_predicted),\n", "        'strict_recall': float(strict_recall),\n", "        'strict_precision': float(strict_precision),\n", "        'strict_f1': float(strict_f1),\n", "        'improved_recall': float(improved_recall),\n", "        'improved_precision': float(improved_precision),\n", "        'improved_f1': float(improved_f1)\n", "    }\n", "\n", "def convert_lists_to_strings_safe(df: pd.DataFrame, list_columns: List[str]) -> pd.DataFrame:\n", "    \"\"\"\n", "    Convert list columns to comma-separated strings for Excel export\n", "    Only processes specified columns, preserves all others\n", "    \"\"\"\n", "    print(\"Converting list columns to strings for Excel export...\")\n", "    df_for_excel = df.copy()\n", "    \n", "    converted_cols = []\n", "    skipped_cols = []\n", "    \n", "    for col in list_columns:\n", "        if col in df_for_excel.columns:\n", "            try:\n", "                df_for_excel[col] = df_for_excel[col].apply(\n", "                    lambda x: ', '.join(x) if isinstance(x, list) and x else (x if pd.notna(x) else '')\n", "                )\n", "                converted_cols.append(col)\n", "            except Exception as e:\n", "                print(f\"  Warning: Could not convert column {col}: {e}\")\n", "                skipped_cols.append(col)\n", "        else:\n", "            skipped_cols.append(f\"{col} (missing)\")\n", "    \n", "    print(f\"  Converted columns: {converted_cols}\")\n", "    if skipped_cols:\n", "        print(f\"  Skipped columns: {skipped_cols}\")\n", "    \n", "    return df_for_excel\n", "\n", "def print_metrics_summary(metrics: Dict) -> None:\n", "    \"\"\"Print formatted metrics summary\"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"METRICS SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    print(f\"Total sentences processed: {metrics['total_sentences']}\")\n", "    print(f\"Total matched keyphrases: {metrics['total_matched']}\")\n", "    print(f\"Total missed keyphrases: {metrics['total_missed']}\")\n", "    print(f\"Total noise keyphrases: {metrics['total_noise']}\")\n", "    print(f\"Total partial matches found: {metrics['total_partial_matches']}\")\n", "    \n", "    print(f\"\\nSTRICT METRICS (exact matches only):\")\n", "    print(f\"  Recall: {metrics['strict_recall']:.4f} ({metrics['strict_recall']*100:.2f}%)\")\n", "    print(f\"  Precision: {metrics['strict_precision']:.4f} ({metrics['strict_precision']*100:.2f}%)\")\n", "    print(f\"  F1-Score: {metrics['strict_f1']:.4f} ({metrics['strict_f1']*100:.2f}%)\")\n", "    \n", "    print(f\"\\nIMPROVED METRICS (including partial matches):\")\n", "    print(f\"  Recall: {metrics['improved_recall']:.4f} ({metrics['improved_recall']*100:.2f}%)\")\n", "    print(f\"  Precision: {metrics['improved_precision']:.4f} ({metrics['improved_precision']*100:.2f}%)\")\n", "    print(f\"  F1-Score: {metrics['improved_f1']:.4f} ({metrics['improved_f1']*100:.2f}%)\")\n", "    \n", "    improvement_recall = metrics['improved_recall'] - metrics['strict_recall']\n", "    improvement_precision = metrics['improved_precision'] - metrics['strict_precision']\n", "    improvement_f1 = metrics['improved_f1'] - metrics['strict_f1']\n", "    \n", "    print(f\"\\nIMPROVEMENT FROM PARTIAL MATCHING:\")\n", "    print(f\"  Recall improvement: +{improvement_recall:.4f} ({improvement_recall*100:.2f}%)\")\n", "    print(f\"  Precision improvement: +{improvement_precision:.4f} ({improvement_precision*100:.2f}%)\")\n", "    print(f\"  F1 improvement: +{improvement_f1:.4f} ({improvement_f1*100:.2f}%)\")\n", "\n", "def validate_required_columns(df: pd.DataFrame) -> None:\n", "    \"\"\"Validate that required columns exist in DataFrame\"\"\"\n", "    critical_columns = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases']\n", "    required_columns = ['missed_keyphrases', 'noise_keyphrases']\n", "    \n", "    missing_critical = [col for col in critical_columns if col not in df.columns]\n", "    missing_required = [col for col in required_columns if col not in df.columns]\n", "    \n", "    if missing_critical:\n", "        raise ValueError(f\"CRITICAL columns missing: {missing_critical}. Cannot proceed.\")\n", "    \n", "    if missing_required:\n", "        print(f\"Warning: Missing optional columns: {missing_required}\")\n", "        print(\"These columns will be created with empty values.\")\n", "        \n", "        # Create missing columns with empty values\n", "        for col in missing_required:\n", "            df[col] = ''\n", "    \n", "    print(f\" All required columns validated\")\n", "    print(f\"Available columns: {list(df.columns)}\")\n", "\n", "def save_results(df: pd.DataFrame, output_file: str, metrics: Dict) -> None:\n", "    \"\"\"Save results and metrics to files with full data preservation\"\"\"\n", "    print(f\"\\nSaving results to {output_file}...\")\n", "    \n", "    # Define which columns need list-to-string conversion\n", "    list_columns = ['term_found', 'predicted_keyphrases', 'matched_keyphrases', \n", "                    'missed_keyphrases', 'noise_keyphrases', 'partial_matches']\n", "    \n", "    # Convert list columns to strings for Excel compatibility\n", "    df_for_excel = convert_lists_to_strings_safe(df, list_columns)\n", "    \n", "    # Verify data integrity before saving\n", "    print(f\"  Original dataframe shape: {df.shape}\")\n", "    print(f\"  Excel dataframe shape: {df_for_excel.shape}\")\n", "    print(f\"  All columns preserved: {len(df.columns) == len(df_for_excel.columns)}\")\n", "    \n", "    # Save to Excel with all columns\n", "    try:\n", "        df_for_excel.to_excel(output_file, index=False)\n", "        print(f\"   Successfully saved {len(df_for_excel)} rows and {len(df_for_excel.columns)} columns\")\n", "    except Exception as e:\n", "        print(f\"   Error saving Excel file: {e}\")\n", "        raise\n", "    \n", "    # Save metrics\n", "    metrics_file = output_file.replace('.xlsx', '_partial_metrics.json')\n", "    try:\n", "        with open(metrics_file, 'w') as f:\n", "            json.dump(metrics, f, indent=2)\n", "        print(f\"   Metrics saved to: {metrics_file}\")\n", "    except Exception as e:\n", "        print(f\"   Error saving metrics: {e}\")\n", "\n", "\n", "\n", "def process_existing_excel_file(input_file: str, output_file: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Process existing Excel file to add partial matches with full data preservation\n", "    \"\"\"\n", "    print(\"=\"*80)\n", "    print(\"PROCESSING PARTIAL KEYPHRASE MATCHES\")\n", "    print(\"=\"*80)\n", "    \n", "    # Load existing results\n", "    print(f\"Loading existing results from: {input_file}\")\n", "    try:\n", "        df = pd.read_excel(input_file)\n", "    except Exception as e:\n", "        print(f\"Error loading file: {e}\")\n", "        raise\n", "    \n", "    print(f\" Loaded {len(df)} records with {len(df.columns)} columns\")\n", "    print(f\"Columns: {list(df.columns)}\")\n", "    \n", "    # Validate required columns\n", "    validate_required_columns(df)\n", "    \n", "    # Process partial matches\n", "    df_updated = process_partial_matches(df)\n", "    \n", "    # Final verification\n", "    print(f\"\\nFinal verification:\")\n", "    print(f\"  Input rows: {len(df)}\")\n", "    print(f\"  Output rows: {len(df_updated)}\")\n", "    print(f\"  Input columns: {len(df.columns)}\")\n", "    print(f\"  Output columns: {len(df_updated.columns)}\")\n", "    print(f\"  Data integrity: {' PASSED' if len(df) == len(df_updated) else ' FAILED'}\")\n", "    \n", "    # Calculate metrics\n", "    metrics = calculate_metrics(df_updated)\n", "    \n", "    # Print metrics summary\n", "    print_metrics_summary(metrics)\n", "    \n", "    # Save results\n", "    save_results(df_updated, output_file, metrics)\n", "    \n", "    return df_updated\n", "\n", "\n", "def test_partial_matching() -> None:\n", "    \"\"\"Test the partial matching logic with examples\"\"\"\n", "    print(\"TESTING PARTIAL MATCHING LOGIC\")\n", "    print(\"=\"*50)\n", "    \n", "    # Test case 1: \"printing\" should match \"3D printing\"\n", "    predicted = ['3d printing', 'inks', 'mxene']\n", "    is_partial = is_partial_match('printing', predicted)\n", "    print(f\"Is 'printing' partially in {predicted}? {is_partial}\")\n", "    \n", "    # Test case 2: \"stems\" should match items in term_found\n", "    term_found = ['3D printing', 'inks', 'printing', 'stems', 'MXene']\n", "    is_partial_noise = is_partial_match('stems', term_found)\n", "    print(f\"Is 'stems' partially in {term_found}? {is_partial_noise}\")\n", "    \n", "    # Test case 3: \"3D printing\" should match \"printing\"\n", "    predicted2 = ['printing material', 'electrodes']\n", "    is_partial_3d = is_partial_match('3D printing', predicted2)\n", "    print(f\"Is '3D printing' partially in {predicted2}? {is_partial_3d}\")\n", "\n", "\n", "\n", "\n", "\n", "def main():\n", "    \"\"\"Main execution function\"\"\"\n", "    \n", "    test_partial_matching()\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    \n", "    # Process Excel file\n", "    try:\n", "        INPUT_FILE = \"test_results_improved_evaluation.xlsx\"   \n", "        OUTPUT_FILE = \"test_results_with_partial_matches-2.xlsx\"\n", "        \n", "        df_updated = process_existing_excel_file(INPUT_FILE, OUTPUT_FILE)\n", "        \n", "        print(f\"\\n\" + \"=\"*80)\n", "        print(f\" PROCESSING COMPLETED SUCCESSFULLY!\")\n", "        print(f\" Updated results saved to: '{OUTPUT_FILE}'\")\n", "        print(f\" All original data preserved with {len(df_updated.columns)} columns\")\n", "        print(\"=\"*80)\n", "        \n", "        # sample of the critical columns to verify \n", "        critical_cols = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'partial_match_count']\n", "        available_cols = [col for col in critical_cols if col in df_updated.columns]\n", "        \n", "        if available_cols:\n", "            print(f\"\\nSample of preserved critical columns:\")\n", "            sample_df = df_updated[available_cols].head(3)\n", "            for col in sample_df.columns:\n", "                if col == 'sentence':\n", "                    sample_df[col] = sample_df[col].astype(str).str[:100] + '...'  # Truncate for display\n", "            print(sample_df.to_string(index=False))\n", "        \n", "    except FileNotFoundError:\n", "        print(f\" ERROR: File '{INPUT_FILE}' not found.\")\n", "        print(\"Please ensure the input file path is correct.\")\n", "    except Exception as e:\n", "        print(f\" ERROR: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 20, "id": "a5958750-9e5f-48bd-b523-a5315e1f97b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sentence</th>\n", "      <th>term_found</th>\n", "      <th>predicted_keyphrases</th>\n", "      <th>matched_keyphrases</th>\n", "      <th>missed_keyphrases</th>\n", "      <th>noise_keyphrases</th>\n", "      <th>matched_count</th>\n", "      <th>missed_count</th>\n", "      <th>noise_count</th>\n", "      <th>true_total</th>\n", "      <th>predicted_total</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The synthesized adsorbents underwent character...</td>\n", "      <td>Proton, 1H NMR, FE-SEM, Fourier transform, The...</td>\n", "      <td>adsorbents, x - ray diffraction, xrd, fourier ...</td>\n", "      <td>field emission, Proton, X-ray diffraction, Fou...</td>\n", "      <td>1H NMR, FE-SEM, Energy, emission</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Moreover, it provides a new approach for prepa...</td>\n", "      <td>environmentally friendly, 3D printing, inks, p...</td>\n", "      <td>environmentally friendly, mxene, inks, 3d prin...</td>\n", "      <td>environmentally friendly, inks, MXene, 3D prin...</td>\n", "      <td>printing, stems</td>\n", "      <td>electronic</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The use of 3D printing technology in fabricati...</td>\n", "      <td>printing, microdevice, 3D printing</td>\n", "      <td>3d printing, energy storage, electrodes</td>\n", "      <td>printing</td>\n", "      <td>microdevice, 3D printing</td>\n", "      <td>energy storage, electrodes</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>By mixing with graphene oxide in a certain pro...</td>\n", "      <td>three-dimensional, 3D printing, reed, printing...</td>\n", "      <td>3d printing, three - dimensional, network</td>\n", "      <td>network, three-dimensional, 3D printing</td>\n", "      <td>reed, printing, ink</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>In this work, the Micro-Supercapacitor (MSC) f...</td>\n", "      <td>printing, Supercapacitor, Electrochemical perf...</td>\n", "      <td>supercapacitor, 3d printing, electrochemical p...</td>\n", "      <td>Electrochemical performance, printing, Superca...</td>\n", "      <td>3D printing</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            sentence  \\\n", "0  The synthesized adsorbents underwent character...   \n", "1  Moreover, it provides a new approach for prepa...   \n", "2  The use of 3D printing technology in fabricati...   \n", "3  By mixing with graphene oxide in a certain pro...   \n", "4  In this work, the Micro-Supercapacitor (MSC) f...   \n", "\n", "                                          term_found  \\\n", "0  Proton, 1H NMR, FE-SEM, Fourier transform, The...   \n", "1  environmentally friendly, 3D printing, inks, p...   \n", "2                 printing, microdevice, 3D printing   \n", "3  three-dimensional, 3D printing, reed, printing...   \n", "4  printing, Supercapacitor, Electrochemical perf...   \n", "\n", "                                predicted_keyphrases  \\\n", "0  adsorbents, x - ray diffraction, xrd, fourier ...   \n", "1  environmentally friendly, mxene, inks, 3d prin...   \n", "2            3d printing, energy storage, electrodes   \n", "3          3d printing, three - dimensional, network   \n", "4  supercapacitor, 3d printing, electrochemical p...   \n", "\n", "                                  matched_keyphrases  \\\n", "0  field emission, Proton, X-ray diffraction, Fou...   \n", "1  environmentally friendly, inks, MXene, 3D prin...   \n", "2                                           printing   \n", "3            network, three-dimensional, 3D printing   \n", "4  Electrochemical performance, printing, Superca...   \n", "\n", "                  missed_keyphrases            noise_keyphrases  \\\n", "0  1H NMR, FE-SEM, Energy, emission                         NaN   \n", "1                   printing, stems                  electronic   \n", "2          microdevice, 3D printing  energy storage, electrodes   \n", "3               reed, printing, ink                         NaN   \n", "4                       3D printing                         NaN   \n", "\n", "   matched_count  missed_count  noise_count  true_total  predicted_total  \n", "0              8             4            0          12                8  \n", "1              4             2            1           6                5  \n", "2              1             2            2           3                3  \n", "3              3             3            0           6                3  \n", "4              3             1            0           4                3  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["INPUT_FILE = \"test_results_improved_evaluation.xlsx\"   \n", "df = pd.read_excel(INPUT_FILE)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "6c3d2fd5-6f04-4e12-b977-2df9e37a2030", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Proton, 1H NMR, FE-SEM, Fourier transform, Thermogravimetric Analysis, field emission, XRD, Energy, X-ray diffraction, emission, FT-IR, adsorbents'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df.term_found[0]            "]}, {"cell_type": "code", "execution_count": 25, "id": "c01b61a5-7320-4a3c-ad42-81dd0a85b199", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sentence</th>\n", "      <th>term_found</th>\n", "      <th>predicted_keyphrases</th>\n", "      <th>matched_keyphrases</th>\n", "      <th>missed_keyphrases</th>\n", "      <th>noise_keyphrases</th>\n", "      <th>matched_count</th>\n", "      <th>missed_count</th>\n", "      <th>noise_count</th>\n", "      <th>true_total</th>\n", "      <th>predicted_total</th>\n", "      <th>partial_matches</th>\n", "      <th>partial_match_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The synthesized adsorbents underwent character...</td>\n", "      <td>Proton, 1H NMR, FE-SEM, Fourier transform, The...</td>\n", "      <td>adsorbents, x - ray diffraction, xrd, fourier ...</td>\n", "      <td>field emission, Proton, X-ray diffraction, Fou...</td>\n", "      <td>1H NMR, FE-SEM, Energy</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>9</td>\n", "      <td>emission</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Moreover, it provides a new approach for prepa...</td>\n", "      <td>environmentally friendly, 3D printing, inks, p...</td>\n", "      <td>environmentally friendly, mxene, inks, 3d prin...</td>\n", "      <td>environmentally friendly, inks, MXene, 3D prin...</td>\n", "      <td>stems</td>\n", "      <td>electronic</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>printing</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The use of 3D printing technology in fabricati...</td>\n", "      <td>printing, microdevice, 3D printing</td>\n", "      <td>3d printing, energy storage, electrodes</td>\n", "      <td>printing</td>\n", "      <td>microdevice</td>\n", "      <td>energy storage, electrodes</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>3D printing</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>By mixing with graphene oxide in a certain pro...</td>\n", "      <td>three-dimensional, 3D printing, reed, printing...</td>\n", "      <td>3d printing, three - dimensional, network</td>\n", "      <td>network, three-dimensional, 3D printing</td>\n", "      <td>reed, ink</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>printing</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>In this work, the Micro-Supercapacitor (MSC) f...</td>\n", "      <td>printing, Supercapacitor, Electrochemical perf...</td>\n", "      <td>supercapacitor, 3d printing, electrochemical p...</td>\n", "      <td>Electrochemical performance, printing, Superca...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>3D printing</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            sentence  \\\n", "0  The synthesized adsorbents underwent character...   \n", "1  Moreover, it provides a new approach for prepa...   \n", "2  The use of 3D printing technology in fabricati...   \n", "3  By mixing with graphene oxide in a certain pro...   \n", "4  In this work, the Micro-Supercapacitor (MSC) f...   \n", "\n", "                                          term_found  \\\n", "0  Proton, 1H NMR, FE-SEM, Fourier transform, The...   \n", "1  environmentally friendly, 3D printing, inks, p...   \n", "2                 printing, microdevice, 3D printing   \n", "3  three-dimensional, 3D printing, reed, printing...   \n", "4  printing, Supercapacitor, Electrochemical perf...   \n", "\n", "                                predicted_keyphrases  \\\n", "0  adsorbents, x - ray diffraction, xrd, fourier ...   \n", "1  environmentally friendly, mxene, inks, 3d prin...   \n", "2            3d printing, energy storage, electrodes   \n", "3          3d printing, three - dimensional, network   \n", "4  supercapacitor, 3d printing, electrochemical p...   \n", "\n", "                                  matched_keyphrases       missed_keyphrases  \\\n", "0  field emission, Proton, X-ray diffraction, Fou...  1H NMR, FE-SEM, Energy   \n", "1  environmentally friendly, inks, MXene, 3D prin...                   stems   \n", "2                                           printing             microdevice   \n", "3            network, three-dimensional, 3D printing               reed, ink   \n", "4  Electrochemical performance, printing, Superca...                     NaN   \n", "\n", "             noise_keyphrases  matched_count  missed_count  noise_count  \\\n", "0                         NaN              8             3            0   \n", "1                  electronic              4             1            1   \n", "2  energy storage, electrodes              1             1            2   \n", "3                         NaN              3             2            0   \n", "4                         NaN              3             0            0   \n", "\n", "   true_total  predicted_total partial_matches  partial_match_count  \n", "0          12                9        emission                    1  \n", "1           6                6        printing                    1  \n", "2           3                4     3D printing                    1  \n", "3           6                4        printing                    1  \n", "4           4                4     3D printing                    1  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["OUTPUT_FILE = \"test_results_with_partial_matches-2.xlsx\"\n", "df2 = pd.read_excel(OUTPUT_FILE)\n", "df2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "419ed0b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cba47075", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TESTING TERM VALIDATION LOGIC\n", "==================================================\n", "\n", "Test case 1:\n", "Sentence: The synthesized adsorbents underwent characterization using X-ray diffraction (X...\n", "Input terms: ['adsorbents', 'X-ray diffraction', 'XRD', 'characterization', 'missing-term']\n", "Validated terms: ['adsorbents', 'X-ray diffraction', 'XRD', 'characterization']\n", "  'adsorbents' -> ✓\n", "  'X-ray diffraction' -> ✓\n", "  'XRD' -> ✓\n", "  'characterization' -> ✓\n", "  'missing-term' -> ✗\n", "\n", "Test case 2:\n", "Sentence: 3D printing technology demonstrates excellent performance...\n", "Input terms: ['3D printing', 'printing', 'technology', 'performance', 'missing']\n", "Validated terms: ['3D printing', 'printing', 'technology', 'performance']\n", "  '3D printing' -> ✓\n", "  'printing' -> ✓\n", "  'technology' -> ✓\n", "  'performance' -> ✓\n", "  'missing' -> ✗\n", "\n", "==================================================\n", "TESTING PARTIAL MATCHING LOGIC\n", "==================================================\n", "Is 'printing' partially in ['3d printing', 'inks', 'mxene']? True\n", "Is 'stems' partially in ['3D printing', 'inks', 'printing', 'stems', 'MXene']? True\n", "Is '3D printing' partially in ['printing material', 'electrodes']? True\n", "\n", "==================================================\n", "TESTING RECALCULATION LOGIC\n", "==================================================\n", "Validated terms: ['3D printing', 'inks', 'MXene']\n", "Predicted terms: ['3d printing', 'inks', 'stems', 'electronic']\n", "Result: {'matched_keyphrases': ['inks'], 'missed_keyphrases': ['3D printing', 'MXene'], 'noise_keyphrases': ['electronic', '3d printing', 'stems']}\n", "\n", "================================================================================\n", "================================================================================\n", "PROCESSING WITH TERM VALIDATION AND PARTIAL MATCHES FILTERING\n", "================================================================================\n", "Loading existing results from: test_results_improved_evaluation_scibert.xlsx\n", "✓ Loaded 1500 records with 11 columns\n", "✓ All required columns validated\n", "Available columns: ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'matched_count', 'missed_count', 'noise_count', 'true_total', 'predicted_total']\n", "Validating term_found column and filtering rows...\n", "  Removing row 198: No valid terms found in sentence\n", "    Original terms: ['Amine']\n", "    Sentence: This study examines the trends and current status of various energy storage technologies, highlighti...\n", "  Removing row 266: No valid terms found in sentence\n", "    Original terms: ['Amine']\n", "    Sentence: Notably, the CSAC increases surface area, while the melamine (nitrogen source) facilitates the forma...\n", "  Removing row 849: No valid terms found in sentence\n", "    Original terms: ['C5+']\n", "    Sentence: The NaFeZn catalyst exhibits a conversion of 28.1 % with a C5+ selectivity of 21.3 % at 320° C, 2 MP...\n", "  Removing row 1064: No valid terms found in sentence\n", "    Original terms: ['Cathodes', 'Cathode']\n", "    Sentence: However, the contribution of the inoculated surface gradient disorder structure on the Li-rich catho...\n", "  Original rows: 1500\n", "  Rows with updated term_found: 1242\n", "  Rows removed (empty term_found): 4\n", "  Final rows: 1496\n", "Processing partial matches...\n", "\n", "DEBUGGING DATAFRAME COLUMNS:\n", "==================================================\n", "DataFrame shape: (1496, 11)\n", "All columns: ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'matched_count', 'missed_count', 'noise_count', 'true_total', 'predicted_total']\n", "\n", "sentence:\n", "  Total rows: 1496\n", "  Non-null values: 1496\n", "  Non-empty values: 1496\n", "\n", "term_found:\n", "  Total rows: 1496\n", "  Non-null values: 1496\n", "  Non-empty values: 1496\n", "  Sample values types: [<class 'list'>, <class 'list'>, <class 'list'>]\n", "  Parsed sample: ['Proton', '1H NMR', 'FE-SEM', 'Fourier transform', 'Thermogravimetric Analysis', 'XRD', 'Energy', 'X-ray diffraction', 'emission', 'FT-IR', 'adsorbents']\n", "\n", "predicted_keyphrases:\n", "  Total rows: 1496\n", "  Non-null values: 1496\n", "  Non-empty values: 1496\n", "  Sample values types: [<class 'str'>, <class 'str'>, <class 'str'>]\n", "  Parsed sample: ['adsorbents', 'x - ray diffraction', 'xrd', 'fourier transform', 'ft - ir', 'proton', 'nmr', '1h nmr', 'thermogravimetric analysis', 'field', 'emission', 'fe - sem', 'energy']\n", "\n", "matched_keyphrases:\n", "  Total rows: 1496\n", "  Non-null values: 1493\n", "  Non-empty values: 1493\n", "  Sample values types: [<class 'str'>, <class 'str'>, <class 'str'>]\n", "  Parsed sample: ['1H NMR', 'Energy', 'Thermogravimetric Analysis', 'X-ray diffraction', 'adsorbents', 'Proton', 'FE-SEM', 'Fourier transform', 'FT-IR', 'XRD', 'emission', 'field emission']\n", "\n", "missed_keyphrases:\n", "  Total rows: 1496\n", "  Non-null values: 1346\n", "  Non-empty values: 1346\n", "  Sample values types: [<class 'str'>, <class 'str'>, <class 'str'>]\n", "  Parsed sample: ['printing', 'stems']\n", "\n", "noise_keyphrases:\n", "  Total rows: 1496\n", "  Non-null values: 584\n", "  Non-empty values: 584\n", "  Sample values types: [<class 'str'>, <class 'str'>, <class 'str'>]\n", "  Parsed sample: ['1h nmr']\n", "\n", "Row 0 debug:\n", "  Debug info: {'original_term_found_count': 11, 'validated_term_found_count': 11, 'predicted_count': 13, 'recalculated_matched_count': 11, 'recalculated_missed_count': 0, 'recalculated_noise_count': 2, 'final_missed_count': 0, 'final_noise_count': 1, 'partial_matches_count': 1, 'terms_removed': 0}\n", "  Partial matches found: 1\n", "Partial matches: ['nmr']\n", "\n", "Row 1 debug:\n", "  Debug info: {'original_term_found_count': 5, 'validated_term_found_count': 5, 'predicted_count': 5, 'recalculated_matched_count': 4, 'recalculated_missed_count': 1, 'recalculated_noise_count': 1, 'final_missed_count': 0, 'final_noise_count': 1, 'partial_matches_count': 1, 'terms_removed': 0}\n", "  Partial matches found: 1\n", "Partial matches: ['printing']\n", "\n", "Row 2 debug:\n", "  Debug info: {'original_term_found_count': 2, 'validated_term_found_count': 2, 'predicted_count': 1, 'recalculated_matched_count': 1, 'recalculated_missed_count': 1, 'recalculated_noise_count': 0, 'final_missed_count': 0, 'final_noise_count': 0, 'partial_matches_count': 1, 'terms_removed': 0}\n", "  Partial matches found: 1\n", "Partial matches: ['printing']\n", "\n", "Processing summary:\n", "Successful rows: 1496\n", "Failed rows: 0\n", "Total partial matches found: 1855\n", "Updating counts...\n", "Count update summary:\n", "Successful count updates: 1496\n", "Failed count updates: 0\n", "Preserving original data integrity...\n", "  Updated column: term_found\n", "  Updated column: matched_keyphrases\n", "  Updated column: missed_keyphrases\n", "  Updated column: noise_keyphrases\n", "  Updated column: partial_matches\n", "  Updated column: partial_match_count\n", "  Updated column: matched_count\n", "  Updated column: missed_count\n", "  Updated column: noise_count\n", "  Updated column: true_total\n", "  Updated column: predicted_total\n", "  Added new column: partial_match_count\n", "✓ All critical columns preserved\n", "Final dataframe shape: (1496, 13)\n", "Original dataframe shape: (1496, 11)\n", "Creating partial_matches_with_term_found column...\n", "  Total partial matches filtered with term_found: 1554\n", "  Rows with filtered partial matches: 819\n", "  Average filtered matches per row: 1.04\n", "\n", "Final verification:\n", "  Input rows: 1500\n", "  Filtered rows: 1496\n", "  Final output rows: 1496\n", "  Final output columns: 15\n", "\n", "============================================================\n", "METRICS SUMMARY\n", "============================================================\n", "Total sentences processed: 1496\n", "Total matched keyphrases: 5539\n", "Total missed keyphrases: 214\n", "Total noise keyphrases: 954\n", "Total partial matches found: 1855\n", "\n", "STRICT METRICS (exact matches only):\n", "  Recall: 0.7280 (72.80%)\n", "  Precision: 0.6635 (66.35%)\n", "  F1-Score: 0.6943 (69.43%)\n", "\n", "IMPROVED METRICS (including partial matches):\n", "  Recall: 0.9719 (97.19%)\n", "  Precision: 0.8857 (88.57%)\n", "  F1-Score: 0.9268 (92.68%)\n", "\n", "IMPROVEMENT FROM PARTIAL MATCHING:\n", "  Recall improvement: +0.2438 (24.38%)\n", "  Precision improvement: +0.2222 (22.22%)\n", "  F1 improvement: +0.2325 (23.25%)\n", "\n", "Saving results to test_results_improved_evaluation_scibert_partial_matches.xlsx...\n", "Converting list columns to strings for Excel export...\n", "  Warning: Could not convert column term_found: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()\n", "  Warning: Could not convert column matched_keyphrases: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()\n", "  Warning: Could not convert column missed_keyphrases: The truth value of an empty array is ambiguous. Use `array.size > 0` to check that an array is not empty.\n", "  Warning: Could not convert column noise_keyphrases: The truth value of an empty array is ambiguous. Use `array.size > 0` to check that an array is not empty.\n", "  Warning: Could not convert column partial_matches: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()\n", "  Warning: Could not convert column partial_matches_with_term_found: The truth value of an empty array is ambiguous. Use `array.size > 0` to check that an array is not empty.\n", "  Converted columns: ['predicted_keyphrases']\n", "  Skipped columns: ['term_found', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', 'partial_matches', 'partial_matches_with_term_found']\n", "  Original dataframe shape: (1496, 15)\n", "  Excel dataframe shape: (1496, 15)\n", "  New columns added: ('partial_matches_with_term_found', 'partial_matches_with_term_found_count')\n", "✓ Successfully saved 1496 rows and 15 columns\n", "✓ Metrics saved to: test_results_improved_evaluation_scibert_partial_matches_partial_metrics.json\n", "\n", "================================================================================\n", "✓ PROCESSING COMPLETED SUCCESSFULLY!\n", "✓ Updated results saved to: 'test_results_improved_evaluation_scibert_partial_matches.xlsx'\n", "✓ All original data preserved with 15 columns\n", "✓ Complete recalculation performed after term validation\n", "================================================================================\n", "\n", "Sample of preserved critical columns:\n", "                                                                                               sentence                                                                                                                           term_found                                                                                                                                 predicted_keyphrases                                                                                                                   matched_keyphrases  partial_match_count\n", "The synthesized adsorbents underwent characterization using X-ray diffraction (XRD), Fourier transfo... [Proton, 1H NMR, FE-SEM, Fourier transform, Thermogravimetric Analysis, XRD, Energy, X-ray diffraction, emission, FT-IR, adsorbents] adsorbents, x - ray diffraction, xrd, fourier transform, ft - ir, proton, nmr, 1h nmr, thermogravimetric analysis, field, emission, fe - sem, energy [adsorbents, ft-ir, x-ray diffraction, xrd, proton, thermogravimetric analysis, fe-sem, emission, fourier transform, energy, 1h nmr]                    1\n", "Moreover, it provides a new approach for preparing environmentally friendly MXene-based inks for the...                                                                       [environmentally friendly, 3D printing, inks, printing, MXene]                                                                                         environmentally friendly, mxene, inks, 3d printing, ##ystems                                                                                 [3d printing, inks, environmentally friendly, mxene]                    1\n", "The use of 3D printing technology in fabricating high-performance energy storage electrodes opens up...                                                                                                              [printing, 3D printing]                                                                                                                                          3d printing                                                                                                                        [3d printing]                    1\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from typing import List, Dict, Set\n", "import ast\n", "import re\n", "import json\n", "\n", "def normalize_for_validation(text: str) -> str:\n", "    \"\"\"Normalize text for validation - keep only alphabetic characters and spaces\"\"\"\n", "    if not text or pd.isna(text):\n", "        return \"\"\n", "    text = str(text).lower().strip()\n", "    # Replace spaces around hyphen with hyphen itself\n", "    # Example: 'X - ray' -> 'x-ray', 'example  -  test' -> 'example-test'\n", "    text = re.sub(r'\\s*-\\s*', '-', text)\n", "    # Keep only alphabetic characters and spaces, remove hyphens and other punctuation\n", "    # text = re.sub(r'[^a-z\\s]', ' ', text)\n", "    # Replace multiple spaces with single space\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    return text.strip()\n", "\n", "def is_term_in_sentence(term: str, sentence: str) -> bool:\n", "    \"\"\"\n", "    Check if a term is present in the sentence as a complete word/phrase\n", "    Ignores case, hyphens, and non-alphabetic characters\n", "    Ensures full word matching, not substring matching\n", "    \"\"\"\n", "    if not term or not sentence or pd.isna(term) or pd.isna(sentence):\n", "        return False\n", "    \n", "    # Normalize both term and sentence\n", "    normalized_term = normalize_for_validation(term)\n", "    normalized_sentence = normalize_for_validation(sentence)\n", "    \n", "    if not normalized_term or not normalized_sentence:\n", "        return False\n", "    \n", "    # Split term into words for multi-word terms\n", "    term_words = normalized_term.split()\n", "    if not term_words:\n", "        return False\n", "    \n", "    # For single word terms, use word boundary matching\n", "    if len(term_words) == 1:\n", "        pattern = r'\\b' + re.escape(term_words[0]) + r'\\b'\n", "        return bool(re.search(pattern, normalized_sentence))\n", "    \n", "    # For multi-word terms, check if all words appear in sequence\n", "    # Create a pattern that allows for flexible spacing\n", "    pattern_parts = []\n", "    for word in term_words:\n", "        pattern_parts.append(r'\\b' + re.escape(word) + r'\\b')\n", "    \n", "    # Allow multiple spaces between words\n", "    pattern = r'\\s+'.join(pattern_parts)\n", "    return bool(re.search(pattern, normalized_sentence))\n", "\n", "def validate_terms_in_sentence(sentence: str, term_found_list: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Validate which terms from term_found are actually present in the sentence\n", "    Returns only the terms that are found in the sentence\n", "    \"\"\"\n", "    if not sentence or not term_found_list:\n", "        return []\n", "    \n", "    validated_terms = []\n", "    for term in term_found_list:\n", "        if term and str(term).strip():  # Skip empty terms\n", "            if is_term_in_sentence(term, sentence):\n", "                validated_terms.append(term)\n", "    \n", "    return validated_terms\n", "\n", "def normalize_for_comparison(text: str) -> str:\n", "    \"\"\"Normalize text for exact matching comparison - now uses normalize_for_validation\"\"\"\n", "    return normalize_for_validation(text)\n", "\n", "def normalize_for_partial_match(text: str) -> str:\n", "    \"\"\"Normalize text for partial matching - now uses normalize_for_validation\"\"\"\n", "    return normalize_for_validation(text)\n", "\n", "\n", "# def normalize_for_partial_match(text: str) -> str:\n", "#     \"\"\"Normalize text for partial matching\"\"\"\n", "#     if not text or pd.isna(text):\n", "#         return \"\"\n", "#     text = str(text).lower().strip()\n", "#     # Remove extra spaces and normalize punctuation\n", "#     text = re.sub(r'[^\\w\\s-]', ' ', text)  # Replace punctuation with spaces except hyphens\n", "#     text = re.sub(r'\\s+', ' ', text)  # Replace multiple spaces with single space\n", "#     return text.strip()\n", "\n", "def get_words_from_phrase(phrase: str) -> Set[str]:\n", "    \"\"\"Extract individual words from a phrase\"\"\"\n", "    if not phrase or pd.isna(phrase):\n", "        return set()\n", "    \n", "    normalized = normalize_for_partial_match(phrase)\n", "    # Split on spaces and hyphens, filter out empty strings\n", "    words = set()\n", "    for word in re.split(r'[\\s\\-]+', normalized):\n", "        word = word.strip()\n", "        if word and len(word) > 1:  # Only include words with more than 1 character\n", "            words.add(word)\n", "    return words\n", "\n", "def is_partial_match(term: str, candidate_list: List[str]) -> bool:\n", "    \"\"\"\n", "    Check if any word from term appears in any phrase from candidate_list\n", "    \"\"\"\n", "    if not term or not candidate_list:\n", "        return False\n", "    \n", "    term_words = get_words_from_phrase(term)\n", "    if not term_words:\n", "        return False\n", "    \n", "    for candidate in candidate_list:\n", "        if not candidate:\n", "            continue\n", "            \n", "        candidate_words = get_words_from_phrase(candidate)\n", "        if not candidate_words:\n", "            continue\n", "            \n", "        # Check for word-level overlap\n", "        if term_words & candidate_words:  # Set intersection\n", "            return True\n", "    \n", "    return False\n", "\n", "\n", "def parse_keyphrase_column(value, debug_info=\"\") -> List[str]:\n", "    \"\"\"\n", "    Parse keyphrase column that might be a list, stringified list, or comma-separated string\n", "    Enhanced with safe handling of array-like inputs and debugging\n", "    \"\"\"\n", "    # Handle array-like inputs first (pandas Series, numpy arrays)\n", "    if isinstance(value, (list, tuple)):\n", "        # Already a list, normalize and filter\n", "        result = [str(item).strip() for item in value if str(item).strip() and str(item).strip().lower() not in ['nan', 'none', '']]\n", "        return result\n", "\n", "    if isinstance(value, (np.n<PERSON><PERSON>, pd.Series)):\n", "        # Convert arrays or series to list and process recursively\n", "        try:\n", "            value_list = value.tolist() if hasattr(value, 'tolist') else list(value)\n", "            return parse_keyphrase_column(value_list, debug_info)\n", "        except:\n", "            return []\n", "\n", "    # Handle scalar values\n", "    if pd.isna(value):\n", "        return []\n", "        \n", "    if value is None:\n", "        return []\n", "\n", "    # Convert to string and process\n", "    try:\n", "        value_str = str(value).strip()\n", "    except:\n", "        return []\n", "    \n", "    # Handle empty string cases\n", "    if not value_str or value_str.lower() in ['nan', 'none', '']:\n", "        return []\n", "\n", "    # Handle stringified lists like \"[item1, item2]\"\n", "    if value_str.startswith('[') and value_str.endswith(']'):\n", "        try:\n", "            parsed = ast.literal_eval(value_str)\n", "            if isinstance(parsed, list):\n", "                result = [str(item).strip() for item in parsed if str(item).strip() and str(item).strip().lower() not in ['nan', 'none', '']]\n", "                return result\n", "        except (ValueError, SyntaxError) as e:\n", "            # If literal_eval fails, try to parse manually\n", "            content = value_str[1:-1]  # Remove brackets\n", "            if content.strip():\n", "                items = [item.strip().strip('\"').strip(\"'\") for item in content.split(',')]\n", "                result = [item for item in items if item and item.lower() not in ['nan', 'none', '']]\n", "                return result\n", "\n", "    # Handle comma-separated values\n", "    if ',' in value_str:\n", "        items = []\n", "        for item in value_str.split(','):\n", "            item = item.strip().strip('\"').strip(\"'\")  # Remove quotes and whitespace\n", "            if item and item.lower() not in ['nan', 'none', '']:\n", "                items.append(item)\n", "        return items\n", "\n", "    # Single item\n", "    if value_str and value_str.lower() not in ['nan', 'none', '']:\n", "        return [value_str]\n", "\n", "    return []\n", "\n", "\n", "def recalculate_matches(validated_term_found: List[str], predicted_list: List[str]) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    Recalculate matched, missed, and noise keyphrases based on validated term_found and predicted lists\n", "    Now assumes inputs are already normalized\n", "    \"\"\"\n", "    if not validated_term_found:\n", "        validated_term_found = []\n", "    if not predicted_list:\n", "        predicted_list = []\n", "    \n", "    matched_keyphrases = []\n", "    missed_keyphrases = []\n", "    noise_keyphrases = []\n", "    \n", "    # Create sets for faster lookup (inputs should already be normalized)\n", "    validated_set = set(validated_term_found)\n", "    predicted_set = set(predicted_list)\n", "    \n", "    # Find matches: items that appear in both lists\n", "    matched_keyphrases = list(predicted_set & validated_set)\n", "    \n", "    # Find noise: items in predicted but not in validated\n", "    noise_keyphrases = list(predicted_set - validated_set)\n", "    \n", "    # Find missed: items in validated but not in predicted\n", "    missed_keyphrases = list(validated_set - predicted_set)\n", "    \n", "    return {\n", "        'matched_keyphrases': matched_keyphrases,\n", "        'missed_keyphrases': missed_keyphrases,\n", "        'noise_keyphrases': noise_keyphrases\n", "    }\n", "\n", "\n", "def debug_dataframe_columns(df: pd.DataFrame) -> None:\n", "    \"\"\"Debug function to check the state of key columns - with safe parsing\"\"\"\n", "    print(\"\\nDEBUGGING DATAFRAME COLUMNS:\")\n", "    print(\"=\"*50)\n", "    print(f\"DataFrame shape: {df.shape}\")\n", "    print(f\"All columns: {list(df.columns)}\")\n", "    \n", "    # Check key columns\n", "    key_columns = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases']\n", "    \n", "    for col in key_columns:\n", "        if col in df.columns:\n", "            non_null_count = df[col].notna().sum()\n", "            non_empty_count = df[col].apply(lambda x: bool(str(x).strip()) and str(x).strip().lower() not in ['nan', 'none']).sum()\n", "            print(f\"\\n{col}:\")\n", "            print(f\"  Total rows: {len(df)}\")\n", "            print(f\"  Non-null values: {non_null_count}\")\n", "            print(f\"  Non-empty values: {non_empty_count}\")\n", "            \n", "            # Show sample values for non-sentence columns\n", "            if col != 'sentence':\n", "                sample_values = df[col].dropna().head(3)\n", "                print(f\"  Sample values types: {[type(val) for val in sample_values]}\")\n", "                \n", "                # Test parsing on sample - with safe handling\n", "                if len(sample_values) > 0:\n", "                    test_val = sample_values.iloc[0]  # Use iloc to get scalar\n", "                    try:\n", "                        parsed = parse_keyphrase_column(test_val, f\"{col}_sample\")\n", "                        print(f\"  Parsed sample: {parsed}\")\n", "                    except Exception as e:\n", "                        print(f\"  Parsing error: {e}\")\n", "                        print(f\"  Test value type: {type(test_val)}\")\n", "                        print(f\"  Test value: {test_val}\")\n", "        else:\n", "            print(f\"\\n{col}: COLUMN MISSING!\")\n", "\n", "def find_partial_matches_in_row(row: pd.Series) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    Process a single row - now expects term_found to already be validated\n", "    \"\"\"\n", "    # Parse all columns (term_found should already be validated)\n", "    validated_term_found = parse_keyphrase_column(row.get('term_found', ''), 'term_found')\n", "    predicted_list = parse_keyphrase_column(row.get('predicted_keyphrases', ''), 'predicted_keyphrases')\n", "    \n", "    # Apply normalization to prevent hyphen-related duplicates\n", "    normalized_term_found = [normalize_for_validation(term) for term in validated_term_found]\n", "    normalized_predicted = [normalize_for_validation(term) for term in predicted_list]\n", "    \n", "    # Remove duplicates after normalization\n", "    def remove_normalized_duplicates(items_list):\n", "        unique_items = []\n", "        seen_lower = set()\n", "        for item in items_list:\n", "            if item and item not in seen_lower:\n", "                unique_items.append(item)\n", "                seen_lower.add(item)\n", "        return unique_items\n", "    \n", "    normalized_term_found = remove_normalized_duplicates(normalized_term_found)\n", "    normalized_predicted = remove_normalized_duplicates(normalized_predicted)\n", "    \n", "    # RECALCULATE ALL MATCHES based on normalized terms\n", "    recalculated_matches = recalculate_matches(normalized_term_found, normalized_predicted)\n", "    \n", "    matched_list = recalculated_matches['matched_keyphrases']\n", "    missed_list = recalculated_matches['missed_keyphrases']\n", "    noise_list = recalculated_matches['noise_keyphrases']\n", "    \n", "    # Process partial matches according to your logic\n", "    partial_matches = []\n", "    new_missed = []\n", "    new_noise = []\n", "    \n", "    # Logic 1: Check if missed keyphrases are partially found in predicted keyphrases\n", "    for missed_kp in missed_list:\n", "        found_partial = False\n", "        for predicted_kp in normalized_predicted:\n", "            if (missed_kp in predicted_kp or predicted_kp in missed_kp):\n", "                partial_matches.append(missed_kp)\n", "                found_partial = True\n", "                break\n", "        if not found_partial:\n", "            new_missed.append(missed_kp)\n", "    \n", "    # Logic 2: Check if noise keyphrases are found in validated term_found\n", "    for noise_kp in noise_list:\n", "        found_partial = False\n", "        for term in normalized_term_found:\n", "            if (noise_kp in term or term in noise_kp):\n", "                partial_matches.append(noise_kp)\n", "                found_partial = True\n", "                break\n", "        if not found_partial:\n", "            new_noise.append(noise_kp)\n", "    \n", "    # Remove duplicates from all final lists\n", "    unique_partial_matches = remove_normalized_duplicates(partial_matches)\n", "    unique_matched = remove_normalized_duplicates(matched_list)\n", "    unique_missed = remove_normalized_duplicates(new_missed)\n", "    unique_noise = remove_normalized_duplicates(new_noise)\n", "    \n", "    return {\n", "        'validated_term_found': normalized_term_found,\n", "        'matched_keyphrases': unique_matched,\n", "        'missed_keyphrases': unique_missed,\n", "        'noise_keyphrases': unique_noise,\n", "        'partial_matches': unique_partial_matches,\n", "        'debug_info': {\n", "            'original_term_found_count': len(validated_term_found),\n", "            'validated_term_found_count': len(normalized_term_found),\n", "            'predicted_count': len(normalized_predicted),\n", "            'recalculated_matched_count': len(matched_list),\n", "            'recalculated_missed_count': len(missed_list),\n", "            'recalculated_noise_count': len(noise_list),\n", "            'final_missed_count': len(unique_missed),\n", "            'final_noise_count': len(unique_noise),\n", "            'partial_matches_count': len(unique_partial_matches),\n", "            'terms_removed': 0  # Already handled in filtering step\n", "        }\n", "    }\n", "\n", "\n", "\n", "def validate_and_filter_rows(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Update 'term_found' column by keeping only terms that are actually found in sentences.\n", "    Remove rows where the updated 'term_found' list becomes empty.\n", "    \"\"\"\n", "    print(\"Validating term_found column and filtering rows...\")\n", "    \n", "    rows_to_keep = []\n", "    removed_count = 0\n", "    updated_count = 0\n", "    \n", "    for idx, row in df.iterrows():\n", "        # Parse original term_found\n", "        original_terms = parse_keyphrase_column(row.get('term_found', ''), 'term_found')\n", "        sentence = row.get('sentence', '')\n", "        \n", "        # Validate which terms are actually in the sentence\n", "        validated_terms = validate_terms_in_sentence(sentence, original_terms)\n", "        \n", "        if validated_terms:\n", "            # Update the row with validated terms\n", "            row_copy = row.copy()\n", "            row_copy['term_found'] = validated_terms\n", "            rows_to_keep.append(row_copy)\n", "            \n", "            # Track if terms were removed\n", "            if len(validated_terms) < len(original_terms):\n", "                updated_count += 1\n", "        else:\n", "            # No valid terms found, remove this row\n", "            removed_count += 1\n", "            print(f\"  Removing row {idx}: No valid terms found in sentence\")\n", "            print(f\"    Original terms: {original_terms}\")\n", "            print(f\"    Sentence: {sentence[:100]}...\")\n", "    \n", "    # Create new DataFrame with filtered rows\n", "    if rows_to_keep:\n", "        filtered_df = pd.DataFrame(rows_to_keep).reset_index(drop=True)\n", "    else:\n", "        filtered_df = pd.DataFrame()\n", "    \n", "    print(f\"  Original rows: {len(df)}\")\n", "    print(f\"  Rows with updated term_found: {updated_count}\")\n", "    print(f\"  Rows removed (empty term_found): {removed_count}\")\n", "    print(f\"  Final rows: {len(filtered_df)}\")\n", "    \n", "    return filtered_df\n", "\n", "\n", "def calculate_counts_for_row(row: pd.Series) -> Dict[str, int]:\n", "    \"\"\"Calculate counts for a single row with corrected true_total and predicted_total logic\"\"\"\n", "    matched_list = row.get('matched_keyphrases', [])\n", "    missed_list = row.get('missed_keyphrases', [])\n", "    noise_list = row.get('noise_keyphrases', [])\n", "    partial_list = row.get('partial_matches', [])\n", "    term_found_list = row.get('term_found', [])\n", "    predicted_list = row.get('predicted_keyphrases', [])\n", "    \n", "    # Ensure they are lists using your existing parse function\n", "    if not isinstance(matched_list, list):\n", "        matched_list = parse_keyphrase_column(matched_list)\n", "    if not isinstance(missed_list, list):\n", "        missed_list = parse_keyphrase_column(missed_list)\n", "    if not isinstance(noise_list, list):\n", "        noise_list = parse_keyphrase_column(noise_list)\n", "    if not isinstance(partial_list, list):\n", "        partial_list = parse_keyphrase_column(partial_list)\n", "    if not isinstance(term_found_list, list):\n", "        term_found_list = parse_keyphrase_column(term_found_list)\n", "    if not isinstance(predicted_list, list):\n", "        predicted_list = parse_keyphrase_column(predicted_list)\n", "    \n", "    matched_count = len(matched_list)\n", "    missed_count = len(missed_list)\n", "    noise_count = len(noise_list)\n", "    partial_match_count = len(partial_list)\n", "    \n", "    # **CORRECTED**: Calculate true_total from validated term_found\n", "    sentence = row.get('sentence', '')\n", "    validated_term_found = validate_terms_in_sentence(sentence, term_found_list)\n", "    true_total = len([item for item in validated_term_found if item and str(item).strip()])\n", "    \n", "    # **CORRECTED**: Calculate predicted_total by counting actual items in predicted_keyphrases\n", "    predicted_total = len([item for item in predicted_list if item and str(item).strip()])\n", "    \n", "    return {\n", "        'matched_count': matched_count,\n", "        'missed_count': missed_count,\n", "        'noise_count': noise_count,\n", "        'partial_match_count': partial_match_count,\n", "        'true_total': true_total,\n", "        'predicted_total': predicted_total\n", "    }\n", "\n", "\n", "def preserve_original_data(df_original: pd.DataFrame, df_processed: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Ensure all original columns are preserved in the final output\n", "    \"\"\"\n", "    print(\"Preserving original data integrity...\")\n", "    \n", "    # Start with original dataframe to preserve all columns and data types\n", "    df_final = df_original.copy()\n", "    \n", "    # List of columns that should be updated with processed data\n", "    columns_to_update = [\n", "        'term_found',  # Update with validated terms\n", "        'matched_keyphrases', 'missed_keyphrases', 'noise_keyphrases', \n", "        'partial_matches', 'partial_match_count',\n", "        'matched_count', 'missed_count', 'noise_count', \n", "        'true_total', 'predicted_total'\n", "    ]\n", "    \n", "    # Update only the processed columns, keeping everything else intact\n", "    for col in columns_to_update:\n", "        if col in df_processed.columns:\n", "            df_final[col] = df_processed[col]\n", "            print(f\"  Updated column: {col}\")\n", "    \n", "    # Add new partial_match_count column if it doesn't exist in original\n", "    if 'partial_match_count' not in df_original.columns and 'partial_match_count' in df_processed.columns:\n", "        df_final['partial_match_count'] = df_processed['partial_match_count']\n", "        print(\"  Added new column: partial_match_count\")\n", "    \n", "    # Verify critical columns are preserved\n", "    critical_columns = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases']\n", "    missing_critical = [col for col in critical_columns if col not in df_final.columns]\n", "    \n", "    if missing_critical:\n", "        print(f\"WARNING: Missing critical columns in final output: {missing_critical}\")\n", "    else:\n", "        print(\"✓ All critical columns preserved\")\n", "    \n", "    print(f\"Final dataframe shape: {df_final.shape}\")\n", "    print(f\"Original dataframe shape: {df_original.shape}\")\n", "    \n", "    return df_final\n", "\n", "def process_partial_matches_0(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Process DataFrame to identify and move partial matches while preserving all original data\n", "    Now includes complete recalculation of matching logic after term validation\n", "    \"\"\"\n", "    print(\"Processing with complete recalculation after term validation...\")\n", "    \n", "    # Store original dataframe for data preservation\n", "    df_original = df.copy()\n", "    \n", "    # Create working copy for processing\n", "    df_working = df.copy()\n", "    \n", "    # Debug the dataframe first\n", "    debug_dataframe_columns(df_working)\n", "    \n", "    # Initialize partial_matches column if it doesn't exist\n", "    if 'partial_matches' not in df_working.columns:\n", "        df_working['partial_matches'] = [[] for _ in range(len(df_working))]\n", "    \n", "    # Initialize partial_match_count column if it doesn't exist  \n", "    if 'partial_match_count' not in df_working.columns:\n", "        df_working['partial_match_count'] = 0\n", "    \n", "    total_partial_matches = 0\n", "    successful_rows = 0\n", "    failed_rows = 0\n", "    total_terms_removed = 0\n", "    total_recalculated = 0\n", "    \n", "    # Process each row\n", "    for idx, row in df_working.iterrows():\n", "        try:\n", "            partial_results = find_partial_matches_in_row(row)\n", "            \n", "            # Update the row with ALL recalculated results\n", "            df_working.at[idx, 'term_found'] = partial_results['validated_term_found']\n", "            df_working.at[idx, 'matched_keyphrases'] = partial_results['matched_keyphrases']\n", "            df_working.at[idx, 'missed_keyphrases'] = partial_results['missed_keyphrases'] \n", "            df_working.at[idx, 'noise_keyphrases'] = partial_results['noise_keyphrases']\n", "            df_working.at[idx, 'partial_matches'] = partial_results['partial_matches']\n", "            \n", "            # Count partial matches found in this row\n", "            partial_count = len(partial_results['partial_matches'])\n", "            total_partial_matches += partial_count\n", "            total_terms_removed += partial_results['debug_info']['terms_removed']\n", "            \n", "            # Track recalculation changes\n", "            if (partial_results['debug_info']['recalculated_matched_count'] > 0 or\n", "                partial_results['debug_info']['recalculated_missed_count'] > 0 or\n", "                partial_results['debug_info']['recalculated_noise_count'] > 0):\n", "                total_recalculated += 1\n", "            \n", "            successful_rows += 1\n", "            \n", "            # Debug first few rows\n", "            if idx < 3:\n", "                print(f\"\\nRow {idx} debug:\")\n", "                print(f\"  Debug info: {partial_results['debug_info']}\")\n", "                print(f\"  Partial matches found: {partial_count}\")\n", "                print(f\"  Terms removed from validation: {partial_results['debug_info']['terms_removed']}\")\n", "                print(f\"  Recalculated matches: {partial_results['debug_info']['recalculated_matched_count']}\")\n", "                print(f\"  Final missed: {partial_results['debug_info']['final_missed_count']}\")\n", "                print(f\"  Final noise: {partial_results['debug_info']['final_noise_count']}\")\n", "                if partial_count > 0:\n", "                    print(f\"  Partial matches: {partial_results['partial_matches']}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing row {idx}: {e}\")\n", "            failed_rows += 1\n", "            continue\n", "    \n", "    print(f\"\\nProcessing summary:\")\n", "    print(f\"Successful rows: {successful_rows}\")\n", "    print(f\"Failed rows: {failed_rows}\")\n", "    print(f\"Total partial matches found: {total_partial_matches}\")\n", "    print(f\"Total terms removed during validation: {total_terms_removed}\")\n", "    print(f\"Rows with recalculated matches: {total_recalculated}\")\n", "    \n", "    # Calculate updated counts for all rows\n", "    print(\"Updating counts...\")\n", "    count_update_success = 0\n", "    count_update_failed = 0\n", "    \n", "    for idx, row in df_working.iterrows():\n", "        try:\n", "            counts = calculate_counts_for_row(row)\n", "            df_working.at[idx, 'matched_count'] = counts['matched_count']\n", "            df_working.at[idx, 'missed_count'] = counts['missed_count'] \n", "            df_working.at[idx, 'noise_count'] = counts['noise_count']\n", "            df_working.at[idx, 'partial_match_count'] = counts['partial_match_count']\n", "            df_working.at[idx, 'true_total'] = counts['true_total']\n", "            df_working.at[idx, 'predicted_total'] = counts['predicted_total']\n", "            count_update_success += 1\n", "        except Exception as e:\n", "            print(f\"  Error updating counts for row {idx}: {e}\")\n", "            count_update_failed += 1\n", "            continue\n", "    \n", "    print(f\"Count update summary:\")\n", "    print(f\"Successful count updates: {count_update_success}\")\n", "    print(f\"Failed count updates: {count_update_failed}\")\n", "    \n", "    # Preserve all original data while incorporating processed results\n", "    df_final = preserve_original_data(df_original, df_working)\n", "    \n", "    return df_final\n", "\n", "def find_partial_matches_in_row(row: pd.Series) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    Process a single row to find partial matches with normalization applied to all columns\n", "    \"\"\"\n", "    # Parse all columns with normalization applied\n", "    original_term_found = parse_keyphrase_column(row.get('term_found', ''), 'term_found')\n", "    predicted_list = parse_keyphrase_column(row.get('predicted_keyphrases', ''), 'predicted_keyphrases')\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> TERMS IN SENTENCE - This is the key first step\n", "    sentence = row.get('sentence', '')\n", "    validated_term_found = validate_terms_in_sentence(sentence, original_term_found)\n", "    \n", "    # Apply normalization to validated terms\n", "    normalized_validated_terms = [normalize_for_validation(term) for term in validated_term_found]\n", "    normalized_predicted = [normalize_for_validation(term) for term in predicted_list]\n", "    \n", "    # Remove duplicates after normalization\n", "    def remove_normalized_duplicates(items_list):\n", "        unique_items = []\n", "        seen_lower = set()\n", "        for item in items_list:\n", "            normalized_item = normalize_for_validation(item)\n", "            if normalized_item and normalized_item not in seen_lower:\n", "                unique_items.append(normalized_item)\n", "                seen_lower.add(normalized_item)\n", "        return unique_items\n", "    \n", "    normalized_validated_terms = remove_normalized_duplicates(validated_term_found)\n", "    normalized_predicted = remove_normalized_duplicates(predicted_list)\n", "    \n", "    # RECALCULATE ALL MATCHES based on normalized terms\n", "    recalculated_matches = recalculate_matches(normalized_validated_terms, normalized_predicted)\n", "    \n", "    matched_list = recalculated_matches['matched_keyphrases']\n", "    missed_list = recalculated_matches['missed_keyphrases']\n", "    noise_list = recalculated_matches['noise_keyphrases']\n", "    \n", "    # Now process partial matches according to your specific logic\n", "    partial_matches = []\n", "    new_missed = []\n", "    new_noise = []\n", "    \n", "    # Logic 1: Check if missed keyphrases are partially found in predicted keyphrases\n", "    for missed_kp in missed_list:\n", "        found_partial = False\n", "        for predicted_kp in normalized_predicted:\n", "            # Both are already normalized, so direct comparison\n", "            if (missed_kp in predicted_kp or predicted_kp in missed_kp):\n", "                partial_matches.append(missed_kp)\n", "                found_partial = True\n", "                break\n", "        \n", "        if not found_partial:\n", "            new_missed.append(missed_kp)\n", "    \n", "    # Logic 2: Check if noise keyphrases are found in validated term_found\n", "    for noise_kp in noise_list:\n", "        found_partial = False\n", "        for term in normalized_validated_terms:\n", "            # Both are already normalized, so direct comparison\n", "            if (noise_kp in term or term in noise_kp):\n", "                partial_matches.append(noise_kp)\n", "                found_partial = True\n", "                break\n", "        \n", "        if not found_partial:\n", "            new_noise.append(noise_kp)\n", "    \n", "    # Logic 3 & 4: Remove duplicates from all final lists\n", "    unique_partial_matches = remove_normalized_duplicates(partial_matches)\n", "    unique_matched = remove_normalized_duplicates(matched_list)\n", "    unique_missed = remove_normalized_duplicates(new_missed)\n", "    unique_noise = remove_normalized_duplicates(new_noise)\n", "    \n", "    return {\n", "        'validated_term_found': normalized_validated_terms,\n", "        'matched_keyphrases': unique_matched,\n", "        'missed_keyphrases': unique_missed,\n", "        'noise_keyphrases': unique_noise,\n", "        'partial_matches': unique_partial_matches,\n", "        'debug_info': {\n", "            'original_term_found_count': len(original_term_found),\n", "            'validated_term_found_count': len(normalized_validated_terms),\n", "            'predicted_count': len(normalized_predicted),\n", "            'recalculated_matched_count': len(matched_list),\n", "            'recalculated_missed_count': len(missed_list),\n", "            'recalculated_noise_count': len(noise_list),\n", "            'final_missed_count': len(unique_missed),\n", "            'final_noise_count': len(unique_noise),\n", "            'partial_matches_count': len(unique_partial_matches),\n", "            'terms_removed': len(original_term_found) - len(normalized_validated_terms)\n", "        }\n", "    }\n", "\n", "\n", "def process_partial_matches(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Process DataFrame to identify and move partial matches while preserving all original data\n", "    \"\"\"\n", "    print(\"Processing partial matches...\")\n", "    \n", "    # Store original dataframe for data preservation\n", "    df_original = df.copy()\n", "    \n", "    # Create working copy for processing\n", "    df_working = df.copy()\n", "    \n", "    # Debug the dataframe first\n", "    debug_dataframe_columns(df_working)\n", "    \n", "    # Initialize partial_matches column if it doesn't exist\n", "    if 'partial_matches' not in df_working.columns:\n", "        df_working['partial_matches'] = [[] for _ in range(len(df_working))]\n", "    \n", "    # Initialize partial_match_count column if it doesn't exist  \n", "    if 'partial_match_count' not in df_working.columns:\n", "        df_working['partial_match_count'] = 0\n", "    \n", "    total_partial_matches = 0\n", "    successful_rows = 0\n", "    failed_rows = 0\n", "    \n", "    # Process each row\n", "    for idx, row in df_working.iterrows():\n", "        try:\n", "            partial_results = find_partial_matches_in_row(row)\n", "            \n", "            # Update the row with processed results\n", "            df_working.at[idx, 'matched_keyphrases'] = partial_results['matched_keyphrases']\n", "            df_working.at[idx, 'missed_keyphrases'] = partial_results['missed_keyphrases'] \n", "            df_working.at[idx, 'noise_keyphrases'] = partial_results['noise_keyphrases']\n", "            df_working.at[idx, 'partial_matches'] = partial_results['partial_matches']\n", "            \n", "            # Count partial matches found in this row\n", "            partial_count = len(partial_results['partial_matches'])\n", "            total_partial_matches += partial_count\n", "            successful_rows += 1\n", "            \n", "            # Debug first few rows\n", "            if idx < 3:\n", "                print(f\"\\nRow {idx} debug:\")\n", "                print(f\"  Debug info: {partial_results['debug_info']}\")\n", "                print(f\"  Partial matches found: {partial_count}\")\n", "                if partial_count > 0:\n", "                    print(f\"Partial matches: {partial_results['partial_matches']}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing row {idx}: {e}\")\n", "            failed_rows += 1\n", "            continue\n", "    \n", "    print(f\"\\nProcessing summary:\")\n", "    print(f\"Successful rows: {successful_rows}\")\n", "    print(f\"Failed rows: {failed_rows}\")\n", "    print(f\"Total partial matches found: {total_partial_matches}\")\n", "    \n", "    # Calculate updated counts for all rows\n", "    print(\"Updating counts...\")\n", "    count_update_success = 0\n", "    count_update_failed = 0\n", "    \n", "    for idx, row in df_working.iterrows():\n", "        try:\n", "            counts = calculate_counts_for_row(row)\n", "            df_working.at[idx, 'matched_count'] = counts['matched_count']\n", "            df_working.at[idx, 'missed_count'] = counts['missed_count'] \n", "            df_working.at[idx, 'noise_count'] = counts['noise_count']\n", "            df_working.at[idx, 'partial_match_count'] = counts['partial_match_count']\n", "            df_working.at[idx, 'true_total'] = counts['true_total']\n", "            df_working.at[idx, 'predicted_total'] = counts['predicted_total']\n", "            count_update_success += 1\n", "        except Exception as e:\n", "            print(f\"  Error updating counts for row {idx}: {e}\")\n", "            count_update_failed += 1\n", "            continue\n", "    \n", "    print(f\"Count update summary:\")\n", "    print(f\"Successful count updates: {count_update_success}\")\n", "    print(f\"Failed count updates: {count_update_failed}\")\n", "    \n", "    # Preserve all original data while incorporating processed results\n", "    df_final = preserve_original_data(df_original, df_working)\n", "    \n", "    return df_final\n", "\n", "def normalize_for_matching(text: str) -> str:\n", "    \"\"\"Normalize text for matching by removing hyphens and ignoring case\"\"\"\n", "    if not text or pd.isna(text):\n", "        return \"\"\n", "    text = str(text).lower().strip()\n", "    # Remove hyphens\n", "    text = re.sub(r'-', '', text)\n", "    # Remove extra spaces\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    return text.strip()\n", "\n", "def filter_partial_matches_with_term_found(row: pd.Series) -> List[str]:\n", "    \"\"\"\n", "    Filter partial_matches to include only items that match with term_found items,\n", "    ignoring case and hyphens\n", "    \"\"\"\n", "    # Parse columns to ensure they are lists\n", "    partial_matches = parse_keyphrase_column(row.get('partial_matches', []))\n", "    term_found = parse_keyphrase_column(row.get('term_found', []))\n", "    \n", "    if not partial_matches or not term_found:\n", "        return []\n", "    \n", "    # Normalize term_found items for comparison\n", "    normalized_term_found = set()\n", "    for term in term_found:\n", "        if term and str(term).strip():\n", "            normalized_term_found.add(normalize_for_matching(term))\n", "    \n", "    # Filter partial_matches that match any term from term_found\n", "    filtered_matches = []\n", "    for item in partial_matches:\n", "        if item and str(item).strip():\n", "            normalized_item = normalize_for_matching(item)\n", "            if normalized_item in normalized_term_found:\n", "                filtered_matches.append(item)  # Keep original form\n", "    \n", "    return filtered_matches\n", "\n", "def add_partial_matches_with_term_found_column(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Add 'partial_matches_with_term_found' and 'partial_matches_with_term_found_count' columns\n", "    \"\"\"\n", "    print(\"Creating partial_matches_with_term_found column...\")\n", "    \n", "    df_updated = df.copy()\n", "    \n", "    # Apply filtering function row-wise\n", "    df_updated['partial_matches_with_term_found'] = df_updated.apply(\n", "        filter_partial_matches_with_term_found, axis=1\n", "    )\n", "    \n", "    # Add count column for non-empty items\n", "    df_updated['partial_matches_with_term_found_count'] = df_updated['partial_matches_with_term_found'].apply(\n", "        lambda x: len([item for item in x if item and str(item).strip()]) if isinstance(x, list) else 0\n", "    )\n", "    \n", "    # Log statistics\n", "    total_filtered_matches = df_updated['partial_matches_with_term_found_count'].sum()\n", "    rows_with_matches = (df_updated['partial_matches_with_term_found_count'] > 0).sum()\n", "    \n", "    print(f\"  Total partial matches filtered with term_found: {total_filtered_matches}\")\n", "    print(f\"  Rows with filtered partial matches: {rows_with_matches}\")\n", "    print(f\"  Average filtered matches per row: {total_filtered_matches/len(df_updated):.2f}\")\n", "    \n", "    return df_updated\n", "\n", "\n", "\n", "\n", "def calculate_metrics(df: pd.DataFrame) -> Dict:\n", "    \"\"\"Calculate metrics including partial matches\"\"\"\n", "    # Ensure count columns exist and are numeric\n", "    count_columns = ['matched_count', 'missed_count', 'noise_count', 'partial_match_count']\n", "    for col in count_columns:\n", "        if col in df.columns:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)\n", "        else:\n", "            df[col] = 0\n", "    \n", "    total_matched = df['matched_count'].sum()\n", "    total_missed = df['missed_count'].sum()\n", "    total_noise = df['noise_count'].sum()\n", "    total_partial = df['partial_match_count'].sum()\n", "    \n", "    total_true = total_matched + total_missed + total_partial\n", "    total_predicted = total_matched + total_noise + total_partial\n", "    \n", "    # Calculate improved metrics (including partial matches)\n", "    improved_recall = (total_matched + total_partial) / total_true if total_true > 0 else 0\n", "    improved_precision = (total_matched + total_partial) / total_predicted if total_predicted > 0 else 0\n", "    improved_f1 = 2 * improved_precision * improved_recall / (improved_precision + improved_recall) if (improved_precision + improved_recall) > 0 else 0\n", "    \n", "    # Traditional metrics (exact matches only)\n", "    strict_recall = total_matched / total_true if total_true > 0 else 0\n", "    strict_precision = total_matched / total_predicted if total_predicted > 0 else 0\n", "    strict_f1 = 2 * strict_precision * strict_recall / (strict_precision + strict_recall) if (strict_precision + strict_recall) > 0 else 0\n", "    \n", "    return {\n", "        'total_sentences': len(df),\n", "        'total_matched': int(total_matched),\n", "        'total_missed': int(total_missed),\n", "        'total_noise': int(total_noise), \n", "        'total_partial_matches': int(total_partial),\n", "        'total_true_keyphrases': int(total_true),\n", "        'total_predicted_keyphrases': int(total_predicted),\n", "        'strict_recall': float(strict_recall),\n", "        'strict_precision': float(strict_precision),\n", "        'strict_f1': float(strict_f1),\n", "        'improved_recall': float(improved_recall),\n", "        'improved_precision': float(improved_precision),\n", "        'improved_f1': float(improved_f1)\n", "    }\n", "\n", "\n", "def convert_lists_to_strings_safe(df: pd.DataFrame, list_columns: List[str]) -> pd.DataFrame:\n", "    \"\"\"\n", "    Convert list columns to comma-separated strings for Excel export\n", "    Handles numpy arrays, empty arrays, and lists gracefully\n", "    \"\"\"\n", "    print(\"Converting list columns to strings for Excel export...\")\n", "    df_for_excel = df.copy()\n", "    \n", "    converted_cols = []\n", "    skipped_cols = []\n", "    \n", "    for col in list_columns:\n", "        if col in df_for_excel.columns:\n", "            try:\n", "                def safe_convert_to_string(x):\n", "                    \"\"\"Safely convert various data types to comma-separated string\"\"\"\n", "                    # Handle None and NaN values\n", "                    if pd.isna(x) or x is None:\n", "                        return ''\n", "                    \n", "                    # Handle numpy arrays\n", "                    if hasattr(x, 'size'):\n", "                        if x.size == 0:  # Empty array\n", "                            return ''\n", "                        <PERSON><PERSON>(x, 'tolist'):\n", "                            x = x.tolist()  # Convert to list\n", "                        else:\n", "                            return str(x)\n", "                    \n", "                    # Handle lists and tuples\n", "                    if isinstance(x, (list, tuple)):\n", "                        if len(x) == 0:  # Empty list\n", "                            return ''\n", "                        # Convert all items to string and join\n", "                        return ', '.join([str(item) for item in x if str(item).strip()])\n", "                    \n", "                    # Handle strings that might represent lists\n", "                    if isinstance(x, str):\n", "                        x_stripped = x.strip()\n", "                        if not x_stripped or x_stripped.lower() in ['nan', 'none', '[]']:\n", "                            return ''\n", "                        return x_stripped\n", "                    \n", "                    # Handle other types\n", "                    return str(x) if str(x).strip() else ''\n", "                \n", "                df_for_excel[col] = df_for_excel[col].apply(safe_convert_to_string)\n", "                converted_cols.append(col)\n", "                \n", "            except Exception as e:\n", "                print(f\"  Warning: Could not convert column {col}: {e}\")\n", "                skipped_cols.append(col)\n", "        else:\n", "            skipped_cols.append(f\"{col} (missing)\")\n", "    \n", "    print(f\"  Converted columns: {converted_cols}\")\n", "    if skipped_cols:\n", "        print(f\"  Skipped columns: {skipped_cols}\")\n", "    \n", "    return df_for_excel\n", "\n", "\n", "\n", "def print_metrics_summary(metrics: Dict) -> None:\n", "    \"\"\"Print formatted metrics summary\"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"METRICS SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    print(f\"Total sentences processed: {metrics['total_sentences']}\")\n", "    print(f\"Total matched keyphrases: {metrics['total_matched']}\")\n", "    print(f\"Total missed keyphrases: {metrics['total_missed']}\")\n", "    print(f\"Total noise keyphrases: {metrics['total_noise']}\")\n", "    print(f\"Total partial matches found: {metrics['total_partial_matches']}\")\n", "    \n", "    print(f\"\\nSTRICT METRICS (exact matches only):\")\n", "    print(f\"  Recall: {metrics['strict_recall']:.4f} ({metrics['strict_recall']*100:.2f}%)\")\n", "    print(f\"  Precision: {metrics['strict_precision']:.4f} ({metrics['strict_precision']*100:.2f}%)\")\n", "    print(f\"  F1-Score: {metrics['strict_f1']:.4f} ({metrics['strict_f1']*100:.2f}%)\")\n", "    \n", "    print(f\"\\nIMPROVED METRICS (including partial matches):\")\n", "    print(f\"  Recall: {metrics['improved_recall']:.4f} ({metrics['improved_recall']*100:.2f}%)\")\n", "    print(f\"  Precision: {metrics['improved_precision']:.4f} ({metrics['improved_precision']*100:.2f}%)\")\n", "    print(f\"  F1-Score: {metrics['improved_f1']:.4f} ({metrics['improved_f1']*100:.2f}%)\")\n", "    \n", "    improvement_recall = metrics['improved_recall'] - metrics['strict_recall']\n", "    improvement_precision = metrics['improved_precision'] - metrics['strict_precision']\n", "    improvement_f1 = metrics['improved_f1'] - metrics['strict_f1']\n", "    \n", "    print(f\"\\nIMPROVEMENT FROM PARTIAL MATCHING:\")\n", "    print(f\"  Recall improvement: +{improvement_recall:.4f} ({improvement_recall*100:.2f}%)\")\n", "    print(f\"  Precision improvement: +{improvement_precision:.4f} ({improvement_precision*100:.2f}%)\")\n", "    print(f\"  F1 improvement: +{improvement_f1:.4f} ({improvement_f1*100:.2f}%)\")\n", "\n", "def validate_required_columns(df: pd.DataFrame) -> None:\n", "    \"\"\"Validate that required columns exist in DataFrame\"\"\"\n", "    critical_columns = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases']\n", "    required_columns = ['missed_keyphrases', 'noise_keyphrases']\n", "    \n", "    missing_critical = [col for col in critical_columns if col not in df.columns]\n", "    missing_required = [col for col in required_columns if col not in df.columns]\n", "    \n", "    if missing_critical:\n", "        raise ValueError(f\"CRITICAL columns missing: {missing_critical}. Cannot proceed.\")\n", "    \n", "    if missing_required:\n", "        print(f\"Warning: Missing optional columns: {missing_required}\")\n", "        print(\"These columns will be created with empty values.\")\n", "        \n", "        # Create missing columns with empty values\n", "        for col in missing_required:\n", "            df[col] = ''\n", "    \n", "    print(f\"✓ All required columns validated\")\n", "    print(f\"Available columns: {list(df.columns)}\")\n", "\n", "def save_results_updated(df: pd.DataFrame, output_file: str, metrics: Dict) -> None:\n", "    \"\"\"Save results and metrics to files with new columns included\"\"\"\n", "    print(f\"\\nSaving results to {output_file}...\")\n", "    \n", "    # Define which columns need list-to-string conversion (include new column)\n", "    list_columns = ['term_found', 'predicted_keyphrases', 'matched_keyphrases', \n", "                   'missed_keyphrases', 'noise_keyphrases', 'partial_matches',\n", "                   'partial_matches_with_term_found']  # NEW COLUMN ADDED\n", "    \n", "    # Convert list columns to strings for Excel compatibility\n", "    df_for_excel = convert_lists_to_strings_safe(df, list_columns)\n", "    \n", "    # Verify data integrity before saving\n", "    print(f\"  Original dataframe shape: {df.shape}\")\n", "    print(f\"  Excel dataframe shape: {df_for_excel.shape}\")\n", "    print(f\"  New columns added: {'partial_matches_with_term_found', 'partial_matches_with_term_found_count'}\")\n", "    \n", "    # Save to Excel with all columns\n", "    try:\n", "        df_for_excel.to_excel(output_file, index=False)\n", "        print(f\"✓ Successfully saved {len(df_for_excel)} rows and {len(df_for_excel.columns)} columns\")\n", "    except Exception as e:\n", "        print(f\"✗ Error saving Excel file: {e}\")\n", "        raise\n", "    \n", "    # Save metrics\n", "    metrics_file = output_file.replace('.xlsx', '_partial_metrics.json')\n", "    try:\n", "        with open(metrics_file, 'w') as f:\n", "            json.dump(metrics, f, indent=2)\n", "        print(f\"✓ Metrics saved to: {metrics_file}\")\n", "    except Exception as e:\n", "        print(f\"✗ Error saving metrics: {e}\")\n", "\n", "\n", "def process_existing_excel_file(input_file: str, output_file: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Process existing Excel file with term validation, row filtering, and partial matches\n", "    \"\"\"\n", "    print(\"=\"*80)\n", "    print(\"PROCESSING WITH TERM VALIDATION AND PARTIAL MATCHES FILTERING\")\n", "    print(\"=\"*80)\n", "    \n", "    # Load existing results\n", "    print(f\"Loading existing results from: {input_file}\")\n", "    try:\n", "        df = pd.read_excel(input_file)\n", "    except Exception as e:\n", "        print(f\"Error loading file: {e}\")\n", "        raise\n", "    \n", "    print(f\"✓ Loaded {len(df)} records with {len(df.columns)} columns\")\n", "    \n", "    # Validate required columns\n", "    validate_required_columns(df)\n", "    \n", "    # Validate term_found and filter rows BEFORE processing\n", "    df_filtered = validate_and_filter_rows(df)\n", "    \n", "    if len(df_filtered) == 0:\n", "        print(\"✗ ERROR: No valid rows remaining after filtering!\")\n", "        return pd.DataFrame()\n", "    \n", "    # Process partial matches on filtered data\n", "    df_updated = process_partial_matches(df_filtered)\n", "    \n", "    # **NEW STEP**: Add partial_matches_with_term_found column\n", "    df_final = add_partial_matches_with_term_found_column(df_updated)\n", "    \n", "    # Final verification\n", "    print(f\"\\nFinal verification:\")\n", "    print(f\"  Input rows: {len(df)}\")\n", "    print(f\"  Filtered rows: {len(df_filtered)}\")\n", "    print(f\"  Final output rows: {len(df_final)}\")\n", "    print(f\"  Final output columns: {len(df_final.columns)}\")\n", "    \n", "    # Calculate metrics (updated to include new column)\n", "    metrics = calculate_metrics(df_final)\n", "    \n", "    # Print metrics summary\n", "    print_metrics_summary(metrics)\n", "    \n", "    # Save results (include new columns in list conversion)\n", "    save_results_updated(df_final, output_file, metrics)\n", "    \n", "    return df_final\n", "\n", "def test_term_validation() -> None:\n", "    \"\"\"Test the term validation logic\"\"\"\n", "    print(\"TESTING TERM VALIDATION LOGIC\")\n", "    print(\"=\"*50)\n", "    \n", "    # Test cases\n", "    test_cases = [\n", "        {\n", "            'sentence': \"The synthesized adsorbents underwent characterization using X-ray diffraction (XRD)\",\n", "            'terms': ['adsorbents', 'X-ray diffraction', 'XRD', 'characterization', 'missing-term'],\n", "            'expected': ['adsorbents', 'x ray diffraction', 'xrd', 'characterization']\n", "        },\n", "        {\n", "            'sentence': \"3D printing technology demonstrates excellent performance\",\n", "            'terms': ['3D printing', 'printing', 'technology', 'performance', 'missing'],\n", "            'expected': ['3d printing', 'printing', 'technology', 'performance']\n", "        }\n", "    ]\n", "    \n", "    for i, case in enumerate(test_cases):\n", "        print(f\"\\nTest case {i+1}:\")\n", "        print(f\"Sentence: {case['sentence'][:80]}...\")\n", "        print(f\"Input terms: {case['terms']}\")\n", "        \n", "        validated = validate_terms_in_sentence(case['sentence'], case['terms'])\n", "        print(f\"Validated terms: {validated}\")\n", "        \n", "        # Check individual terms\n", "        for term in case['terms']:\n", "            found = is_term_in_sentence(term, case['sentence'])\n", "            print(f\"  '{term}' -> {'✓' if found else '✗'}\")\n", "\n", "def test_partial_matching() -> None:\n", "    \"\"\"Test the partial matching logic with examples\"\"\"\n", "    print(\"TESTING PARTIAL MATCHING LOGIC\")\n", "    print(\"=\"*50)\n", "    \n", "    # Test case 1: \"printing\" should match \"3D printing\"\n", "    predicted = ['3d printing', 'inks', 'mxene']\n", "    is_partial = is_partial_match('printing', predicted)\n", "    print(f\"Is 'printing' partially in {predicted}? {is_partial}\")\n", "    \n", "    # Test case 2: \"stems\" should match items in term_found\n", "    term_found = ['3D printing', 'inks', 'printing', 'stems', 'MXene']\n", "    is_partial_noise = is_partial_match('stems', term_found)\n", "    print(f\"Is 'stems' partially in {term_found}? {is_partial_noise}\")\n", "    \n", "    # Test case 3: \"3D printing\" should match \"printing\"\n", "    predicted2 = ['printing material', 'electrodes']\n", "    is_partial_3d = is_partial_match('3D printing', predicted2)\n", "    print(f\"Is '3D printing' partially in {predicted2}? {is_partial_3d}\")\n", "\n", "def test_recalculation_logic() -> None:\n", "    \"\"\"Test the recalculation logic\"\"\"\n", "    print(\"TESTING RECALCULATION LOGIC\")\n", "    print(\"=\"*50)\n", "    \n", "    # Test case\n", "    validated_terms = ['3D printing', 'inks', 'MXene']\n", "    predicted_terms = ['3d printing', 'inks', 'stems', 'electronic']\n", "    \n", "    result = recalculate_matches(validated_terms, predicted_terms)\n", "    \n", "    print(f\"Validated terms: {validated_terms}\")\n", "    print(f\"Predicted terms: {predicted_terms}\")\n", "    print(f\"Result: {result}\")\n", "    \n", "    # Expected:\n", "    # Matched: ['3d printing', 'inks'] (found in both)\n", "    # Noise: ['stems', 'electronic'] (in predicted but not in validated)\n", "    # Missed: ['MXene'] (in validated but not in predicted)\n", "\n", "def main():\n", "    \"\"\"Main execution function\"\"\"\n", "    # Test the validation logic first\n", "    test_term_validation()\n", "    \n", "    print(\"\\n\" + \"=\"*50)\n", "    \n", "    # Test the partial matching logic\n", "    test_partial_matching()\n", "    \n", "    print(\"\\n\" + \"=\"*50)\n", "    \n", "    # Test the recalculation logic\n", "    test_recalculation_logic()\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    \n", "    # Process actual Excel file\n", "    try:\n", "        # INPUT_FILE = \"test_results_improved_evaluation.xlsx\"   \n", "        # OUTPUT_FILE = \"test_results_with_partial_matches-3.xlsx\"\n", "\n", "        # INPUT_FILE = \"test_results_improved_evaluation_scibert.xlsx\"\n", "        INPUT_FILE = \"test_results_improved_evaluation_scibert_model_v2.xlsx\"\n", "        OUTPUT_FILE = INPUT_FILE.replace('.xlsx', '_partial_matches.xlsx')\n", "        \n", "        df_updated = process_existing_excel_file(INPUT_FILE, OUTPUT_FILE)\n", "        \n", "        print(f\"\\n\" + \"=\"*80)\n", "        print(f\"✓ PROCESSING COMPLETED SUCCESSFULLY!\")\n", "        print(f\"✓ Updated results saved to: '{OUTPUT_FILE}'\")\n", "        print(f\"✓ All original data preserved with {len(df_updated.columns)} columns\")\n", "        print(f\"✓ Complete recalculation performed after term validation\")\n", "        print(\"=\"*80)\n", "        \n", "        # Show a sample of the critical columns to verify preservation\n", "        critical_cols = ['sentence', 'term_found', 'predicted_keyphrases', 'matched_keyphrases', 'partial_match_count']\n", "        available_cols = [col for col in critical_cols if col in df_updated.columns]\n", "        \n", "        if available_cols:\n", "            print(f\"\\nSample of preserved critical columns:\")\n", "            sample_df = df_updated[available_cols].head(3)\n", "            for col in sample_df.columns:\n", "                if col == 'sentence':\n", "                    sample_df[col] = sample_df[col].astype(str).str[:100] + '...'  # Truncate for display\n", "            print(sample_df.to_string(index=False))\n", "        \n", "    except FileNotFoundError:\n", "        print(f\"✗ ERROR: File '{INPUT_FILE}' not found.\")\n", "        print(\"Please ensure the input file path is correct.\")\n", "    except Exception as e:\n", "        print(f\"✗ ERROR: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "2a3b3ffe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a00e4856", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "chemenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}